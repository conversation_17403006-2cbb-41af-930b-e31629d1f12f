# 会员/积分系统架构设计

## 项目概述

基于 AI 女友聊天应用的会员制收费模式，实现会员等级管理、积分消费、权限控制的完整业务系统。

### 积分有效期机制 🆕

**核心原则：积分与会员绑定**

- **周期重置**：积分随用户个人会员周期重置（非自然月）
- **到期清零**：会员到期时积分同步清零，避免囤积
- **社交功能**：支持会员间积分赠送，促进用户互动
- **商业模式**：类似运营商套餐，促进每月消费，保持商业健康

## 系统架构

### 整体架构设计（v2.0 - 配置数据库化）

```
┌─────────────────── 前端展示层 ───────────────────┐
│  会员中心 │ 积分商城 │ 权限控制 │ 支付界面 │ 管理后台 │
├─────────────────── 业务逻辑层 ───────────────────┤
│  会员服务 │ 积分服务 │ 权限服务 │ 支付服务 │ 配置服务 │
├─────────────────── 配置管理层 ───────────────────┤
│  动态积分配置 │ 会员等级配置 │ 缓存管理 │ 后台管理  │
├─────────────────── 数据访问层 ───────────────────┤
│  会员管理 │ 积分管理 │ 交易记录 │ 系统配置 │ 统计数据 │
├─────────────────── 外部服务层 ───────────────────┤
│  支付宝API │ 微信支付 │ AI服务 │ 存储服务         │
└─────────────────────────────────────────────────┘
```

### v2.0 新增特性

- **动态配置管理**：积分消费从硬编码改为数据库配置
- **管理后台支持**：可在线调整积分价格和会员等级
- **配置缓存机制**：10 分钟缓存提升性能
- **初始化支持**：首次部署自动初始化默认配置

### 核心服务模块

#### 1. 权限控制服务 (PermissionService)

- 实时检查用户会员状态
- 功能访问权限验证
- 使用次数限制控制
- 中间件拦截验证

#### 2. 积分管理服务 (PointsService)

- 积分余额查询
- 积分扣除/增加
- 积分交易记录
- 积分套餐管理

#### 3. 会员管理服务 (MembershipService)

- 会员状态管理
- 订阅生命周期
- 自动续费处理
- 等级权益管理

#### 4. 支付服务 (PaymentService)

- 统一支付接口
- 支付回调处理
- 订单状态管理
- 退款处理

#### 5. 配置管理服务 (PointsConfigManager) 🆕

- 动态积分配置管理
- 会员等级配置管理
- 配置缓存机制
- 管理后台支持

#### 6. 积分周期管理服务 (PointsCycleManager) 🆕

- 个人会员周期追踪
- 积分自动重置机制
- 会员升级积分补差处理
- 积分赠送社交功能

## 扣费规则设计

### 扣费时机

```typescript
enum ChargeTiming {
  BEFORE_SERVICE = 'before', // 服务前扣费（推荐）
  AFTER_SERVICE = 'after', // 服务后扣费
  ON_SUCCESS = 'success' // 成功后扣费
}

// 具体业务扣费时机
const CHARGE_RULES = {
  text_chat: ChargeTiming.BEFORE_SERVICE, // 文本对话：发送前检查权限
  voice_generation: ChargeTiming.BEFORE_SERVICE, // 语音生成：生成前扣费
  image_generation: ChargeTiming.BEFORE_SERVICE, // 图片生成：生成前扣费
  script_purchase: ChargeTiming.BEFORE_SERVICE, // 剧本购买：购买时扣费
  character_creation: ChargeTiming.ON_SUCCESS // 角色创建：创建成功后扣费
}
```

### 积分消费配置（v2.0 动态配置）

```typescript
// v1.0 硬编码配置（已弃用）
// const POINTS_COST = { ... }

// v2.0 数据库动态配置
const pointsCost = await getFeaturePointsCost(env, 'IMAGE_GENERATION')

// 默认积分配置（后备配置）
const DEFAULT_POINTS_CONFIG = {
  IMAGE_GENERATION: 10, // 图片生成：10积分/张
  VOICE_GENERATION: 5, // 语音生成：5积分/次
  SCRIPT_PURCHASE: 50, // 剧本购买：50积分/个
  GALLERY_GENERATION: 15, // 写真集生成：15积分/次
  VIDEO_GENERATION: 20 // 视频生成：20积分/次（预留）
}

// 管理后台可动态调整这些配置
```

## 积分生命周期管理 🆕

### 积分周期设计

#### 个人会员周期模式

- **周期起点**：用户开通会员的确切日期时间
- **周期长度**：30 天（按实际天数计算）
- **重置时机**：每个用户独立的 30 天周期到期时
- **示例**：用户 A 在 3 月 15 日开通 Pro 会员，积分在 4 月 14 日重置

#### 会员升级处理机制

- **支付计算**：按剩余天数比例补差额
- **积分补差**：(目标积分 - 当前积分) × (剩余天数/30)
- **权益生效**：立即享受升级后的全部权益
- **周期保持**：升级不改变原有到期时间

#### 积分赠送社交功能

- **赠送限制**：每日最多赠送 100 积分，每月 500 积分
- **接收条件**：仅限有效会员之间赠送
- **有效期**：赠送积分 30 天有效期
- **社交增强**：好友系统、赠送记录、感谢机制

### 技术实现方案

#### 定时任务架构

- **主力方案**：Cloudflare Cron Triggers（付费版）
- **执行频率**：每小时检查积分到期用户
- **备用机制**：用户操作时实时检查个人积分状态
- **双重保障**：定时处理 + 实时检查确保准确性

#### 数据存储设计

```typescript
interface UserPointsCycle {
  userId: string
  currentBalance: number // 当前积分余额
  cycleStartDate: Date // 当前周期开始时间
  cycleEndDate: Date // 当前周期结束时间
  membershipLevel: string // 当前会员等级
  monthlyAllocation: number // 月度分配积分
  consumed: number // 本周期已消费
  gifted: number // 本周期已赠送
  received: number // 本周期已接收
}
```

## 开发计划

### 阶段一：核心权限系统 (优先级：P0)

#### 后端开发

- [x] 1.1 权限检查中间件
  - [x] 用户会员状态验证
  - [x] 功能访问权限检查
  - [x] 使用次数限制验证
- [x] 1.2 会员状态服务
  - [x] 获取用户当前会员信息
  - [x] 检查会员是否过期
  - [x] 会员权益配置管理
- [x] 1.3 积分余额检查
  - [x] 积分余额查询接口
  - [x] 积分充足性验证
  - [x] 积分预扣除机制
- [x] 1.4 基础会员管理 API
  - [x] 会员套餐列表
  - [x] 用户订阅状态查询
  - [x] 会员信息更新

#### 前端开发

- [x] 1.5 权限控制组件 ✅
  - [x] 功能权限验证 HOC (`PermissionGuard`, `withPermission`)
  - [x] 会员状态显示组件 (`PermissionModal`)
  - [x] 权限不足提示组件 (`PermissionToast`)
- [x] 1.6 会员中心页面 ✅
  - [x] 会员信息展示 (`MembershipPage`)
  - [x] 套餐对比页面 (已集成到会员页面)
  - [x] 订阅状态管理 (使用真实 API 数据)

### 阶段二：积分系统 (优先级：P0)

#### 后端开发

- [x] 2.1 积分核心服务
  - [x] 积分扣除/增加服务
  - [x] 积分交易事务处理
  - [x] 积分余额实时更新
- [x] 2.2 积分交易记录
  - [x] 积分消费记录
  - [x] 积分获得记录
  - [x] 交易历史查询
- [x] 2.3 积分套餐管理
  - [x] 积分包配置
  - [ ] 积分包购买处理
  - [ ] 积分包优惠活动
- [x] 2.4 动态积分配置 🆕
  - [x] 数据库配置存储
  - [x] 配置缓存机制
  - [x] 配置管理 API

#### 前端开发

- [ ] 2.5 积分商城
  - [ ] 积分套餐展示
  - [ ] 积分购买流程
  - [ ] 积分使用记录
- [ ] 2.6 积分余额组件
  - [ ] 积分余额显示
  - [ ] 积分变动提醒
  - [ ] 积分不足提示
- [ ] 2.7 管理后台界面 🆕
  - [ ] 积分配置管理页面
  - [ ] 配置修改界面
  - [ ] 配置历史查看

### 阶段三：会员订阅系统 (优先级：P1)

#### 后端开发

- [x] 3.1 订阅管理服务
  - [x] 会员套餐管理
  - [x] 订阅状态管理
  - [x] 订阅历史记录
- [x] 3.2 自动处理机制
  - [x] 会员到期检查
  - [ ] 自动续费处理
  - [ ] 积分月度发放
- [x] 3.3 套餐升级降级
  - [x] 套餐变更处理
  - [x] 积分差额计算
  - [x] 变更生效时间

#### 前端开发

- [x] 3.4 订阅管理页面
  - [x] 套餐选择界面
  - [x] 订阅状态展示
  - [x] 套餐变更操作
- [x] 3.5 订阅历史
  - [x] 订阅记录查询
  - [ ] 支付记录展示
  - [ ] 发票下载功能(不做)

### 阶段四：支付系统集成 (优先级：P2)

#### 支付接入

- [ ] 4.1 支付宝集成
  - [ ] 支付宝 SDK 集成
  - [ ] 支付订单创建
  - [ ] 支付回调处理
- [ ] 4.2 微信支付集成
  - [ ] 微信支付 SDK 集成
  - [ ] 微信支付流程
  - [ ] 支付状态同步
- [ ] 4.3 订单管理系统
  - [ ] 订单创建和状态管理
  - [ ] 支付超时处理
  - [ ] 订单查询接口
- [ ] 4.4 退款处理
  - [ ] 退款申请流程
  - [ ] 退款状态跟踪
  - [ ] 退款通知机制

#### 前端支付

- [ ] 4.5 支付界面
  - [ ] 支付方式选择
  - [ ] 支付进度展示
  - [ ] 支付结果页面
- [ ] 4.6 订单管理
  - [ ] 订单列表展示
  - [ ] 订单详情查看
  - [ ] 支付重试功能

### 阶段五：积分生命周期系统 (优先级：P1) 🆕

**预计时间：1-2 周**

#### 后端开发

- [ ] 5.1 积分周期管理核心
  - [ ] 用户积分周期数据模型
  - [ ] 个人会员周期计算逻辑
  - [ ] 积分自动重置机制
  - [x] 会员升级积分补差处理
- [ ] 5.2 Cloudflare 定时任务
  - [ ] Cron Triggers 配置
  - [ ] 批量积分到期检查
  - [ ] 积分重置执行逻辑
  - [ ] 执行结果日志记录
- [ ] 5.3 积分赠送社交功能（暂不做）
  - [ ] 积分赠送 API 接口
  - [ ] 赠送限制和验证
  - [ ] 赠送记录和历史查询
  - [ ] 好友关系管理（可选）
- [ ] 5.4 实时积分状态检查
  - [ ] 用户操作时周期检查
  - [ ] 过期积分自动处理
  - [ ] 积分余额实时更新

#### 前端开发

- [ ] 5.5 积分周期显示
  - [ ] 积分余额和周期信息
  - [ ] 积分到期倒计时
  - [ ] 本周期消费统计
- [ ] 5.6 积分赠送界面（暂不做）
  - [ ] 好友选择和搜索
  - [ ] 赠送积分表单
  - [ ] 赠送记录查看
- [ ] 5.7 会员升级积分提示
  - [ ] 升级后积分变化预览
  - [ ] 积分补差说明
  - [ ] 升级确认界面

### 阶段六：管理后台系统 (优先级：P2) 🆕

#### 后端开发

- [x] 6.1 配置管理 API
  - [x] 积分配置管理
  - [x] 会员等级配置
  - [x] 配置初始化接口
  - [x] 配置状态报告
- [ ] 6.2 管理权限系统
  - [ ] 管理员角色验证
  - [ ] 操作权限控制
  - [ ] 操作日志记录
- [ ] 6.3 数据统计 API
  - [ ] 积分消费统计
  - [ ] 会员转化分析
  - [ ] 收入报表接口

#### 前端开发

- [ ] 6.4 管理后台界面
  - [ ] 积分配置管理页面
  - [ ] 会员等级管理页面
  - [ ] 配置修改表单
- [ ] 6.5 数据统计页面
  - [ ] 积分消费图表
  - [ ] 会员增长趋势
  - [ ] 收入统计看板

### 阶段七：监控与优化 (优先级：P3)

- [x] 7.1 配置缓存优化
  - [x] 积分配置缓存（10 分钟）
  - [x] 会员状态缓存（5 分钟）
  - [ ] Redis 缓存支持
- [ ] 7.2 数据统计
  - [ ] 会员转化率统计
  - [ ] 积分消费分析
  - [ ] 收入统计报表
- [ ] 7.3 性能优化
  - [ ] 权限检查优化
  - [ ] 数据库查询优化
  - [ ] API 响应时间优化
- [ ] 7.4 监控告警
  - [ ] 支付异常监控
  - [ ] 会员状态异常
  - [ ] 积分异常消费

## 技术实现

### 支付方案

采用**先模拟支付，预留真实接口**的策略：

```typescript
// 支付服务接口设计
interface PaymentService {
  createOrder(orderInfo: OrderInfo): Promise<PaymentResult>
  verifyPayment(paymentId: string): Promise<VerificationResult>
  processCallback(callbackData: any): Promise<CallbackResult>
}

// 环境配置切换
const paymentService =
  process.env.NODE_ENV === 'production' ? new RealPaymentService() : new MockPaymentService()
```

### 配置管理方案 🆕

采用**数据库动态配置 + 缓存优化**的策略：

```typescript
// 配置管理器
class PointsConfigManager {
  // 从数据库获取配置（带缓存）
  async getPointsCost(feature: string): Promise<number>

  // 管理后台更新配置
  async updatePointsCost(feature: string, cost: number): Promise<boolean>

  // 配置缓存管理
  private cache = new Map() // 10分钟缓存
}

// 业务代码使用
const cost = await getFeaturePointsCost(env, 'IMAGE_GENERATION')
```

### 数据库设计

基于现有 schema，主要涉及表：

- `MembershipPlan` - 会员套餐
- `UserSubscription` - 用户订阅
- `SubscriptionHistory` - 订阅历史
- `PointsPackage` - 积分套餐
- `UserPoints` - 用户积分
- `PointsTransaction` - 积分交易记录
- `SystemConfig` - 系统配置（积分价格、会员等级配置）🆕

### API 设计

```typescript
// 权限验证API
POST /api/membership/check-permission
GET /api/membership/verify-access/:feature

// 会员管理API
GET /api/membership/plans
GET /api/membership/subscription
POST /api/membership/subscription
GET /api/membership/status

// 积分管理API
GET /api/membership/points
GET /api/membership/points/transactions
POST /api/membership/consume-points
GET /api/membership/features/config

// 管理后台API 🆕
PUT /api/admin/config/points-cost
PUT /api/admin/config/points-costs
GET /api/admin/config/points-costs
POST /api/admin/config/reset-points-costs
POST /api/admin/config/initialize
GET /api/admin/config/report

// 支付API（预留）
POST /api/payment/create-order
POST /api/payment/callback
GET /api/payment/status
```

## 开发进度跟踪

### 当前状态：阶段二完成，配置系统升级 (v2.0)

- [x] 业务需求分析
- [x] 系统架构设计
- [x] 扣费规则制定
- [x] 开发计划制定
- [x] 技术方案确定

#### 已完成项目（2024-06-20 v3.0）

**🎉 核心升级 v3.0：积分生命周期设计**

- [x] 积分与会员绑定模式确定
- [x] 个人会员周期设计（非自然月）
- [x] 会员升级积分补差机制
- [x] 积分赠送社交功能设计
- [x] Cloudflare 定时任务技术方案

**🎉 核心升级 v2.0：配置数据库化**

- [x] 积分配置从硬编码改为数据库动态配置
- [x] 管理后台配置管理 API
- [x] 配置缓存机制（10 分钟缓存）
- [x] TTS 和图片生成服务积分集成

**阶段一：核心权限系统 ✅**

- [x] 权限检查中间件 (`/src/middleware/permission.ts`)
- [x] 会员状态服务 (`/src/lib/membership/service.ts`)
- [x] 积分余额检查服务 (`/src/lib/membership/points.ts`)
- [x] 权限验证 API 接口扩展 (`/src/routes/membership.ts`)

**阶段二：积分系统 ✅**

- [x] 积分核心服务和交易记录
- [x] 积分套餐配置管理
- [x] 积分消费/添加 API
- [x] 动态积分配置系统 (`/src/lib/membership/config.ts`) 🆕
- [x] 管理后台配置 API (`/src/routes/admin-config.ts`) 🆕

**阶段三：会员订阅系统 🔄 (部分完成)**

- [x] 基础订阅管理服务
- [x] 会员到期检查机制

**阶段五：管理后台系统 🔄 (后端完成)**

- [x] 配置管理 API 完整实现
- [x] 积分配置初始化支持

**核心 API 接口:**

- [x] POST `/api/membership/check-permission` - 检查用户权限
- [x] GET `/api/membership/verify-access/:feature` - 验证功能访问权限
- [x] POST `/api/membership/consume-points` - 消费积分
- [x] GET `/api/membership/features/config` - 获取功能配置（动态）

**管理后台 API 接口:** 🆕

- [x] PUT `/api/admin/config/points-cost` - 更新单个积分配置
- [x] PUT `/api/admin/config/points-costs` - 批量更新积分配置
- [x] GET `/api/admin/config/points-costs` - 获取配置管理视图
- [x] POST `/api/admin/config/reset-points-costs` - 重置为默认配置
- [x] POST `/api/admin/config/initialize` - 初始化配置
- [x] GET `/api/admin/config/report` - 配置状态报告

### 下一步行动 (优先级排序)

1. **P0 - 积分周期数据模型**：设计和实现用户积分周期存储结构
2. **P0 - 个人周期计算逻辑**：实现基于用户开通日期的 30 天周期算法
3. **P1 - Cloudflare 定时任务**：配置 Cron Triggers 进行批量积分重置
4. **P1 - 实时积分检查**：用户操作时检查个人积分状态
5. **P2 - 会员升级补差**：实现升级时的积分补差逻辑
6. **P2 - 积分赠送功能**：开发社交赠送机制
7. **P3 - 前端积分周期界面**：显示积分余额、周期信息、到期倒计时

### v3.0 架构优势

- ✅ **积分生命周期**：个人会员周期管理，避免积分无限囤积
- ✅ **商业模式健康**：积分与会员绑定，促进持续付费
- ✅ **用户体验优化**：30 天完整使用期，公平合理
- ✅ **社交功能扩展**：积分赠送增加用户粘性和互动
- ✅ **技术架构简洁**：Cloudflare 生态一体化，维护成本低
- ✅ **升级机制完善**：会员升级积分补差，用户权益保护
- ✅ **配置灵活性**：管理员可随时调整积分价格，无需代码部署
- ✅ **数据完整性**：所有积分变动完整记录到数据库

## 风险与注意事项

### 技术风险

- 支付接口对接可能存在调试困难
- 积分扣费的事务一致性需要保证
- 会员权限缓存可能存在延迟

### 业务风险

- 会员定价策略需要市场验证
- 积分消费规则需要持续优化
- 用户体验需要平衡付费和免费功能

### 安全考虑

- 支付数据加密传输
- 积分操作防止重复扣费
- 会员信息安全存储
- 配置管理需要管理员权限验证

## 使用示例

### 1. 系统初始化

```bash
# 首次部署时初始化积分配置
curl -X POST /api/admin/config/initialize \
  -H "Authorization: Bearer admin-token"
```

### 2. 在服务中集成积分扣除

```typescript
// TTS服务中使用
import { createServicePointsManager } from '@/lib/membership/service-points'

async function generateTTS(userId: string, text: string) {
  const pointsManager = createServicePointsManager(env)

  // 扣除积分
  const result = await pointsManager.consumeTTSPoints(userId, {
    textLength: text.length,
    taskId: `tts-${Date.now()}`
  })

  if (!result.success) {
    throw new Error(result.error)
  }

  // 执行TTS生成...
  return { audioUrl: '...', pointsConsumed: result.remainingPoints }
}
```

### 3. 管理后台调整配置

```bash
# 调整图片生成积分价格
curl -X PUT /api/admin/config/points-cost \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin-token" \
  -d '{"feature": "IMAGE_GENERATION", "cost": 15}'

# 获取配置状态报告
curl -X GET /api/admin/config/report \
  -H "Authorization: Bearer admin-token"
```

### 4. 前端权限检查

```typescript
// 检查用户是否有权限生成图片
const response = await fetch('/api/membership/verify-access/IMAGE_GENERATION')
const { data } = await response.json()

if (!data.canAccess) {
  showError(data.reason)
  return
}

// 执行图片生成...
```

## 最新更新 (v3.0+)

### ✅ 已完成功能

#### 阶段五：积分生命周期系统 (COMPLETED)

- **✅ 数据模型扩展**: userPoints 表增加周期管理字段
- **✅ 个人周期管理**: 基于用户注册的 30 天周期计算
- **✅ 自动重置机制**: Cloudflare Cron 定时任务每日凌晨 2 点执行
- **✅ 会员升级补差**: 按剩余天数比例计算积分补偿
- **✅ 实时周期检查**: 积分消费时自动检测并处理过期
- **✅ API 接口**: 完整的周期管理 REST API

#### 定时任务配置

```toml
# wrangler.toml
[triggers]
crons = ["0 2 * * *"]  # 每天凌晨2点执行积分周期检查
```

#### 核心功能

- **积分周期管理器**: PointsCycleManager 类提供完整周期管理功能
- **批量处理**: 定时任务批量处理所有过期用户
- **实时检查**: 用户操作时实时检查周期状态
- **错误处理**: 完善的错误日志和异常处理机制

### 🔄 当前开发状态

- **高优先级任务**: 全部完成 ✅
- **中优先级任务**: 全部完成 ✅
- **低优先级任务**: 待完成
  - 数据库初始化积分配置
  - 测试积分扣除功能

### 📋 下一步计划

#### 🎯 当前已完成 (2024-06-21 v5.0)

- [x] **权限守卫系统集成**: 完成前端权限控制组件与现有功能的全面集成

  - [x] 角色创建器图片生成权限保护
  - [x] 聊天图片生成权限验证
  - [x] 实时流式音频播放器权限控制
  - [x] 流式音频播放器权限集成
  - [x] TypeScript 错误修复 (PermissionToast, index.ts)

- [x] **模拟支付系统完成**: 实现完整的会员订阅支付流程
  - [x] 后端模拟支付服务 (MockPaymentService)
  - [x] 支付 API 路由 (/api/payment/\*)
  - [x] 支付成功后会员激活逻辑
  - [x] 积分自动发放机制
  - [x] 前端支付页面和流程
  - [x] 会员页面支付跳转集成

#### 🚀 下一步优先级任务

1. **P0 - 支付流程测试** - 测试完整的支付 → 会员激活 → 积分发放流程
2. **P0 - 权限系统测试** - 验证会员获得积分后的权限控制
3. **P1 - 积分扣除验证** - 测试图片生成和语音生成的积分消费
4. **P1 - 前端积分显示** - 更新前端界面显示积分余额和周期信息
5. **P2 - 生产部署** - 配置生产环境支付和 Cron 触发器
