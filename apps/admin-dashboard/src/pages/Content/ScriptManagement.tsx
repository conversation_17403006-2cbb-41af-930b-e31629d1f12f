import React, { useState, useEffect } from 'react'
import { Typography, Modal, message } from 'antd'
import { DEFAULT_PAGE_SIZE } from '@/constants'
import { scriptService } from '@/services/script'
import type {
  Script,
  ScriptListParams,
  ScriptCreateParams,
  ScriptStats as ScriptStatsType
} from '@/types/script'
import { ScriptEditor } from '@/pages/Content'
import { ScriptStats, ScriptSearch, ScriptTable, ScriptForm } from './components'

const { Title } = Typography

const ScriptManagement: React.FC = () => {
  // 基础状态
  const [scripts, setScripts] = useState<Script[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)

  // 模态框状态
  const [modalVisible, setModalVisible] = useState(false)
  const [editingScript, setEditingScript] = useState<Script | null>(null)
  const [contentEditorVisible, setContentEditorVisible] = useState(false)

  // 搜索条件
  const [searchParams, setSearchParams] = useState<ScriptListParams>({
    page: 1,
    pageSize: DEFAULT_PAGE_SIZE
  })

  // 统计数据
  const [stats, setStats] = useState<ScriptStatsType>({
    totalScripts: 0,
    publicScripts: 0,
    privateScripts: 0,
    activeScripts: 0,
    premiumScripts: 0,
    totalUsage: 0,
    averageRating: 0
  })

  // 分类选项
  const [categories, setCategories] = useState<string[]>([])

  useEffect(() => {
    loadScripts()
    loadStats()
    loadCategories()
  }, [currentPage, pageSize])

  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1)
    } else {
      loadScripts()
    }
  }, [searchParams])

  // 数据加载方法
  const loadScripts = async () => {
    try {
      setLoading(true)

      const params = {
        page: currentPage,
        pageSize,
        ...searchParams
      }

      const response = await scriptService.getScripts(params)

      if (response.success && response.data) {
        setScripts(response.data.data)
        setTotal(response.data.total)
      } else {
        message.error(response.message || '获取剧本列表失败')
      }
      setLoading(false)
    } catch (error) {
      console.error('获取剧本列表失败:', error)
      message.error('获取剧本列表失败')
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await scriptService.getStats()

      if (response.success && response.data) {
        setStats(response.data)
      } else {
        console.error('获取统计数据失败:', response.message)
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  const loadCategories = async () => {
    try {
      const response = await scriptService.getCategories()

      if (response.success && response.data) {
        setCategories(response.data)
      } else {
        console.error('获取分类失败:', response.message)
      }
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  }

  // 事件处理方法
  const handleSearch = () => {
    setCurrentPage(1)
    setSearchParams({
      ...searchParams,
      page: 1
    })
  }

  const handleReset = () => {
    setSearchParams({
      page: 1,
      pageSize: DEFAULT_PAGE_SIZE
    })
    setCurrentPage(1)
  }

  const handleCreate = () => {
    setEditingScript(null)
    setModalVisible(true)
  }

  const handleEdit = async (script: Script) => {
    try {
      setLoading(true)
      // 获取完整的剧本数据包括 content
      const response = await scriptService.getScript(script.id)

      if (response.success && response.data) {
        const fullScript = response.data
        setEditingScript(fullScript)
        setModalVisible(true)
      } else {
        message.error(response.message || '获取剧本详情失败')
      }
      setLoading(false)
    } catch (error) {
      console.error('获取剧本详情失败:', error)
      message.error('获取剧本详情失败')
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await scriptService.deleteScript(id)

      if (response.success) {
        message.success('删除成功')
        loadScripts()
        loadStats()
      } else {
        message.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  const handleToggleStatus = async (id: string, field: 'isActive' | 'isPublic', value: boolean) => {
    try {
      let response
      if (field === 'isActive') {
        response = await scriptService.toggleScriptStatus(id, value)
      } else {
        response = await scriptService.toggleScriptPublic(id, value)
      }

      if (response.success) {
        message.success(response.message || '状态更新成功')
        loadScripts()
        loadStats()
      } else {
        message.error(response.message || '状态更新失败')
      }
    } catch (error) {
      console.error('状态更新失败:', error)
      message.error('状态更新失败')
    }
  }

  const handleEditContent = async (script: Script) => {
    try {
      setLoading(true)
      const response = await scriptService.getScript(script.id)
      if (response.success && response.data) {
        setEditingScript(response.data)
        setContentEditorVisible(true)
      } else {
        message.error(response.message || '获取剧本详情失败')
      }
      setLoading(false)
    } catch (error) {
      console.error('获取剧本详情失败:', error)
      message.error('获取剧本详情失败')
      setLoading(false)
    }
  }

  const handleSubmit = async (values: Record<string, unknown>) => {
    try {
      // 处理 coverImage 字段，确保它是字符串URL而不是对象
      let coverImageUrl = ''
      if (values.coverImage) {
        if (typeof values.coverImage === 'string') {
          coverImageUrl = values.coverImage
        } else if (typeof values.coverImage === 'object' && values.coverImage !== null) {
          // 如果是对象，尝试提取URL
          const imgObj = values.coverImage as Record<string, unknown>
          // 处理嵌套的数据结构，如 { success: true, data: { url: "..." } }
          if (imgObj.success && imgObj.data && typeof imgObj.data === 'object') {
            const dataObj = imgObj.data as Record<string, unknown>
            coverImageUrl = String(dataObj.url || '')
          } else {
            // 处理直接的URL对象，如 { url: "...", response: {...} }
            coverImageUrl = String(imgObj.url || imgObj.response || imgObj.thumbUrl || '')
          }
        }
      }

      const submitData: ScriptCreateParams = {
        title: String(values.title || ''),
        description: String(values.description || ''),
        coverImage: coverImageUrl,
        category: String(values.category || ''),
        // 确保 duration 是字符串类型
        duration: String(values.duration || ''),
        tags: Array.isArray(values.tags) ? values.tags.map(tag => String(tag).trim()) : [],
        content: editingScript?.content || { commands: [], stages: [] },
        audioUrl: values.audioUrl ? String(values.audioUrl) : undefined,
        isPublic: Boolean(values.isPublic),
        isPremium: Boolean(values.isPremium),
        pointsCost: Number(values.pointsCost) || 0
      }

      let response
      if (editingScript) {
        response = await scriptService.updateScript({
          id: editingScript.id,
          ...submitData
        })
      } else {
        response = await scriptService.createScript(submitData)
      }

      if (response.success) {
        message.success(response.message || (editingScript ? '更新成功' : '创建成功'))
        setModalVisible(false)
        loadScripts()
        loadStats()
      } else {
        message.error(response.message || '操作失败')
      }
    } catch (error) {
      console.error('操作失败:', error)
      message.error('操作失败')
    }
  }

  const handlePageChange = (page: number, size: number) => {
    setCurrentPage(page)
    setPageSize(size)
  }

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        剧本管理
      </Title>

      {/* 统计卡片 */}
      <ScriptStats stats={stats} />

      {/* 搜索和筛选 */}
      <ScriptSearch
        searchParams={searchParams}
        categories={categories}
        onSearchParamsChange={setSearchParams}
        onSearch={handleSearch}
        onReset={handleReset}
        onCreate={handleCreate}
      />

      {/* 剧本列表 */}
      <ScriptTable
        scripts={scripts}
        loading={loading}
        total={total}
        currentPage={currentPage}
        pageSize={pageSize}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onToggleStatus={handleToggleStatus}
        onEditContent={handleEditContent}
        onPageChange={handlePageChange}
      />

      {/* 新增/编辑剧本表单 */}
      <ScriptForm
        visible={modalVisible}
        editingScript={editingScript}
        categories={categories}
        onCancel={() => setModalVisible(false)}
        onSubmit={handleSubmit}
        onCategoriesChange={setCategories}
        onEditContent={() => {
          setModalVisible(false)
          setContentEditorVisible(true)
        }}
      />

      {/* 剧本内容编辑器 */}
      <Modal
        title="编辑剧本内容"
        open={contentEditorVisible}
        onCancel={() => setContentEditorVisible(false)}
        width="90%"
        style={{ top: 20 }}
        footer={null}
        destroyOnClose
      >
        {editingScript && (
          <ScriptEditor
            scriptId={editingScript.id}
            initialContent={editingScript.content}
            onSave={async content => {
              try {
                const response = await scriptService.updateScriptContent(editingScript.id, content)
                if (response.success) {
                  message.success('剧本内容保存成功')
                  setContentEditorVisible(false)
                  // 更新当前编辑的剧本对象
                  setEditingScript({
                    ...editingScript,
                    content
                  })
                } else {
                  message.error(response.message || '保存失败')
                }
              } catch (error) {
                console.error('保存失败:', error)
                message.error('保存失败')
              }
            }}
            onCancel={() => setContentEditorVisible(false)}
          />
        )}
      </Modal>
    </div>
  )
}

export default ScriptManagement
