import React, { useState, useEffect } from 'react'
import { scriptService } from '@/services/script'
import type { ScriptContent, Command, Stage } from '@/types/script'
import { Typography, Button, Space, Tabs, message } from 'antd'
import { EyeOutlined, SaveOutlined } from '@ant-design/icons'
import { CommandManager, StageManager, ScriptPreview } from './components'

const { Title } = Typography

interface ScriptEditorProps {
  scriptId?: string
  initialContent?: ScriptContent
  onSave?: (content: ScriptContent) => void
  onCancel?: () => void
}

const ScriptEditor: React.FC<ScriptEditorProps> = ({
  scriptId,
  initialContent,
  onSave,
  onCancel
}) => {
  const [content, setContent] = useState<ScriptContent>(
    initialContent || { commands: [], stages: [] }
  )
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('commands')
  const [previewVisible, setPreviewVisible] = useState(false)

  useEffect(() => {
    if (scriptId) {
      loadScriptContent()
    }
  }, [scriptId])

  const loadScriptContent = async () => {
    if (!scriptId) return

    try {
      setLoading(true)
      const response = await scriptService.getScriptContent(scriptId)

      if (response.success && response.data) {
        setContent(response.data)
      } else {
        message.error(response.message || '加载剧本内容失败')
      }
      setLoading(false)
    } catch (error) {
      console.error('加载剧本内容失败:', error)
      message.error('加载剧本内容失败')
      setLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setLoading(true)

      // 如果有scriptId，直接调用API保存到服务器
      if (scriptId) {
        const response = await scriptService.updateScriptContent(scriptId, content)

        if (response.success) {
          message.success('剧本内容保存成功')
        } else {
          message.error(response.message || '保存失败')
          return
        }
      }

      // 如果有onSave回调，也调用它（用于父组件处理）
      if (onSave) {
        onSave(content)
      }

      setLoading(false)
    } catch (error) {
      console.error('保存失败:', error)
      message.error('保存失败')
      setLoading(false)
    }
  }

  const handleCommandsChange = (commands: Command[]) => {
    setContent({ ...content, commands })
  }

  const handleStagesChange = (stages: Stage[]) => {
    setContent({ ...content, stages })
  }

  return (
    <div style={{ padding: 24 }}>
      {/* 头部操作栏 */}
      <div
        style={{
          marginBottom: 24,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <Title level={3}>剧本内容编辑器</Title>
        <Space>
          <Button icon={<EyeOutlined />} onClick={() => setPreviewVisible(true)}>
            预览
          </Button>
          <Button type="primary" icon={<SaveOutlined />} loading={loading} onClick={handleSave}>
            保存
          </Button>
          {onCancel && <Button onClick={onCancel}>取消</Button>}
        </Space>
      </div>

      {/* 主要内容区域 */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'commands',
            label: `指令管理 (${content.commands.length})`,
            children: (
              <CommandManager commands={content.commands} onCommandsChange={handleCommandsChange} />
            )
          },
          {
            key: 'stages',
            label: `阶段管理 (${content.stages.length})`,
            children: <StageManager stages={content.stages} onStagesChange={handleStagesChange} />
          }
        ]}
      />

      {/* 预览模态框 */}
      <ScriptPreview
        visible={previewVisible}
        content={content}
        onCancel={() => setPreviewVisible(false)}
      />
    </div>
  )
}

export default ScriptEditor
