import React from 'react'
import { Table, Card, Button, Space, Tag, Image, Tooltip, Popconfirm, Badge } from 'antd'
import {
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StarOutlined,
  UserOutlined,
  ClockCircleOutlined,
  FileTextOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { TABLE_CONFIG } from '@/constants'

import type { Script } from '@/types/script'
import dayjs from 'dayjs'
import { getTimeDescription } from '@/utils/time'

// 格式化duration显示 - 确保显示为MM:SS格式
const formatDurationDisplay = (duration: string | number | undefined): string => {
  if (!duration) return '00:00'

  const durationStr = String(duration)

  // 如果已经是MM:SS格式，直接返回
  if (/^\d{1,2}:\d{2}$/.test(durationStr)) {
    return durationStr
  }

  // 如果是纯数字（秒数），转换为MM:SS格式
  const seconds = parseInt(durationStr)
  if (!isNaN(seconds) && seconds >= 0) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 其他情况返回原值
  return durationStr
}

interface ScriptTableProps {
  scripts: Script[]
  loading: boolean
  total: number
  currentPage: number
  pageSize: number
  onEdit: (script: Script) => void
  onDelete: (id: string) => void
  onToggleStatus: (id: string, field: 'isActive' | 'isPublic', value: boolean) => void
  onEditContent: (script: Script) => void
  onPageChange: (page: number, size: number) => void
}

const ScriptTable: React.FC<ScriptTableProps> = ({
  scripts,
  loading,
  total,
  currentPage,
  pageSize,
  onEdit,
  onDelete,
  onToggleStatus,
  onEditContent,
  onPageChange
}) => {
  const columns: ColumnsType<Script> = [
    {
      title: '封面',
      dataIndex: 'coverImage',
      width: 80,
      render: coverImage => (
        <Image
          src={coverImage}
          width={60}
          height={60}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG8XccyMnFMTuAd3BlOAC6HjgcMXFOKHVsgA1RSkGEGjGhFwSF4D2Q4IBcAPaEaGaVMrsQYGNnZfr4lNcm2"
        />
      )
    },
    {
      title: '剧本信息',
      key: 'info',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{record.title}</div>
          <div style={{ color: '#666', fontSize: '12px', marginBottom: 4 }}>
            {record.description.length > 50
              ? `${record.description.substring(0, 50)}...`
              : record.description}
          </div>
          <Space size="small">
            <Tooltip title={getTimeDescription(record.duration)}>
              <Tag color="blue" icon={<ClockCircleOutlined />}>
                {formatDurationDisplay(record.duration)}
              </Tag>
            </Tooltip>
            <Tag color="green">{record.stageCount}阶段</Tag>
            <Tag color="purple">{record.category}</Tag>
          </Space>
        </div>
      )
    },
    {
      title: '标签',
      dataIndex: 'tags',
      width: 150,
      render: (tags: string[]) => (
        <div>
          {tags.map(tag => (
            <Tag key={tag} style={{ margin: '2px', fontSize: '12px' }}>
              {tag}
            </Tag>
          ))}
        </div>
      )
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <Badge
              status={record.isActive ? 'success' : 'error'}
              text={record.isActive ? '启用' : '禁用'}
            />
          </div>
          <div style={{ marginBottom: 4 }}>
            <Badge
              status={record.isPublic ? 'processing' : 'default'}
              text={record.isPublic ? '公开' : '私有'}
            />
          </div>
          {record.isPremium && (
            <Tag color="gold" style={{ fontSize: '12px' }}>
              会员专享
            </Tag>
          )}
        </div>
      )
    },
    {
      title: '统计',
      key: 'stats',
      width: 120,
      render: (_, record) => (
        <div style={{ fontSize: '12px' }}>
          <div>
            <UserOutlined /> {record.usageCount}次使用
          </div>
          <div style={{ marginTop: 4 }}>
            <StarOutlined /> {record.rating.toFixed(1)}({record.ratingCount}评分)
          </div>
          {record.pointsCost > 0 && (
            <div style={{ marginTop: 4, color: '#f50' }}>{record.pointsCost} 积分</div>
          )}
        </div>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 120,
      render: date => dayjs(date).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 250,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="link" icon={<EyeOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              icon={<EditOutlined />}
              size="small"
              onClick={() => onEdit(record)}
            />
          </Tooltip>
          <Tooltip title="编辑内容">
            <Button
              type="link"
              icon={<FileTextOutlined />}
              size="small"
              onClick={() => onEditContent(record)}
            />
          </Tooltip>
          <Tooltip title={record.isActive ? '禁用' : '启用'}>
            <Button
              type="link"
              icon={record.isActive ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              size="small"
              onClick={() => onToggleStatus(record.id, 'isActive', !record.isActive)}
            />
          </Tooltip>
          <Popconfirm title="确定删除这个剧本吗？" onConfirm={() => onDelete(record.id)}>
            <Tooltip title="删除">
              <Button type="link" danger icon={<DeleteOutlined />} size="small" />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <Card>
      <Table
        columns={columns}
        dataSource={scripts}
        rowKey="id"
        loading={loading}
        pagination={{
          current: currentPage,
          pageSize,
          total,
          onChange: onPageChange,
          ...TABLE_CONFIG
        }}
      />
    </Card>
  )
}

export default ScriptTable
