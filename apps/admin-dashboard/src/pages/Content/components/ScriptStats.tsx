import React from 'react'
import { Row, <PERSON>, Card, Statistic } from 'antd'
import { PlayCircleOutlined } from '@ant-design/icons'
import type { ScriptStats } from '@/types/script'

interface ScriptStatsProps {
  stats: ScriptStats
}

const ScriptStatsComponent: React.FC<ScriptStatsProps> = ({ stats }) => {
  return (
    <Row gutter={16} style={{ marginBottom: 16 }}>
      <Col span={4}>
        <Card>
          <Statistic
            title="总剧本数"
            value={stats.totalScripts}
            prefix={<PlayCircleOutlined />}
            valueStyle={{ color: '#3f8600' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic
            title="公开剧本"
            value={stats.publicScripts}
            valueStyle={{ color: '#1890ff' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic
            title="启用剧本"
            value={stats.activeScripts}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic
            title="会员专享"
            value={stats.premiumScripts}
            valueStyle={{ color: '#722ed1' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic
            title="总使用次数"
            value={stats.totalUsage}
            valueStyle={{ color: '#fa8c16' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic
            title="平均评分"
            value={stats.averageRating}
            precision={1}
            suffix="/ 5.0"
            valueStyle={{ color: '#eb2f96' }}
          />
        </Card>
      </Col>
    </Row>
  )
}

export default ScriptStatsComponent
