import React, { useState } from 'react'
import {
  Card,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  message
} from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, SoundOutlined } from '@ant-design/icons'

const { Text } = Typography

interface Intensity {
  thrust: number
  suction: number
  vibrate: number
}

interface IntensityManagerProps {
  intensity: Record<string, Intensity>
  onIntensityChange: (intensity: Record<string, Intensity>) => void
}

const IntensityManager: React.FC<IntensityManagerProps> = ({ intensity, onIntensityChange }) => {
  const [modalVisible, setModalVisible] = useState(false)
  const [editingTime, setEditingTime] = useState<string>('')
  const [form] = Form.useForm()

  // 解析时间字符串为秒数，用于排序
  const parseTime = (timeStr: string): number => {
    const parts = timeStr.split(':')
    return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2])
  }

  const handleAdd = () => {
    setEditingTime('')
    form.resetFields()
    setModalVisible(true)
  }

  const handleEdit = (time: string) => {
    const intensityData = intensity[time]
    setEditingTime(time)
    form.setFieldsValue({ time, ...intensityData })
    setModalVisible(true)
  }

  const handleDelete = (time: string) => {
    const newIntensity = { ...intensity }
    delete newIntensity[time]
    onIntensityChange(newIntensity)
    message.success('强度设置删除成功')
  }

  const handleSubmit = (values: {
    time: string
    thrust: number
    suction: number
    vibrate: number
  }) => {
    const { time, thrust, suction, vibrate } = values
    const newIntensity = { ...intensity }

    // 如果时间改变了，删除旧的
    if (editingTime && editingTime !== time) {
      delete newIntensity[editingTime]
    }

    newIntensity[time] = { thrust, suction, vibrate }
    onIntensityChange(newIntensity)
    setModalVisible(false)
    message.success(editingTime ? '强度设置更新成功' : '强度设置添加成功')
  }

  return (
    <Card
      size="small"
      title={
        <>
          <SoundOutlined /> 强度设置
        </>
      }
    >
      <div style={{ marginBottom: 8 }}>
        <Button size="small" icon={<PlusOutlined />} onClick={handleAdd}>
          添加强度点
        </Button>
      </div>

      {Object.entries(intensity)
        .sort(([a], [b]) => parseTime(a) - parseTime(b))
        .map(([time, intensityData]) => (
          <div
            key={time}
            style={{
              marginBottom: 8,
              padding: 8,
              border: '1px solid #f0f0f0',
              borderRadius: 4
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}
            >
              <Space>
                <Tag color="blue">{time}</Tag>
                <Text style={{ fontSize: '12px' }}>
                  推力:{intensityData.thrust} 吸力:{intensityData.suction} 震动:
                  {intensityData.vibrate}
                </Text>
              </Space>
              <Space>
                <Button
                  type="link"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(time)}
                />
                <Button
                  type="link"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDelete(time)}
                />
              </Space>
            </div>
          </div>
        ))}

      {Object.keys(intensity).length === 0 && <Text type="secondary">暂无强度设置</Text>}

      {/* 强度设置模态框 */}
      <Modal
        title="设置强度"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="time"
            label="时间点"
            rules={[
              { required: true, message: '请输入时间点' },
              {
                pattern: /^\d{2}:\d{2}:\d{2}$/,
                message: '请输入正确的时间格式 (HH:MM:SS)'
              }
            ]}
          >
            <Input placeholder="格式: 00:01:30" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="thrust"
                label="推力强度"
                rules={[{ required: true, message: '请设置推力强度' }]}
                initialValue={0}
              >
                <InputNumber min={-1} max={9} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="suction"
                label="吸力强度"
                rules={[{ required: true, message: '请设置吸力强度' }]}
                initialValue={0}
              >
                <InputNumber min={-1} max={9} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="vibrate"
                label="震动强度"
                rules={[{ required: true, message: '请设置震动强度' }]}
                initialValue={0}
              >
                <InputNumber min={-1} max={9} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <div style={{ fontSize: '12px', color: '#999', marginBottom: 16 }}>
            强度范围: -1(停止) 0(最低) 1-9(强度等级，数值越大强度越高)
          </div>
          <Form.Item style={{ marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  )
}

export default IntensityManager
