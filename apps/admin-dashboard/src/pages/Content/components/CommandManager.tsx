import React, { useState } from 'react'
import {
  Card,
  Button,
  List,
  Modal,
  Form,
  Input,
  Space,
  Tag,
  Tooltip,
  Popconfirm,
  message,
  Upload,
  Divider,
  Alert
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  DownloadOutlined,
  UploadOutlined,
  ExportOutlined
} from '@ant-design/icons'
import type { Command } from '@/types/script'
import { downloadCommandTemplate, parseCommandExcel, exportCommandsToExcel } from '@/utils/excel'

interface CommandManagerProps {
  commands: Command[]
  onCommandsChange: (commands: Command[]) => void
}

const CommandManager: React.FC<CommandManagerProps> = ({ commands, onCommandsChange }) => {
  const [modalVisible, setModalVisible] = useState(false)
  const [editingCommand, setEditingCommand] = useState<{ index: number; command: Command } | null>(
    null
  )
  const [form] = Form.useForm()
  const [importing, setImporting] = useState(false)

  const handleAdd = () => {
    setEditingCommand(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEdit = (index: number) => {
    const command = commands[index]
    setEditingCommand({ index, command })
    form.setFieldsValue(command)
    setModalVisible(true)
  }

  const handleDelete = (index: number) => {
    const newCommands = [...commands]
    newCommands.splice(index, 1)
    onCommandsChange(newCommands)
    message.success('指令删除成功')
  }

  const handleSubmit = (values: Command) => {
    const newCommands = [...commands]
    if (editingCommand) {
      newCommands[editingCommand.index] = values
    } else {
      newCommands.push(values)
    }

    // 按时间排序
    newCommands.sort((a, b) => {
      const timeA = a.time.split(':').reduce((acc, time) => 60 * acc + +time, 0)
      const timeB = b.time.split(':').reduce((acc, time) => 60 * acc + +time, 0)
      return timeA - timeB
    })

    onCommandsChange(newCommands)
    setModalVisible(false)
    message.success(editingCommand ? '指令更新成功' : '指令添加成功')
  }

  // 下载Excel模板
  const handleDownloadTemplate = () => {
    try {
      downloadCommandTemplate()
      message.success('模板下载成功')
    } catch (error) {
      console.error('下载模板失败:', error)
      message.error('下载模板失败')
    }
  }

  // 导出当前指令为Excel
  const handleExportCommands = () => {
    if (commands.length === 0) {
      message.warning('没有指令可导出')
      return
    }

    try {
      exportCommandsToExcel(commands)
      message.success('指令导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 处理Excel文件导入
  const handleImportExcel = async (file: File) => {
    try {
      setImporting(true)
      const result = await parseCommandExcel(file)

      if (result.success && result.data) {
        const newCommands: Command[] = result.data

        onCommandsChange(newCommands)
        message.success(`成功导入 ${newCommands.length} 个指令`)
      } else {
        // 显示详细的错误信息
        const errors = result.errors || ['导入失败']

        if (errors.length === 1) {
          message.error(errors[0])
        } else {
          // 多个错误时使用Modal显示
          Modal.error({
            title: '导入失败',
            content: (
              <div>
                <p>发现以下错误：</p>
                <ul style={{ maxHeight: '300px', overflow: 'auto' }}>
                  {errors.map((error, index) => (
                    <li key={index} style={{ marginBottom: '4px' }}>
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            ),
            width: 600
          })
        }
      }
    } catch (error) {
      console.error('导入失败:', error)
      message.error('导入失败')
    } finally {
      setImporting(false)
    }

    return false // 阻止默认上传行为
  }

  return (
    <Card>
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            添加指令
          </Button>
          <Divider type="vertical" />
          <Button icon={<DownloadOutlined />} onClick={handleDownloadTemplate}>
            下载模板
          </Button>
          <Upload accept=".xlsx,.xls" beforeUpload={handleImportExcel} showUploadList={false}>
            <Button icon={<UploadOutlined />} loading={importing}>
              导入Excel
            </Button>
          </Upload>
          {commands.length > 0 && (
            <Button icon={<ExportOutlined />} onClick={handleExportCommands}>
              导出Excel
            </Button>
          )}
        </Space>
      </div>

      {commands.length > 0 && (
        <Alert
          message="Excel功能说明"
          description="可以下载模板填写指令数据后批量导入，或将当前指令导出为Excel文件。模板包含时间(秒)和指令内容两列。导入时会替换所有现有指令。"
          type="info"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      <List
        dataSource={commands}
        renderItem={(command, index) => (
          <List.Item
            actions={[
              <Tooltip title="编辑" key="edit">
                <Button type="link" icon={<EditOutlined />} onClick={() => handleEdit(index)} />
              </Tooltip>,
              <Popconfirm
                key="delete"
                title="确定删除这个指令吗？"
                onConfirm={() => handleDelete(index)}
              >
                <Tooltip title="删除">
                  <Button type="link" danger icon={<DeleteOutlined />} />
                </Tooltip>
              </Popconfirm>
            ]}
          >
            <List.Item.Meta
              avatar={
                <Tag color="blue" icon={<ClockCircleOutlined />}>
                  {command.time}
                </Tag>
              }
              title={command.command}
            />
          </List.Item>
        )}
      />

      {/* 指令编辑模态框 */}
      <Modal
        title={editingCommand ? '编辑指令' : '添加指令'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="time"
            label="时间点"
            rules={[
              { required: true, message: '请输入时间点' },
              {
                pattern: /^\d{2}:\d{2}:\d{2}$/,
                message: '请输入正确的时间格式 (HH:MM:SS)'
              }
            ]}
          >
            <Input placeholder="格式: 00:01:30" />
          </Form.Item>
          <Form.Item
            name="command"
            label="指令内容"
            rules={[{ required: true, message: '请输入指令内容' }]}
          >
            <Input placeholder="请输入指令内容" />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  )
}

export default CommandManager
