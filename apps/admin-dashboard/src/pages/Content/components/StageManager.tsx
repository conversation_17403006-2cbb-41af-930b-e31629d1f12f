import React, { useState } from 'react'
import {
  Card,
  Button,
  Collapse,
  Modal,
  Form,
  Input,
  InputNumber,
  Space,
  Tag,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  message,
  Upload,
  Divider,
  Alert
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined,
  ExportOutlined
} from '@ant-design/icons'
import type { Stage, Picture } from '@/types/script'
import PictureManager from './PictureManager'
import IntensityManager from './IntensityManager'
import { downloadStageTemplate, parseStageExcel, exportStagesToExcel } from '@/utils/excel'

const { Panel } = Collapse

interface StageManagerProps {
  stages: Stage[]
  onStagesChange: (stages: Stage[]) => void
}

const StageManager: React.FC<StageManagerProps> = ({ stages, onStagesChange }) => {
  const [modalVisible, setModalVisible] = useState(false)
  const [editingStage, setEditingStage] = useState<{ index: number; stage: Stage } | null>(null)
  const [form] = Form.useForm()
  const [importing, setImporting] = useState(false)

  const handleAdd = () => {
    setEditingStage(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEdit = (index: number) => {
    const stage = stages[index]
    setEditingStage({ index, stage })
    form.setFieldsValue({
      stage: stage.stage,
      stageTitle: stage.stageTitle
    })
    setModalVisible(true)
  }

  const handleDelete = (index: number) => {
    const newStages = [...stages]
    newStages.splice(index, 1)
    // 重新编号
    newStages.forEach((stage, i) => {
      stage.stage = i + 1
    })
    onStagesChange(newStages)
    message.success('阶段删除成功')
  }

  const handleSubmit = (values: { stage: number; stageTitle: string }) => {
    const newStages = [...stages]
    if (editingStage) {
      newStages[editingStage.index] = {
        ...newStages[editingStage.index],
        ...values
      }
    } else {
      newStages.push({
        ...values,
        stage: newStages.length + 1,
        pics: [],
        intensity: {}
      })
    }
    onStagesChange(newStages)
    setModalVisible(false)
    message.success(editingStage ? '阶段更新成功' : '阶段添加成功')
  }

  // 更新阶段的图片
  const handlePicturesChange = (stageIndex: number, pictures: Picture[]) => {
    const newStages = [...stages]
    newStages[stageIndex].pics = pictures
    onStagesChange(newStages)
  }

  // 更新阶段的强度设置
  const handleIntensityChange = (
    stageIndex: number,
    intensity: Record<string, { thrust: number; suction: number; vibrate: number }>
  ) => {
    const newStages = [...stages]
    newStages[stageIndex].intensity = intensity
    onStagesChange(newStages)
  }

  // 下载Excel模板
  const handleDownloadTemplate = () => {
    try {
      downloadStageTemplate()
      message.success('阶段模板下载成功')
    } catch (error) {
      console.error('下载模板失败:', error)
      message.error('下载模板失败')
    }
  }

  // 导出当前阶段为Excel
  const handleExportStages = () => {
    if (stages.length === 0) {
      message.warning('没有阶段可导出')
      return
    }

    try {
      exportStagesToExcel(stages)
      message.success('阶段导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 处理Excel文件导入
  const handleImportExcel = async (file: File) => {
    try {
      setImporting(true)
      const result = await parseStageExcel(file)

      if (result.success && result.data) {
        const newStages: Stage[] = result.data

        onStagesChange(newStages)
        message.success(`成功导入 ${newStages.length} 个阶段`)
      } else {
        // 显示详细的错误信息
        const errors = result.errors || ['导入失败']

        if (errors.length === 1) {
          message.error(errors[0])
        } else {
          // 多个错误时使用Modal显示
          Modal.error({
            title: '导入失败',
            content: (
              <div>
                <p>发现以下错误：</p>
                <ul style={{ maxHeight: '300px', overflow: 'auto' }}>
                  {errors.map((error, index) => (
                    <li key={index} style={{ marginBottom: '4px' }}>
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            ),
            width: 600
          })
        }
      }
    } catch (error) {
      console.error('导入失败:', error)
      message.error('导入失败')
    } finally {
      setImporting(false)
    }

    return false // 阻止默认上传行为
  }

  return (
    <Card>
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            添加阶段
          </Button>
          <Divider type="vertical" />
          <Button icon={<DownloadOutlined />} onClick={handleDownloadTemplate}>
            下载模板
          </Button>
          <Upload accept=".xlsx,.xls" beforeUpload={handleImportExcel} showUploadList={false}>
            <Button icon={<UploadOutlined />} loading={importing}>
              导入Excel
            </Button>
          </Upload>
          {stages.length > 0 && (
            <Button icon={<ExportOutlined />} onClick={handleExportStages}>
              导出Excel
            </Button>
          )}
        </Space>
      </div>

      {stages.length > 0 && (
        <Alert
          message="Excel功能说明"
          description="可以下载模板填写阶段数据后批量导入，或将当前阶段导出为Excel文件。模板包含阶段编号、标题、强度时间点、强度值和图片名称。导入时会替换所有现有阶段。图片URL需要后续手动上传。"
          type="info"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      <Collapse>
        {stages.map((stage, stageIndex) => (
          <Panel
            header={
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <span>
                  阶段 {stage.stage}: {stage.stageTitle}
                </span>
                <Space>
                  <Tag color="green">{stage.pics.length} 图片</Tag>
                  <Tag color="orange">{Object.keys(stage.intensity).length} 强度点</Tag>
                </Space>
              </div>
            }
            key={stageIndex}
            extra={
              <Space onClick={e => e.stopPropagation()}>
                <Tooltip title="编辑阶段">
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => handleEdit(stageIndex)}
                  />
                </Tooltip>
                <Popconfirm title="确定删除这个阶段吗？" onConfirm={() => handleDelete(stageIndex)}>
                  <Tooltip title="删除阶段">
                    <Button type="link" size="small" danger icon={<DeleteOutlined />} />
                  </Tooltip>
                </Popconfirm>
              </Space>
            }
          >
            <Row gutter={16}>
              <Col span={12}>
                <PictureManager
                  pictures={stage.pics}
                  onPicturesChange={pictures => handlePicturesChange(stageIndex, pictures)}
                />
              </Col>
              <Col span={12}>
                <IntensityManager
                  intensity={stage.intensity}
                  onIntensityChange={intensity => handleIntensityChange(stageIndex, intensity)}
                />
              </Col>
            </Row>
          </Panel>
        ))}
      </Collapse>

      {stages.length === 0 && (
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
          暂无阶段，点击上方按钮添加阶段
        </div>
      )}

      {/* 阶段编辑模态框 */}
      <Modal
        title={editingStage ? '编辑阶段' : '添加阶段'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="stage"
            label="阶段编号"
            rules={[{ required: true, message: '请输入阶段编号' }]}
            initialValue={stages.length + 1}
          >
            <InputNumber min={1} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="stageTitle"
            label="阶段标题"
            rules={[{ required: true, message: '请输入阶段标题' }]}
          >
            <Input placeholder="请输入阶段标题" />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  )
}

export default StageManager
