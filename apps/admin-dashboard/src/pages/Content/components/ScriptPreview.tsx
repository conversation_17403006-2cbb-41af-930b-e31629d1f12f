import React from 'react'
import { Mo<PERSON>, Card, Tag, Typography, Row, Col, Image } from 'antd'
import type { ScriptContent } from '@/types/script'

const { Title, Text } = Typography

interface ScriptPreviewProps {
  visible: boolean
  content: ScriptContent
  onCancel: () => void
}

const ScriptPreview: React.FC<ScriptPreviewProps> = ({ visible, content, onCancel }) => {
  // 解析时间字符串为秒数，用于排序
  const parseTime = (timeStr: string): number => {
    const parts = timeStr.split(':')
    return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2])
  }

  return (
    <Modal title="剧本预览" open={visible} onCancel={onCancel} width="80%" footer={null}>
      <div style={{ maxHeight: '70vh', overflow: 'auto' }}>
        {/* 指令时间轴 */}
        <div style={{ marginBottom: 24 }}>
          <Title level={4}>指令时间轴 ({content.commands.length} 个指令)</Title>
          <div style={{ padding: 16, backgroundColor: '#f5f5f5', borderRadius: 8 }}>
            {content.commands.length > 0 ? (
              content.commands
                .sort((a, b) => {
                  const timeA = a.time.split(':').reduce((acc, time) => 60 * acc + +time, 0)
                  const timeB = b.time.split(':').reduce((acc, time) => 60 * acc + +time, 0)
                  return timeA - timeB
                })
                .map((command, index) => (
                  <div key={index} style={{ marginBottom: 8 }}>
                    <Tag color="blue">{command.time}</Tag>
                    <span style={{ marginLeft: 8 }}>{command.command}</span>
                  </div>
                ))
            ) : (
              <Text type="secondary">暂无指令</Text>
            )}
          </div>
        </div>

        {/* 剧本阶段 */}
        <div>
          <Title level={4}>剧本阶段 ({content.stages.length} 个阶段)</Title>
          {content.stages.length > 0 ? (
            content.stages.map((stage, index) => (
              <Card key={index} style={{ marginBottom: 16 }} size="small">
                <div style={{ marginBottom: 12 }}>
                  <Title level={5}>
                    阶段 {stage.stage}: {stage.stageTitle}
                  </Title>
                </div>

                <Row gutter={16}>
                  {/* 图片展示 */}
                  <Col span={12}>
                    <div style={{ marginBottom: 12 }}>
                      <Text strong>图片 ({stage.pics.length})</Text>
                    </div>
                    {stage.pics.length > 0 ? (
                      <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                        {stage.pics.map((pic, picIndex) => (
                          <div key={picIndex} style={{ textAlign: 'center' }}>
                            <Image
                              src={pic.pic}
                              width={80}
                              height={80}
                              style={{ objectFit: 'cover', borderRadius: 4 }}
                              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6U"
                            />
                            <div style={{ fontSize: '12px', marginTop: 4 }}>{pic.name}</div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <Text type="secondary">暂无图片</Text>
                    )}
                  </Col>

                  {/* 强度设置展示 */}
                  <Col span={12}>
                    <div style={{ marginBottom: 12 }}>
                      <Text strong>强度设置 ({Object.keys(stage.intensity).length})</Text>
                    </div>
                    {Object.keys(stage.intensity).length > 0 ? (
                      Object.entries(stage.intensity)
                        .sort(([a], [b]) => parseTime(a) - parseTime(b))
                        .map(([time, intensity]) => (
                          <div key={time} style={{ marginBottom: 8 }}>
                            <Tag color="blue">{time}</Tag>
                            <span style={{ fontSize: '12px', marginLeft: 8 }}>
                              推力:{intensity.thrust} 吸力:{intensity.suction} 震动:
                              {intensity.vibrate}
                            </span>
                          </div>
                        ))
                    ) : (
                      <Text type="secondary">暂无强度设置</Text>
                    )}
                  </Col>
                </Row>
              </Card>
            ))
          ) : (
            <Text type="secondary">暂无阶段</Text>
          )}
        </div>
      </div>
    </Modal>
  )
}

export default ScriptPreview
