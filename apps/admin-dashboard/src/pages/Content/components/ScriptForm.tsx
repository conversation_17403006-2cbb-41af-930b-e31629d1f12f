import React, { useState, useEffect } from 'react'
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  InputNumber,
  Switch,
  Upload,
  message
} from 'antd'
import { UploadOutlined, PlusOutlined, ReloadOutlined, EditOutlined } from '@ant-design/icons'
import type { UploadFile } from 'antd/es/upload/interface'
import { scriptService } from '@/services/script'
import type { Script } from '@/types/script'
import TagInput from '@/components/TagInput'
import { isValidTimeFormat, formatTimeString } from '@/utils/time'

const { TextArea } = Input

// 格式化duration为表单可用的格式
const formatDurationForForm = (duration: string | number | undefined): string => {
  if (!duration) return ''

  const durationStr = String(duration)

  // 如果已经是MM:SS格式，直接返回
  if (/^\d{1,2}:\d{2}$/.test(durationStr)) {
    return durationStr
  }

  // 如果是纯数字（秒数），转换为MM:SS格式
  const seconds = parseInt(durationStr)
  if (!isNaN(seconds) && seconds >= 0) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // 其他情况返回原值
  return durationStr
}

interface ScriptFormProps {
  visible: boolean
  editingScript: Script | null
  categories: string[]
  onCancel: () => void
  onSubmit: (values: Record<string, unknown>) => void
  onCategoriesChange: (categories: string[]) => void
  onEditContent?: () => void
}

const ScriptForm: React.FC<ScriptFormProps> = ({
  visible,
  editingScript,
  categories,
  onCancel,
  onSubmit,
  onCategoriesChange,
  onEditContent
}) => {
  const [form] = Form.useForm()
  const [newCategory, setNewCategory] = useState<string>('')
  const [coverImageList, setCoverImageList] = useState<UploadFile[]>([])
  const [uploadingCover, setUploadingCover] = useState(false)
  const [audioFileList, setAudioFileList] = useState<UploadFile[]>([])
  const [uploadingAudio, setUploadingAudio] = useState(false)

  // 处理封面图片上传
  const handleCoverUpload = async (file: File): Promise<boolean> => {
    try {
      setUploadingCover(true)
      const response = await scriptService.uploadCoverImage(file)

      if (response.success && response.data?.url) {
        // 更新表单中的 coverImage 字段
        form.setFieldValue('coverImage', response.data.url)

        // 更新上传列表显示
        setCoverImageList([
          {
            uid: file.name,
            name: file.name,
            status: 'done',
            url: response.data.url
          }
        ])

        message.success('封面上传成功')
        return true
      } else {
        message.error(response.message || '上传失败')
        return false
      }
    } catch (error) {
      console.error('封面上传失败:', error)
      message.error('上传失败')
      return false
    } finally {
      setUploadingCover(false)
    }
  }

  // 处理音频文件上传
  const handleAudioUpload = async (file: File): Promise<boolean> => {
    try {
      setUploadingAudio(true)
      const response = await scriptService.uploadAudioFile(file)

      if (response.success && response.data?.url) {
        // 更新表单中的 audioUrl 字段
        form.setFieldValue('audioUrl', response.data.url)

        // 更新上传列表显示
        setAudioFileList([
          {
            uid: file.name,
            name: file.name,
            status: 'done',
            url: response.data.url
          }
        ])

        message.success('音频上传成功')
        return true
      } else {
        message.error(response.message || '音频上传失败')
        return false
      }
    } catch (error) {
      console.error('音频上传失败:', error)
      message.error('音频上传失败')
      return false
    } finally {
      setUploadingAudio(false)
    }
  }

  // 重置表单状态
  const resetFormState = () => {
    form.resetFields()
    setCoverImageList([])
    setAudioFileList([])
    setNewCategory('')
  }

  // 监听编辑剧本变化
  useEffect(() => {
    if (visible) {
      if (editingScript) {
        // 编辑模式 - 设置表单值
        const formValues = {
          ...editingScript,
          tags: editingScript.tags, // TagInput组件直接接受数组
          duration: formatDurationForForm(editingScript.duration) // 确保duration是正确的MM:SS格式
        }

        form.setFieldsValue(formValues)

        // 设置封面图片显示
        if (editingScript.coverImage) {
          setCoverImageList([
            {
              uid: 'cover-image',
              name: 'cover-image',
              status: 'done',
              url: editingScript.coverImage
            }
          ])
        } else {
          setCoverImageList([])
        }

        // 设置音频文件显示
        if (editingScript.audioUrl) {
          setAudioFileList([
            {
              uid: 'audio-file',
              name: 'audio-file',
              status: 'done',
              url: editingScript.audioUrl
            }
          ])
        } else {
          setAudioFileList([])
        }
      } else {
        // 创建模式 - 重置表单
        resetFormState()
      }
    }
  }, [visible, editingScript, form])

  const handleCancel = () => {
    resetFormState()
    onCancel()
  }

  const handleFormSubmit = (values: Record<string, unknown>) => {
    onSubmit(values)
    // 不要立即重置表单，等模态框关闭时再重置
  }

  // 监听模态框关闭，重置表单状态
  useEffect(() => {
    if (!visible && !editingScript) {
      // 模态框关闭且不是编辑模式时重置表单
      resetFormState()
    }
  }, [visible, editingScript])

  return (
    <Modal
      title={editingScript ? '编辑剧本' : '新增剧本'}
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={() => form.submit()}>
          {editingScript ? '更新' : '创建'}
        </Button>
      ]}
    >
      <Form form={form} layout="vertical" onFinish={handleFormSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="title"
              label="剧本标题"
              rules={[{ required: true, message: '请输入剧本标题' }]}
            >
              <Input placeholder="请输入剧本标题" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="category"
              label="剧本分类"
              rules={[{ required: true, message: '请选择剧本分类' }]}
            >
              <Select
                placeholder="请选择剧本分类"
                dropdownRender={menu => (
                  <div>
                    {menu}
                    <div style={{ padding: '8px', borderTop: '1px solid #e8e8e8' }}>
                      <Input
                        placeholder="输入新分类名称"
                        value={newCategory}
                        onChange={e => setNewCategory(e.target.value)}
                        onPressEnter={() => {
                          if (newCategory && !categories.includes(newCategory)) {
                            const newCategories = [...categories, newCategory]
                            onCategoriesChange(newCategories)
                            form.setFieldValue('category', newCategory)
                            setNewCategory('')
                          }
                        }}
                        style={{ marginBottom: 8 }}
                      />
                      <Button
                        type="dashed"
                        block
                        icon={<PlusOutlined />}
                        onClick={() => {
                          if (newCategory && !categories.includes(newCategory)) {
                            const newCategories = [...categories, newCategory]
                            onCategoriesChange(newCategories)
                            form.setFieldValue('category', newCategory)
                            setNewCategory('')
                          }
                        }}
                      >
                        添加新分类
                      </Button>
                    </div>
                  </div>
                )}
              >
                {categories.map(category => (
                  <Select.Option key={category} value={category}>
                    {category}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="description"
          label="剧本描述"
          rules={[{ required: true, message: '请输入剧本描述' }]}
        >
          <TextArea rows={3} placeholder="请输入剧本描述" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="duration"
              label="剧本时长"
              rules={[
                { required: true, message: '请输入剧本时长' },
                {
                  validator: (_, value) => {
                    if (!value) return Promise.resolve()
                    if (!isValidTimeFormat(value)) {
                      return Promise.reject(
                        new Error('请输入正确的时间格式，如：21:11（分钟:秒钟）')
                      )
                    }
                    return Promise.resolve()
                  }
                }
              ]}
            >
              <Input
                placeholder="如：21:11（分钟:秒钟）"
                onBlur={e => {
                  const value = e.target.value
                  if (value && isValidTimeFormat(value)) {
                    const formatted = formatTimeString(value)
                    if (formatted !== value) {
                      form.setFieldValue('duration', formatted)
                    }
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="tags" label="标签" extra="支持回车或逗号添加标签">
              <TagInput placeholder="输入标签后按回车或逗号分隔" maxTags={8} />
            </Form.Item>
          </Col>
        </Row>

        {/* 隐藏的coverImage字段，存储URL字符串 */}
        <Form.Item name="coverImage" style={{ display: 'none' }}>
          <Input />
        </Form.Item>

        {/* Upload组件，不绑定到Form字段 */}
        <Form.Item label="封面图片">
          <Upload
            listType="picture-card"
            maxCount={1}
            fileList={coverImageList}
            beforeUpload={async file => {
              await handleCoverUpload(file)
              return false // 阻止默认上传行为
            }}
            onRemove={() => {
              setCoverImageList([])
              form.setFieldValue('coverImage', '')
            }}
          >
            {coverImageList.length === 0 && (
              <div>
                {uploadingCover ? (
                  <div>
                    <ReloadOutlined spin />
                    <div style={{ marginTop: 8 }}>上传中...</div>
                  </div>
                ) : (
                  <div>
                    <UploadOutlined />
                    <div style={{ marginTop: 8 }}>上传封面</div>
                  </div>
                )}
              </div>
            )}
          </Upload>
        </Form.Item>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item name="pointsCost" label="积分消耗" initialValue={0}>
              <InputNumber min={0} style={{ width: '100%' }} placeholder="免费请输入0" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="isPublic" label="公开状态" valuePropName="checked" initialValue={true}>
              <Switch checkedChildren="公开" unCheckedChildren="私有" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="isPremium"
              label="会员专享"
              valuePropName="checked"
              initialValue={false}
            >
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
        </Row>

        {/* 隐藏的audioUrl字段，存储URL字符串 */}
        <Form.Item name="audioUrl" style={{ display: 'none' }}>
          <Input />
        </Form.Item>

        {/* 音频文件上传组件 */}
        <Form.Item label="音频文件">
          <Upload
            accept="audio/*"
            maxCount={1}
            fileList={audioFileList}
            beforeUpload={async file => {
              await handleAudioUpload(file)
              return false // 阻止默认上传行为
            }}
            onRemove={() => {
              setAudioFileList([])
              form.setFieldValue('audioUrl', '')
            }}
          >
            <Button
              icon={uploadingAudio ? <ReloadOutlined spin /> : <UploadOutlined />}
              disabled={uploadingAudio}
            >
              {uploadingAudio ? '上传中...' : '上传音频文件'}
            </Button>
          </Upload>
          {audioFileList.length > 0 && (
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              已上传：{audioFileList[0].name}
            </div>
          )}
        </Form.Item>

        {editingScript && (
          <Form.Item label="剧本内容" extra="点击按钮编辑剧本的详细内容（指令、阶段、图片等）">
            <Button type="dashed" icon={<EditOutlined />} block onClick={onEditContent}>
              编辑剧本内容
            </Button>
          </Form.Item>
        )}
      </Form>
    </Modal>
  )
}

export default ScriptForm
