import React, { useState } from 'react'
import { Card, Button, Modal, Form, Input, Upload, Image, Space, Typography, message } from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  LoadingOutlined,
  PictureOutlined
} from '@ant-design/icons'
import { scriptService } from '@/services/script'
import type { Picture } from '@/types/script'

const { Text } = Typography

interface PictureManagerProps {
  pictures: Picture[]
  onPicturesChange: (pictures: Picture[]) => void
}

const PictureManager: React.FC<PictureManagerProps> = ({ pictures, onPicturesChange }) => {
  const [modalVisible, setModalVisible] = useState(false)
  const [editingPic, setEditingPic] = useState<{ index: number; pic: Picture } | null>(null)
  const [uploadingImage, setUploadingImage] = useState(false)
  const [form] = Form.useForm()

  const handleAdd = () => {
    setEditingPic(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEdit = (index: number) => {
    const pic = pictures[index]
    setEditingPic({ index, pic })
    form.setFieldsValue(pic)
    setModalVisible(true)
    // 确保预览图片能正确显示
    setTimeout(() => {
      form.validateFields(['pic'])
    }, 100)
  }

  const handleDelete = (index: number) => {
    const newPictures = [...pictures]
    newPictures.splice(index, 1)
    onPicturesChange(newPictures)
    message.success('图片删除成功')
  }

  const handleSubmit = (values: Picture) => {
    // 检查是否已上传图片
    if (!values.pic) {
      message.error('请先上传图片')
      return
    }

    const newPictures = [...pictures]
    if (editingPic) {
      newPictures[editingPic.index] = values
    } else {
      newPictures.push(values)
    }
    onPicturesChange(newPictures)
    setModalVisible(false)
    message.success(editingPic ? '图片更新成功' : '图片添加成功')
  }

  // 处理图片上传
  const handleImageUpload = async (file: File): Promise<boolean> => {
    try {
      setUploadingImage(true)
      const response = await scriptService.uploadStageImage(file)

      if (response.success && response.data?.url) {
        // 更新表单中的图片URL
        form.setFieldValue('pic', response.data.url)
        // 如果没有设置名称，使用文件名
        if (!form.getFieldValue('name')) {
          form.setFieldValue('name', file.name.split('.')[0])
        }
        // 强制重新渲染以显示预览图片
        form.validateFields(['pic'])
        message.success('图片上传成功')
        return true
      } else {
        message.error(response.message || '上传失败')
        return false
      }
    } catch (error) {
      console.error('图片上传失败:', error)
      message.error('上传失败')
      return false
    } finally {
      setUploadingImage(false)
    }
  }

  return (
    <Card
      size="small"
      title={
        <>
          <PictureOutlined /> 图片管理
        </>
      }
    >
      <div style={{ marginBottom: 8 }}>
        <Button size="small" icon={<PlusOutlined />} onClick={handleAdd}>
          添加图片
        </Button>
      </div>

      <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
        {pictures.map((pic, index) => (
          <div key={index} style={{ position: 'relative' }}>
            <Image
              src={pic.pic}
              width={60}
              height={60}
              style={{ objectFit: 'cover', borderRadius: 4 }}
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6U"
            />
            <div style={{ position: 'absolute', top: 2, right: 2 }}>
              <Space size="small">
                <Button
                  type="primary"
                  size="small"
                  icon={<EditOutlined />}
                  style={{ fontSize: '10px', padding: '0 4px' }}
                  onClick={() => handleEdit(index)}
                />
                <Button
                  danger
                  size="small"
                  icon={<DeleteOutlined />}
                  style={{ fontSize: '10px', padding: '0 4px' }}
                  onClick={() => handleDelete(index)}
                />
              </Space>
            </div>
            <Text
              style={{
                fontSize: '10px',
                display: 'block',
                textAlign: 'center'
              }}
            >
              {pic.name}
            </Text>
          </div>
        ))}
      </div>

      {/* 图片编辑模态框 */}
      <Modal
        title={editingPic ? '编辑图片' : '添加图片'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="name"
            label="图片名称"
            rules={[{ required: true, message: '请输入图片名称' }]}
          >
            <Input placeholder="请输入图片名称" />
          </Form.Item>

          {/* 隐藏的URL字段，由上传自动填充 */}
          <Form.Item name="pic" style={{ display: 'none' }}>
            <Input />
          </Form.Item>

          <Form.Item label="上传图片" required rules={[{ required: true, message: '请上传图片' }]}>
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={async file => {
                await handleImageUpload(file)
                return false // 阻止默认上传行为
              }}
              showUploadList={false}
              accept="image/*"
            >
              <div>
                {uploadingImage ? (
                  <div>
                    <LoadingOutlined />
                    <div style={{ marginTop: 8 }}>上传中...</div>
                  </div>
                ) : (
                  <div>
                    <UploadOutlined />
                    <div style={{ marginTop: 8 }}>点击上传图片</div>
                  </div>
                )}
              </div>
            </Upload>

            {/* 显示当前图片预览 */}
            {form.getFieldValue('pic') && (
              <div style={{ marginTop: 8 }}>
                <Image
                  src={form.getFieldValue('pic')}
                  width={100}
                  height={100}
                  style={{ objectFit: 'cover', borderRadius: 4 }}
                  fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6U"
                />
                <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>已上传的图片</div>
              </div>
            )}
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  )
}

export default PictureManager
