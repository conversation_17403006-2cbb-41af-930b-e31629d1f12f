import React from 'react'
import { Card, Space, Input, Select, Button } from 'antd'
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons'
import type { ScriptListParams } from '@/types/script'

interface ScriptSearchProps {
  searchParams: ScriptListParams
  categories: string[]
  onSearchParamsChange: (params: ScriptListParams) => void
  onSearch: () => void
  onReset: () => void
  onCreate: () => void
}

const ScriptSearch: React.FC<ScriptSearchProps> = ({
  searchParams,
  categories,
  onSearchParamsChange,
  onSearch,
  onReset,
  onCreate
}) => {
  return (
    <Card style={{ marginBottom: 16 }}>
      <Space wrap>
        <Input
          placeholder="搜索剧本标题或描述"
          style={{ width: 200 }}
          value={searchParams.keyword}
          onChange={e => onSearchParamsChange({ ...searchParams, keyword: e.target.value })}
          onPressEnter={onSearch}
        />

        <Select
          placeholder="选择分类"
          style={{ width: 120 }}
          allowClear
          value={searchParams.category}
          onChange={value => onSearchParamsChange({ ...searchParams, category: value })}
        >
          {categories.map(category => (
            <Select.Option key={category} value={category}>
              {category}
            </Select.Option>
          ))}
        </Select>

        <Select
          placeholder="公开状态"
          style={{ width: 120 }}
          allowClear
          value={searchParams.isPublic}
          onChange={value => onSearchParamsChange({ ...searchParams, isPublic: value })}
        >
          <Select.Option value={true}>公开</Select.Option>
          <Select.Option value={false}>私有</Select.Option>
        </Select>

        <Select
          placeholder="启用状态"
          style={{ width: 120 }}
          allowClear
          value={searchParams.isActive}
          onChange={value => onSearchParamsChange({ ...searchParams, isActive: value })}
        >
          <Select.Option value={true}>启用</Select.Option>
          <Select.Option value={false}>禁用</Select.Option>
        </Select>

        <Button type="primary" icon={<SearchOutlined />} onClick={onSearch}>
          搜索
        </Button>

        <Button icon={<ReloadOutlined />} onClick={onReset}>
          重置
        </Button>

        <Button type="primary" icon={<PlusOutlined />} onClick={onCreate}>
          新增剧本
        </Button>
      </Space>
    </Card>
  )
}

export default ScriptSearch
