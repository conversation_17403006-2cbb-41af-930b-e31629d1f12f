import { apiService } from './api'
import type { ApiResponse, PaginatedResponse, User, UserProfile } from '@/types/api'

export interface UserListParams {
  page?: number
  pageSize?: number
  keyword?: string
  gender?: string
  isEmailVerified?: boolean
  startDate?: string
  endDate?: string
}

export interface UserDetailResponse extends User {
  profile?: UserProfile
  subscription?: {
    status: string
    planName?: string
    endDate?: string
  }
  points?: {
    balance: number
    totalUsed: number
  }
  stats?: {
    chatCount: number
    messageCount: number
    totalSpent: number
  }
}

export interface CreateUserRequest {
  email: string
  password?: string
  nickname?: string
  gender?: 'male' | 'female' | 'other'
  generatePassword?: boolean
  // 会员相关字段
  setAsMember?: boolean
  membershipPlanId?: string
  membershipDuration?: number
}

export interface CreateUserResponse {
  id: string
  email: string
  nickname: string
  gender?: 'male' | 'female' | 'other'
  tempPassword?: string
  membershipSet?: boolean
}

// 批量创建用户请求
export interface BatchCreateUsersRequest {
  users: CreateUserRequest[]
}

// 批量创建用户响应
export interface BatchCreateUsersResponse {
  successUsers: CreateUserResponse[]
  failedUsers: Array<{
    email: string
    error: string
  }>
  summary: {
    total: number
    success: number
    failed: number
  }
}

// 用户管理服务
export class UserService {
  // 获取用户列表
  async getUsers(params: UserListParams): Promise<ApiResponse<PaginatedResponse<User>>> {
    return await apiService.get<PaginatedResponse<User>>('/admin/users', { params })
  }

  // 获取用户详情
  async getUserDetail(userId: string): Promise<ApiResponse<UserDetailResponse>> {
    return await apiService.get<UserDetailResponse>(`/admin/users/${userId}`)
  }

  // 创建用户
  async createUser(data: CreateUserRequest): Promise<ApiResponse<CreateUserResponse>> {
    return await apiService.post<CreateUserResponse>('/admin/users', data)
  }

  // 批量创建用户
  async batchCreateUsers(
    data: BatchCreateUsersRequest
  ): Promise<ApiResponse<BatchCreateUsersResponse>> {
    return await apiService.post<BatchCreateUsersResponse>('/admin/users/batch', data)
  }

  // 禁用/启用用户
  async toggleUserStatus(userId: string, isActive: boolean): Promise<ApiResponse<void>> {
    return await apiService.put<void>(`/admin/users/${userId}/status`, { isActive })
  }

  // 重置用户密码
  async resetUserPassword(userId: string): Promise<ApiResponse<{ tempPassword: string }>> {
    return await apiService.post<{ tempPassword: string }>(`/admin/users/${userId}/reset-password`)
  }

  // 删除用户
  async deleteUser(userId: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/users/${userId}`)
  }

  // 调整用户积分
  async adjustUserPoints(
    userId: string,
    points: number,
    reason: string
  ): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/users/${userId}/points`, {
      points,
      reason,
      type: points > 0 ? 'add' : 'deduct'
    })
  }

  // 获取用户统计数据
  async getUserStats(): Promise<
    ApiResponse<{
      totalUsers: number
      activeUsers: number
      newUsersToday: number
      verifiedUsers: number
    }>
  > {
    return await apiService.get<{
      totalUsers: number
      activeUsers: number
      newUsersToday: number
      verifiedUsers: number
    }>('/admin/users/stats')
  }

  // 导出用户数据
  async exportUsers(params: UserListParams): Promise<Blob> {
    const response = await apiService.get('/admin/users/export', {
      params,
      responseType: 'blob'
    })
    // 当 responseType 为 'blob' 时，apiService 直接返回 Blob 而不是 ApiResponse
    return response as unknown as Blob
  }

  // 设为会员
  async setUserMembership(
    userId: string,
    planId: string,
    duration: number
  ): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/users/${userId}/membership`, {
      planId,
      duration
    })
  }
}

export const userService = new UserService()
