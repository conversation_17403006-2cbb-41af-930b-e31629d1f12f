import { apiService } from './api'
import type { ApiResponse, PaginatedResponse } from '@/types/api'
import type {
  Script,
  ScriptListParams,
  ScriptCreateParams,
  ScriptUpdateParams,
  ScriptStats,
  ScriptContent
} from '@/types/script'

// 剧本管理服务
export class ScriptService {
  // ==================== 剧本CRUD ====================

  // 获取剧本列表
  async getScripts(params?: ScriptListParams): Promise<ApiResponse<PaginatedResponse<Script>>> {
    return await apiService.get<PaginatedResponse<Script>>('/admin/content/scripts', { params })
  }

  // 获取剧本详情
  async getScript(id: string): Promise<ApiResponse<Script>> {
    return await apiService.get<Script>(`/admin/content/scripts/${id}`)
  }

  // 创建剧本
  async createScript(params: ScriptCreateParams): Promise<ApiResponse<Script>> {
    return await apiService.post<Script>('/admin/content/scripts', params)
  }

  // 更新剧本
  async updateScript(params: ScriptUpdateParams): Promise<ApiResponse<Script>> {
    const { id, ...updateData } = params
    return await apiService.put<Script>(`/admin/content/scripts/${id}`, updateData)
  }

  // 删除剧本
  async deleteScript(id: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/content/scripts/${id}`)
  }

  // ==================== 剧本状态管理 ====================

  // 启用/禁用剧本
  async toggleScriptStatus(id: string, isActive: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/content/scripts/${id}/toggle-status`, { isActive })
  }

  // 设置剧本公开状态
  async toggleScriptPublic(id: string, isPublic: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/content/scripts/${id}/toggle-public`, { isPublic })
  }

  // 设置会员专享
  async toggleScriptPremium(id: string, isPremium: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/content/scripts/${id}/toggle-premium`, { isPremium })
  }

  // ==================== 剧本内容管理 ====================

  // 获取剧本内容
  async getScriptContent(id: string): Promise<ApiResponse<ScriptContent>> {
    return await apiService.get<ScriptContent>(`/admin/content/scripts/${id}/content`)
  }

  // 更新剧本内容
  async updateScriptContent(id: string, content: ScriptContent): Promise<ApiResponse<void>> {
    return await apiService.put<void>(`/admin/content/scripts/${id}/content`, content)
  }

  // 上传剧本封面图片 - 使用专门的图片上传接口
  async uploadCoverImage(file: File): Promise<ApiResponse<{ url: string }>> {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('fileName', file.name)

      // 如果浏览器无法识别MIME类型，根据文件扩展名设置
      let fileType = file.type
      if (!fileType || fileType === 'application/octet-stream') {
        const extension = file.name.toLowerCase().split('.').pop()
        const mimeMap: Record<string, string> = {
          jpg: 'image/jpeg',
          jpeg: 'image/jpeg',
          png: 'image/png',
          webp: 'image/webp',
          gif: 'image/gif'
        }
        fileType = mimeMap[extension || ''] || file.type || 'application/octet-stream'
      }

      formData.append('fileType', fileType)
      formData.append('uploadType', 'script-cover')

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
        headers: {
          Authorization: `Bearer ${localStorage.getItem('admin_token') || ''}`
        }
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: '上传失败' }))
        return {
          success: false,
          message: errorData.error || `HTTP ${response.status}`,
          data: null
        }
      }

      const result = await response.json()

      if (result.success && result.data?.url) {
        return {
          success: true,
          data: { url: result.data.url }
        }
      } else {
        return {
          success: false,
          message: result.message || '上传失败',
          data: null
        }
      }
    } catch (error) {
      console.error('封面图片上传失败:', error)
      return {
        success: false,
        message: '网络错误或服务器异常',
        data: null
      }
    }
  }

  // 上传阶段图片 - 使用专门的图片上传接口
  async uploadStageImage(file: File): Promise<ApiResponse<{ url: string }>> {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('fileName', file.name)

      // 如果浏览器无法识别MIME类型，根据文件扩展名设置
      let fileType = file.type
      if (!fileType || fileType === 'application/octet-stream') {
        const extension = file.name.toLowerCase().split('.').pop()
        const mimeMap: Record<string, string> = {
          jpg: 'image/jpeg',
          jpeg: 'image/jpeg',
          png: 'image/png',
          webp: 'image/webp',
          gif: 'image/gif'
        }
        fileType = mimeMap[extension || ''] || file.type || 'application/octet-stream'
      }

      formData.append('fileType', fileType)
      formData.append('uploadType', 'script-stage')

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
        headers: {
          Authorization: `Bearer ${localStorage.getItem('admin_token') || ''}`
        }
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: '上传失败' }))
        return {
          success: false,
          message: errorData.error || `HTTP ${response.status}`,
          data: null
        }
      }

      const result = await response.json()

      if (result.success && result.data?.url) {
        return {
          success: true,
          data: { url: result.data.url }
        }
      } else {
        return {
          success: false,
          message: result.message || '上传失败',
          data: null
        }
      }
    } catch (error) {
      console.error('阶段图片上传失败:', error)
      return {
        success: false,
        message: '网络错误或服务器异常',
        data: null
      }
    }
  }

  // 上传剧本音频文件 - 使用专门的音频上传接口
  async uploadAudioFile(file: File): Promise<ApiResponse<{ url: string }>> {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('fileName', file.name)

      // 如果浏览器无法识别MIME类型，根据文件扩展名设置
      let fileType = file.type
      if (!fileType || fileType === 'application/octet-stream') {
        const extension = file.name.toLowerCase().split('.').pop()
        const mimeMap: Record<string, string> = {
          mp3: 'audio/mpeg',
          wav: 'audio/wav',
          ogg: 'audio/ogg',
          m4a: 'audio/m4a',
          flac: 'audio/flac',
          aac: 'audio/aac',
          webm: 'audio/webm'
        }
        fileType = mimeMap[extension || ''] || file.type || 'application/octet-stream'
      }

      formData.append('fileType', fileType)
      formData.append('uploadType', 'script-audio')

      const response = await fetch('/api/upload/audio', {
        method: 'POST',
        body: formData,
        headers: {
          // 不设置 Content-Type，让浏览器自动设置 multipart/form-data
          Authorization: `Bearer ${localStorage.getItem('admin_token') || ''}`
        }
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: '上传失败' }))
        return {
          success: false,
          message: errorData.error || `HTTP ${response.status}`,
          data: null
        }
      }

      const result = await response.json()

      if (result.success && result.data?.url) {
        return {
          success: true,
          data: { url: result.data.url }
        }
      } else {
        return {
          success: false,
          message: result.message || '上传失败',
          data: null
        }
      }
    } catch (error) {
      console.error('音频上传失败:', error)
      return {
        success: false,
        message: '网络错误或服务器异常',
        data: null
      }
    }
  }

  // ==================== 统计和分析 ====================

  // 获取剧本统计数据
  async getStats(): Promise<ApiResponse<ScriptStats>> {
    return await apiService.get<ScriptStats>('/admin/content/scripts/stats/summary')
  }

  // 获取剧本分类列表
  async getCategories(): Promise<ApiResponse<string[]>> {
    return await apiService.get<string[]>('/admin/content/scripts/categories')
  }

  // 获取剧本使用记录
  async getUsageRecords(
    scriptId: string,
    params?: {
      page?: number
      pageSize?: number
    }
  ): Promise<ApiResponse<PaginatedResponse<{ id: string; userId: string; usedAt: string }>>> {
    return await apiService.get<PaginatedResponse<{ id: string; userId: string; usedAt: string }>>(
      `/admin/content/scripts/${scriptId}/usage`,
      { params }
    )
  }

  // ==================== 批量操作 ====================

  // 批量删除剧本
  async batchDeleteScripts(ids: string[]): Promise<ApiResponse<void>> {
    return await apiService.post<void>('/admin/content/scripts/batch-delete', { ids })
  }

  // 批量设置状态
  async batchToggleStatus(ids: string[], isActive: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>('/admin/content/scripts/batch-toggle-status', {
      ids,
      isActive
    })
  }

  // 批量设置公开状态
  async batchTogglePublic(ids: string[], isPublic: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>('/admin/content/scripts/batch-toggle-public', {
      ids,
      isPublic
    })
  }

  // ==================== 导入导出 ====================

  // 导出剧本数据
  async exportScripts(params?: {
    format?: 'json' | 'csv'
    ids?: string[]
  }): Promise<ApiResponse<{ downloadUrl: string }>> {
    return await apiService.post<{ downloadUrl: string }>('/admin/content/scripts/export', params)
  }

  // 导入剧本数据
  async importScripts(file: File): Promise<
    ApiResponse<{
      success: number
      failed: number
      errors?: string[]
    }>
  > {
    const formData = new FormData()
    formData.append('file', file)
    return await apiService.post<{
      success: number
      failed: number
      errors?: string[]
    }>('/admin/content/scripts/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  // ==================== 剧本预览 ====================

  // 生成剧本预览
  async generatePreview(id: string): Promise<
    ApiResponse<{
      previewUrl: string
      thumbnails: string[]
    }>
  > {
    return await apiService.post<{
      previewUrl: string
      thumbnails: string[]
    }>(`/admin/content/scripts/${id}/generate-preview`)
  }

  // 获取剧本预览
  async getPreview(id: string): Promise<
    ApiResponse<{
      previewUrl?: string
      thumbnails?: string[]
    }>
  > {
    return await apiService.get<{
      previewUrl?: string
      thumbnails?: string[]
    }>(`/admin/content/scripts/${id}/preview`)
  }
}

export const scriptService = new ScriptService()
