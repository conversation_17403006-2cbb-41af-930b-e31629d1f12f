import * as XLSX from 'xlsx'
import type { Command } from '@/types/script'

// 指令Excel模板列定义
export const COMMAND_TEMPLATE_COLUMNS = [
  { key: 'time', label: '时间(秒)', width: 15, example: '0' },
  { key: 'command', label: '指令内容', width: 40, example: '开始播放背景音乐' }
]

// 阶段模板列定义
export const STAGE_TEMPLATE_COLUMNS = [
  { key: 'stage', label: '阶段编号', width: 12, example: '1' },
  { key: 'stageTitle', label: '阶段标题', width: 30, example: '开场准备' },
  { key: 'intensityTime', label: '强度时间点', width: 15, example: '00:01:30' },
  { key: 'thrust', label: '推力强度(-1~9)', width: 15, example: '2' },
  { key: 'suction', label: '吸力强度(-1~9)', width: 15, example: '1' },
  { key: 'vibrate', label: '震动强度(-1~9)', width: 15, example: '3' },
  { key: 'pictureName', label: '图片名称(可选)', width: 25, example: '场景1' }
]

/**
 * 下载指令Excel模板
 */
export const downloadCommandTemplate = () => {
  // 创建工作簿
  const wb = XLSX.utils.book_new()

  // 创建示例数据
  const templateData = [
    // 表头
    COMMAND_TEMPLATE_COLUMNS.map(col => col.label),
    // 示例数据行
    COMMAND_TEMPLATE_COLUMNS.map(col => col.example),
    // 空行供用户填写
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', '']
  ]

  // 创建工作表
  const ws = XLSX.utils.aoa_to_sheet(templateData)

  // 设置列宽
  ws['!cols'] = COMMAND_TEMPLATE_COLUMNS.map(col => ({ width: col.width }))

  // 设置表头样式 (如果支持的话)
  const headerRange = XLSX.utils.decode_range('A1:E1')
  for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
    const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col })
    if (ws[cellAddress]) {
      ws[cellAddress].s = {
        font: { bold: true },
        fill: { fgColor: { rgb: 'E6F3FF' } }
      }
    }
  }

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '指令模板')

  // 生成文件并下载
  const fileName = `指令模板_${new Date().toISOString().slice(0, 10)}.xlsx`
  XLSX.writeFile(wb, fileName)
}

/**
 * 下载阶段Excel模板
 */
export const downloadStageTemplate = () => {
  const wb = XLSX.utils.book_new()

  // 创建表头数据
  const headers = STAGE_TEMPLATE_COLUMNS.map(col => col.label)
  const examples = STAGE_TEMPLATE_COLUMNS.map(col => col.example)

  // 创建示例数据
  const sampleData = [
    ['1', '开场准备', '00:00:10', '1', '0', '2', '开场图片'],
    ['1', '开场准备', '00:00:30', '2', '1', '3', ''],
    ['2', '高潮阶段', '00:02:00', '3', '2', '3', '高潮场景'],
    ['2', '高潮阶段', '00:02:30', '2', '3', '2', ''],
    ['3', '结束阶段', '00:05:00', '1', '1', '1', '结束画面']
  ]

  // 合并数据
  const data = [headers, examples, ...sampleData]

  // 创建工作表
  const ws = XLSX.utils.aoa_to_sheet(data)

  // 设置列宽
  ws['!cols'] = STAGE_TEMPLATE_COLUMNS.map(col => ({ width: col.width }))

  // 设置表头样式
  for (let i = 0; i < STAGE_TEMPLATE_COLUMNS.length; i++) {
    const cellAddress = XLSX.utils.encode_cell({ r: 0, c: i })
    if (ws[cellAddress]) {
      ws[cellAddress].s = {
        font: { bold: true },
        fill: { fgColor: { rgb: 'E6F3FF' } }
      }
    }
  }

  // 设置示例行样式
  for (let i = 0; i < STAGE_TEMPLATE_COLUMNS.length; i++) {
    const cellAddress = XLSX.utils.encode_cell({ r: 1, c: i })
    if (ws[cellAddress]) {
      ws[cellAddress].s = {
        font: { italic: true },
        fill: { fgColor: { rgb: 'F0F0F0' } }
      }
    }
  }

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '阶段模板')

  // 生成文件并下载
  const fileName = `阶段模板_${new Date().toISOString().slice(0, 10)}.xlsx`
  XLSX.writeFile(wb, fileName)
}

/**
 * 解析上传的Excel文件并转换为指令数据
 */
export const parseCommandExcel = (
  file: File
): Promise<{
  success: boolean
  data?: Command[]
  errors?: string[]
}> => {
  return new Promise(resolve => {
    const reader = new FileReader()

    reader.onload = e => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })

        // 获取第一个工作表
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]

        // 将工作表转换为JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1, // 使用数组格式
          defval: '' // 空单元格默认值
        }) as string[][]

        if (jsonData.length < 2) {
          resolve({
            success: false,
            errors: ['Excel文件格式不正确，至少需要包含表头和一行数据']
          })
          return
        }

        // 跳过表头行，从第二行开始解析数据
        const dataRows = jsonData.slice(1).filter(row => {
          // 过滤掉完全空白的行 - 确保至少前两列（时间和指令）有内容
          if (!row || row.length === 0) return false

          const timeCell = row[0]
          const commandCell = row[1]

          // 至少时间或指令内容不为空
          return (
            (timeCell && timeCell.toString().trim() !== '') ||
            (commandCell && commandCell.toString().trim() !== '')
          )
        })

        if (dataRows.length === 0) {
          resolve({
            success: false,
            errors: ['Excel文件中没有有效的数据行']
          })
          return
        }

        const commands: Command[] = []
        const errors: string[] = []

        dataRows.forEach((row, index) => {
          const rowNum = index + 2 // 实际行号（从1开始，跳过表头）

          try {
            // 解析各列数据
            const timeStr = row[0]?.toString().trim() || ''
            const commandContent = row[1]?.toString().trim() || ''

            // 跳过完全空白的行（防止处理Excel中的空行）
            if (!timeStr && !commandContent) {
              return
            }

            // 验证必填字段
            if (!timeStr) {
              errors.push(`第${rowNum}行：时间不能为空`)
              return
            }

            if (!commandContent) {
              errors.push(`第${rowNum}行：指令内容不能为空`)
              return
            }

            // 解析并格式化时间 - 支持 HH:MM:SS,mmm 格式并转换为 HH:MM:SS
            let formattedTime: string

            if (timeStr.includes(':')) {
              // HH:MM:SS,mmm 或 HH:MM:SS.mmm 格式
              try {
                // 去掉毫秒部分（逗号或点号后的部分）
                let cleanTimeStr = timeStr
                if (timeStr.includes(',')) {
                  cleanTimeStr = timeStr.split(',')[0]
                } else if (timeStr.includes('.') && timeStr.split(':').length === 3) {
                  // 只有在是时间格式时才去掉点号后的部分
                  const parts = timeStr.split(':')
                  if (parts.length === 3 && parts[2].includes('.')) {
                    parts[2] = parts[2].split('.')[0]
                    cleanTimeStr = parts.join(':')
                  }
                }

                // 验证格式 HH:MM:SS
                const parts = cleanTimeStr.split(':')
                if (parts.length !== 3) {
                  throw new Error('时间格式不正确')
                }

                const hours = parseInt(parts[0])
                const minutes = parseInt(parts[1])
                const seconds = parseInt(parts[2])

                // 验证时间范围
                if (
                  isNaN(hours) ||
                  isNaN(minutes) ||
                  isNaN(seconds) ||
                  hours < 0 ||
                  minutes < 0 ||
                  minutes >= 60 ||
                  seconds < 0 ||
                  seconds >= 60
                ) {
                  throw new Error('时间数值不正确')
                }

                // 格式化为 HH:MM:SS
                formattedTime = `${hours.toString().padStart(2, '0')}:${minutes
                  .toString()
                  .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
              } catch {
                errors.push(`第${rowNum}行：时间格式不正确，应为 HH:MM:SS 格式（如：00:01:30）`)
                return
              }
            } else {
              // 纯数字格式（秒数）- 转换为 HH:MM:SS 格式
              const totalSeconds = parseFloat(timeStr.replace(',', '.'))

              if (isNaN(totalSeconds) || totalSeconds < 0) {
                errors.push(`第${rowNum}行：时间必须是大于等于0的数字`)
                return
              }

              const hours = Math.floor(totalSeconds / 3600)
              const minutes = Math.floor((totalSeconds % 3600) / 60)
              const seconds = Math.floor(totalSeconds % 60)

              formattedTime = `${hours.toString().padStart(2, '0')}:${minutes
                .toString()
                .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
            }

            // 创建指令对象
            const command: Command = {
              time: formattedTime,
              command: commandContent
            }

            commands.push(command)
          } catch (error) {
            errors.push(`第${rowNum}行：数据解析失败 - ${error}`)
          }
        })

        // 检查时间重复（只检查成功创建的指令）
        if (commands.length > 0) {
          const timeCount = new Map<string, number>()

          // 统计每个时间点的出现次数
          commands.forEach(cmd => {
            const count = timeCount.get(cmd.time) || 0
            timeCount.set(cmd.time, count + 1)
          })

          // 报告重复的时间点
          const duplicateTimes: string[] = []
          timeCount.forEach((count, time) => {
            if (count > 1) {
              duplicateTimes.push(`${time}秒(${count}次)`)
            }
          })

          if (duplicateTimes.length > 0) {
            errors.push(`发现重复的时间点：${duplicateTimes.join(', ')}`)
          }
        }

        if (errors.length > 0) {
          resolve({
            success: false,
            errors
          })
        } else {
          // 按时间排序
          commands.sort((a, b) => parseFloat(a.time) - parseFloat(b.time))

          resolve({
            success: true,
            data: commands
          })
        }
      } catch (error) {
        resolve({
          success: false,
          errors: [`文件解析失败：${error}`]
        })
      }
    }

    reader.onerror = () => {
      resolve({
        success: false,
        errors: ['文件读取失败']
      })
    }

    reader.readAsArrayBuffer(file)
  })
}

/**
 * 解析阶段Excel文件并转换为阶段数据
 */
export const parseStageExcel = (
  file: File
): Promise<{
  success: boolean
  data?: import('@/types/script').Stage[]
  errors?: string[]
}> => {
  return new Promise(resolve => {
    const reader = new FileReader()

    reader.onload = e => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })

        // 获取第一个工作表
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]

        // 将工作表转换为JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1, // 使用数组格式
          defval: '' // 空单元格默认值
        }) as string[][]

        if (jsonData.length < 3) {
          resolve({
            success: false,
            errors: ['Excel文件格式不正确，至少需要包含表头、示例行和一行数据']
          })
          return
        }

        // 智能识别数据开始位置
        let dataStartIndex = 1 // 默认从第二行开始（跳过表头）

        // 检查第二行是否是示例格式行（通常包含格式说明，不是真实数据）
        if (jsonData.length > 2) {
          const secondRow = jsonData[1]

          // 检查第二行是否看起来像示例行（包含特定的示例文本）
          const isExampleRow = secondRow.some(cell => {
            const cellStr = cell?.toString().toLowerCase() || ''
            return (
              cellStr.includes('example') ||
              cellStr.includes('示例') ||
              cellStr.includes('格式') ||
              cellStr === '开场准备'
            ) // 模板中的示例文本
          })

          if (isExampleRow) {
            dataStartIndex = 2 // 跳过表头和示例行
          }
        }

        // 从确定的起始位置开始解析数据
        const dataRows = jsonData.slice(dataStartIndex).filter(row => {
          // 过滤掉完全空白的行 - 确保至少阶段编号和标题有内容
          if (!row || row.length === 0) return false

          const stageCell = row[0]
          const titleCell = row[1]

          // 至少阶段编号或标题不为空
          return (
            (stageCell && stageCell.toString().trim() !== '') ||
            (titleCell && titleCell.toString().trim() !== '')
          )
        })

        if (dataRows.length === 0) {
          resolve({
            success: false,
            errors: ['Excel文件中没有有效的数据行']
          })
          return
        }

        const stagesMap = new Map<number, import('@/types/script').Stage>()
        const errors: string[] = []

        dataRows.forEach((row, index) => {
          const rowNum = index + dataStartIndex + 1 // 实际行号（从1开始，基于数据起始位置）

          try {
            // 解析各列数据
            const stageNumStr = row[0]?.toString().trim() || ''
            const stageTitle = row[1]?.toString().trim() || ''
            const intensityTimeStr = row[2]?.toString().trim() || ''
            const thrustStr = row[3]?.toString().trim() || ''
            const suctionStr = row[4]?.toString().trim() || ''
            const vibrateStr = row[5]?.toString().trim() || ''
            const pictureName = row[6]?.toString().trim() || ''

            // 跳过完全空白的行
            if (
              !stageNumStr &&
              !stageTitle &&
              !intensityTimeStr &&
              !thrustStr &&
              !suctionStr &&
              !vibrateStr &&
              !pictureName
            ) {
              return
            }

            // 验证必填字段
            if (!stageNumStr) {
              errors.push(`第${rowNum}行：阶段编号不能为空`)
              return
            }

            if (!stageTitle) {
              errors.push(`第${rowNum}行：阶段标题不能为空`)
              return
            }

            // 解析阶段编号
            const stageNum = parseInt(stageNumStr)
            if (isNaN(stageNum) || stageNum < 1) {
              errors.push(`第${rowNum}行：阶段编号必须是大于0的整数`)
              return
            }

            // 获取或创建阶段
            let stage = stagesMap.get(stageNum)
            if (!stage) {
              stage = {
                stage: stageNum,
                stageTitle: stageTitle,
                pics: [],
                intensity: {}
              }
              stagesMap.set(stageNum, stage)
            } else if (stage.stageTitle !== stageTitle) {
              // 如果同一阶段的标题不一致，使用第一次出现的标题
              console.warn(`第${rowNum}行：阶段${stageNum}的标题与之前不一致，将使用首次出现的标题`)
            }

            // 处理强度设置（如果有强度时间点）
            if (intensityTimeStr) {
              // 解析并格式化时间 - 支持 HH:MM:SS,mmm 格式并转换为 HH:MM:SS
              let formattedTime: string

              if (intensityTimeStr.includes(':')) {
                try {
                  // 去掉毫秒部分（逗号或点号后的部分）
                  let cleanTimeStr = intensityTimeStr
                  if (intensityTimeStr.includes(',')) {
                    cleanTimeStr = intensityTimeStr.split(',')[0]
                  } else if (
                    intensityTimeStr.includes('.') &&
                    intensityTimeStr.split(':').length === 3
                  ) {
                    const parts = intensityTimeStr.split(':')
                    if (parts.length === 3 && parts[2].includes('.')) {
                      parts[2] = parts[2].split('.')[0]
                      cleanTimeStr = parts.join(':')
                    }
                  }

                  // 验证格式 HH:MM:SS
                  const parts = cleanTimeStr.split(':')
                  if (parts.length !== 3) {
                    throw new Error('时间格式不正确')
                  }

                  const hours = parseInt(parts[0])
                  const minutes = parseInt(parts[1])
                  const seconds = parseInt(parts[2])

                  // 验证时间范围
                  if (
                    isNaN(hours) ||
                    isNaN(minutes) ||
                    isNaN(seconds) ||
                    hours < 0 ||
                    minutes < 0 ||
                    minutes >= 60 ||
                    seconds < 0 ||
                    seconds >= 60
                  ) {
                    throw new Error('时间数值不正确')
                  }

                  // 格式化为 HH:MM:SS
                  formattedTime = `${hours.toString().padStart(2, '0')}:${minutes
                    .toString()
                    .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
                } catch {
                  errors.push(
                    `第${rowNum}行：强度时间格式不正确，应为 HH:MM:SS 格式（如：00:01:30）`
                  )
                  return
                }
              } else {
                errors.push(`第${rowNum}行：强度时间格式不正确，应为 HH:MM:SS 格式（如：00:01:30）`)
                return
              }

              // 解析强度值
              const thrust = thrustStr ? parseInt(thrustStr) : 0
              const suction = suctionStr ? parseInt(suctionStr) : 0
              const vibrate = vibrateStr ? parseInt(vibrateStr) : 0

              // 验证强度值范围
              if (isNaN(thrust) || thrust < -1 || thrust > 9) {
                errors.push(`第${rowNum}行：推力强度必须是-1到9之间的整数`)
                return
              }
              if (isNaN(suction) || suction < -1 || suction > 9) {
                errors.push(`第${rowNum}行：吸力强度必须是-1到9之间的整数`)
                return
              }
              if (isNaN(vibrate) || vibrate < -1 || vibrate > 9) {
                errors.push(`第${rowNum}行：震动强度必须是-1到9之间的整数`)
                return
              }

              // 添加强度设置
              stage.intensity[formattedTime] = { thrust, suction, vibrate }
            }

            // 处理图片（如果有图片名称）
            if (pictureName) {
              // 检查是否已存在相同名称的图片
              const existingPic = stage.pics.find(pic => pic.name === pictureName)
              if (!existingPic) {
                stage.pics.push({
                  name: pictureName,
                  pic: '' // 图片URL为空，需要后续上传
                })
              }
            }
          } catch (error) {
            errors.push(`第${rowNum}行：数据解析失败 - ${error}`)
          }
        })

        // 转换为数组并按阶段编号排序
        const stages = Array.from(stagesMap.values()).sort((a, b) => a.stage - b.stage)

        if (errors.length > 0) {
          resolve({
            success: false,
            errors
          })
        } else {
          resolve({
            success: true,
            data: stages
          })
        }
      } catch (error) {
        resolve({
          success: false,
          errors: [`文件解析失败：${error}`]
        })
      }
    }

    reader.onerror = () => {
      resolve({
        success: false,
        errors: ['文件读取失败']
      })
    }

    reader.readAsArrayBuffer(file)
  })
}

/**
 * 导出指令数据为Excel文件
 */
export const exportCommandsToExcel = (commands: Command[], fileName?: string) => {
  // 准备数据
  const data = [
    // 表头
    COMMAND_TEMPLATE_COLUMNS.map(col => col.label),
    // 数据行
    ...commands.map(cmd => [cmd.time, cmd.command])
  ]

  // 创建工作簿和工作表
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.aoa_to_sheet(data)

  // 设置列宽
  ws['!cols'] = COMMAND_TEMPLATE_COLUMNS.map(col => ({ width: col.width }))

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '指令数据')

  // 生成文件并下载
  const defaultFileName = `指令数据_${new Date().toISOString().slice(0, 10)}.xlsx`
  XLSX.writeFile(wb, fileName || defaultFileName)
}

/**
 * 导出阶段数据为Excel文件
 */
export const exportStagesToExcel = (stages: import('@/types/script').Stage[]) => {
  const wb = XLSX.utils.book_new()

  // 创建表头
  const headers = STAGE_TEMPLATE_COLUMNS.map(col => col.label)
  const data = [headers]

  // 转换阶段数据为Excel行
  stages.forEach(stage => {
    // 先处理强度设置
    const intensityEntries = Object.entries(stage.intensity)
    if (intensityEntries.length > 0) {
      intensityEntries.forEach(([time, intensity]) => {
        data.push([
          stage.stage.toString(),
          stage.stageTitle,
          time,
          intensity.thrust.toString(),
          intensity.suction.toString(),
          intensity.vibrate.toString(),
          '' // 图片名称暂空
        ])
      })
    }

    // 处理图片
    stage.pics.forEach(pic => {
      data.push([
        stage.stage.toString(),
        stage.stageTitle,
        '', // 强度时间点暂空
        '', // 推力强度暂空
        '', // 吸力强度暂空
        '', // 震动强度暂空
        pic.name
      ])
    })

    // 如果既没有强度设置也没有图片，至少添加一行基本信息
    if (intensityEntries.length === 0 && stage.pics.length === 0) {
      data.push([stage.stage.toString(), stage.stageTitle, '', '', '', '', ''])
    }
  })

  // 创建工作表
  const ws = XLSX.utils.aoa_to_sheet(data)

  // 设置列宽
  ws['!cols'] = STAGE_TEMPLATE_COLUMNS.map(col => ({ width: col.width }))

  // 设置表头样式
  for (let i = 0; i < STAGE_TEMPLATE_COLUMNS.length; i++) {
    const cellAddress = XLSX.utils.encode_cell({ r: 0, c: i })
    if (ws[cellAddress]) {
      ws[cellAddress].s = {
        font: { bold: true },
        fill: { fgColor: { rgb: 'E6F3FF' } }
      }
    }
  }

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '阶段数据')

  // 生成文件并下载
  const fileName = `阶段数据_${new Date().toISOString().slice(0, 10)}.xlsx`
  XLSX.writeFile(wb, fileName)
}
