/**
 * 时间格式工具函数
 */

/**
 * 验证时间格式是否为 MM:SS 格式
 * @param timeStr 时间字符串，如 "21:11"
 * @returns 是否为有效格式
 */
export const isValidTimeFormat = (timeStr: string): boolean => {
  const timeRegex = /^\d{1,2}:\d{2}$/
  if (!timeRegex.test(timeStr)) {
    return false
  }

  const [minutes, seconds] = timeStr.split(':').map(Number)

  // 分钟可以是任意正整数，秒钟必须在 0-59 之间
  return minutes >= 0 && seconds >= 0 && seconds <= 59
}

/**
 * 格式化时间字符串，确保秒钟是两位数
 * @param timeStr 时间字符串，如 "21:5" 或 "21:11"
 * @returns 格式化后的时间字符串，如 "21:05" 或 "21:11"
 */
export const formatTimeString = (timeStr: string): string => {
  if (!timeStr) return ''

  const parts = timeStr.split(':')
  if (parts.length !== 2) return timeStr

  const [minutes, seconds] = parts
  const formattedSeconds = seconds.padStart(2, '0')

  return `${minutes}:${formattedSeconds}`
}

/**
 * 将时间字符串转换为总秒数
 * @param timeStr 时间字符串，如 "21:11"
 * @returns 总秒数
 */
export const timeStringToSeconds = (timeStr: string): number => {
  if (!timeStr || !isValidTimeFormat(timeStr)) {
    return 0
  }

  const [minutes, seconds] = timeStr.split(':').map(Number)
  return minutes * 60 + seconds
}

/**
 * 将总秒数转换为时间字符串
 * @param totalSeconds 总秒数
 * @returns 时间字符串，如 "21:11"
 */
export const secondsToTimeString = (totalSeconds: number): string => {
  if (totalSeconds < 0) return '0:00'

  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60

  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

/**
 * 获取时间字符串的可读描述
 * @param timeStr 时间字符串，如 "21:11"
 * @returns 可读描述，如 "21分11秒"
 */
export const getTimeDescription = (timeStr: string): string => {
  if (!timeStr || !isValidTimeFormat(timeStr)) {
    return timeStr
  }

  const [minutes, seconds] = timeStr.split(':').map(Number)

  if (minutes === 0) {
    return `${seconds}秒`
  } else if (seconds === 0) {
    return `${minutes}分钟`
  } else {
    return `${minutes}分${seconds}秒`
  }
}
