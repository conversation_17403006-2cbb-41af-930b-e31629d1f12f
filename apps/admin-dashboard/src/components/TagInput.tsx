import React, { useState, useRef } from 'react'
import { Tag, Input, Space } from 'antd'
import type { InputRef } from 'antd'
import { PlusOutlined } from '@ant-design/icons'

interface TagInputProps {
  value?: string[]
  onChange?: (tags: string[]) => void
  placeholder?: string
  maxTags?: number
}

const TagInput: React.FC<TagInputProps> = ({
  value = [],
  onChange,
  placeholder = '输入标签后按回车或逗号分隔',
  maxTags = 10
}) => {
  const [inputValue, setInputValue] = useState('')
  const [inputVisible, setInputVisible] = useState(false)
  const inputRef = useRef<InputRef>(null)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputVal = e.target.value

    // 检查是否包含逗号，如果包含则自动分割
    if (inputVal.includes(',')) {
      const newTags = inputVal
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag && !value.includes(tag))

      if (newTags.length > 0 && value.length + newTags.length <= maxTags) {
        const updatedTags = [...value, ...newTags]
        onChange?.(updatedTags)
        setInputValue('')
      } else {
        // 如果有重复或超出限制，只保留最后一个未处理的部分
        const lastPart = inputVal.split(',').pop()?.trim() || ''
        setInputValue(lastPart)
      }
    } else {
      setInputValue(inputVal)
    }
  }

  const handleInputConfirm = () => {
    const trimmedValue = inputValue.trim()
    if (trimmedValue && !value.includes(trimmedValue) && value.length < maxTags) {
      const newTags = [...value, trimmedValue]
      onChange?.(newTags)
    }
    setInputValue('')
    setInputVisible(false)
  }

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleInputConfirm()
    }
  }

  const handleTagClose = (tagToRemove: string) => {
    const newTags = value.filter(tag => tag !== tagToRemove)
    onChange?.(newTags)
  }

  const showInput = () => {
    setInputVisible(true)
    setTimeout(() => {
      inputRef.current?.focus()
    }, 100)
  }

  return (
    <Space size={[8, 8]} wrap>
      {value.map(tag => (
        <Tag key={tag} closable onClose={() => handleTagClose(tag)} style={{ marginBottom: 8 }}>
          {tag}
        </Tag>
      ))}

      {inputVisible ? (
        <Input
          ref={inputRef}
          size="small"
          style={{ width: 200 }}
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleInputKeyDown}
          onBlur={handleInputConfirm}
          placeholder={placeholder}
        />
      ) : (
        value.length < maxTags && (
          <Tag
            onClick={showInput}
            style={{
              background: '#fff',
              borderStyle: 'dashed',
              cursor: 'pointer'
            }}
          >
            <PlusOutlined /> 添加标签
          </Tag>
        )
      )}
    </Space>
  )
}

export default TagInput
