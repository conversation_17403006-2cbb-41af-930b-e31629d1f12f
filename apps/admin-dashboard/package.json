{"name": "admin-dashboard", "private": true, "version": "0.0.0", "type": "module", "description": "灵犀秘境 AI 女友聊天应用管理后台", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write .", "deploy": "pnpm build && wrangler pages deploy", "deploy:preview": "pnpm build && wrangler pages deploy --env preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.26.3", "axios": "^1.10.0", "dayjs": "^1.11.13", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.1", "xlsx": "^0.18.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/node": "^24.0.10", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "wrangler": "^4.18.0"}}