// wrangler.toml
name = "hono-supabase-kv-app"
type = "javascript"
compatibility_date = "2024-07-23"

[[kv_namespaces]]
binding = "USER_CACHE"
id = "__your_kv_id__"

[vars]
SUPABASE_URL = "https://xyzcompany.supabase.co"
SUPABASE_KEY = "your_anon_or_service_role_key"
NODE_ENV = "development"

// src/index.ts
import { Hono } from 'hono'
import adminRoutes from './modules/admin'
import appRoutes from './modules/app'

const app = new Hono()

app.route('/admin', adminRoutes)
app.route('/app', appRoutes)

export default app

// src/modules/admin/index.ts
import { Hono } from 'hono'
import userRoutes from './user/routes/user.route'
import articleRoutes from './article/routes/article.route'

const admin = new Hono()
admin.route('/users', userRoutes)
admin.route('/articles', articleRoutes)

export default admin

// src/modules/admin/user/routes/user.route.ts
import { Hono } from 'hono'
import { getUserHandler } from '../controllers/user.controller'

const route = new Hono()
route.get('/:id', getUserHandler)

export default route

// src/modules/admin/user/controllers/user.controller.ts
import { Context } from 'hono'
import { getUserById } from '../services/user.service'

export const getUserHandler = async (c: Context) => {
  const id = c.req.param('id')
  const user = await getUserById(c.env, id)
  return user ? c.json(user) : c.notFound()
}

// src/modules/admin/user/services/user.service.ts
import { getUserFromDB } from '../repositories/user.repository'
import { cacheGetOrFetch } from '../../../../utils/cache'
import type { Env } from '@/types/env'

export async function getUserById(env: Env, id: string) {
  const key = `admin:user:${id}`
  return await cacheGetOrFetch({
    env,
    namespace: env.USER_CACHE,
    key,
    ttl: 600,
    fallback: () => getUserFromDB(id),
  })
}

// src/modules/admin/user/repositories/user.repository.ts
import { supabase } from '../../../../supabase/client'

export async function getUserFromDB(id: string) {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', id)
    .single()

  if (error) return null
  return data
}

// src/modules/admin/article/routes/article.route.ts
import { Hono } from 'hono'
import { getArticleHandler } from '../controllers/article.controller'

const route = new Hono()
route.get('/:id', getArticleHandler)

export default route

// src/modules/admin/article/controllers/article.controller.ts
import { Context } from 'hono'
import { getArticleById } from '../services/article.service'

export const getArticleHandler = async (c: Context) => {
  const id = c.req.param('id')
  const article = await getArticleById(c.env, id)
  return article ? c.json(article) : c.notFound()
}

// src/modules/admin/article/services/article.service.ts
import { getArticleFromDB } from '../repositories/article.repository'
import type { Env } from '@/types/env'

export async function getArticleById(env: Env, id: string) {
  const key = `admin:article:${id}`
  return await  getArticleFromDB(id)
}

// src/modules/admin/article/repositories/article.repository.ts
import { getSupabase } from '@/lib/db/queries/base'

export async function getArticleFromDB(id: string) {
  const supabase = getSupabase(env)
  const { data, error } = await supabase
    .from('articles')
    .select('*')
    .eq('id', id)
    .single()

  if (error) return null
  return data
}

// src/modules/app/index.ts
import { Hono } from 'hono'
import orderRoutes from './order/routes/order.route'

const app = new Hono()
app.route('/orders', orderRoutes)

export default app

// src/modules/app/order/routes/order.route.ts
import { Hono } from 'hono'
import { getOrderHandler } from '../controllers/order.controller'

const route = new Hono()
route.get('/:id', getOrderHandler)

export default route

// src/modules/app/order/controllers/order.controller.ts
import { Context } from 'hono'
import { getOrderById } from '../services/order.service'

export const getOrderHandler = async (c: Context) => {
  const id = c.req.param('id')
  const order = await getOrderById(c.env, id)
  return order ? c.json(order) : c.notFound()
}

// src/modules/app/order/services/order.service.ts
import { getOrderFromDB } from '../repositories/order.repository'
import type { Env } from '@/types/env'

export async function getOrderById(env: Env, id: string) {
  return getOrderFromDB(id)
}

// src/modules/app/order/repositories/order.repository.ts
import { getSupabase } from '@/lib/db/queries/base'

export async function getOrderFromDB(id: string) {
  const supabase = getSupabase(env)
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('id', id)
    .single()

  if (error) return null
  return data
}

// src/utils/cache.ts
type CacheOptions<T> = {
  env: Env
  namespace: KVNamespace
  key: string
  ttl?: number
  fallback?: () => Promise<T | null>
}

export async function cacheGetOrFetch<T = any>(options: CacheOptions<T>): Promise<T | null> {
  const { namespace, key, ttl = 300, fallback } = options
  const raw = await namespace.get(key, { type: 'json' })
  if (raw) return raw as T

  if (fallback) {
    const data = await fallback()
    if (data) {
      await namespace.put(key, JSON.stringify(data), { expirationTtl: ttl })
    }
    return data
  }
  return null
}

export async function cacheSet<T = any>(namespace: KVNamespace, key: string, data: T, ttl = 300) {
  await namespace.put(key, JSON.stringify(data), { expirationTtl: ttl })
}

export async function cacheDelete(namespace: KVNamespace, key: string) {
  await namespace.delete(key)
}
