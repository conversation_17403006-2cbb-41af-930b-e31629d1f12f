import type { Context, Next } from 'hono'
import type { Env } from '@/types/env'
import { checkUserMembership, getUserPoints, ensureUserExists } from '@/lib/db/queries/membership'
import { throwBusinessError } from '@/middleware/global-error-handler'
import { ErrorCode } from '@/types/errors'

// 权限检查结果类型
export interface PermissionCheckResult {
  hasPermission: boolean
  reason?: string
  userType: 'free' | 'member'
  pointsAvailable: number
  membershipInfo?: {
    planId: string
    planName: string
    endDate: Date
  }
}

// 权限类型枚举
export enum PermissionType {
  // 文本对话相关
  TEXT_CHAT = 'text_chat',
  TEXT_CHAT_UNLIMITED = 'text_chat_unlimited',

  // 角色创建相关
  CHARACTER_CREATE = 'character_create',
  CHARACTER_CREATE_UNLIMITED = 'character_create_unlimited',

  // 积分消费相关
  IMAGE_GENERATION = 'image_generation',
  VOICE_GENERATION = 'voice_generation',
  SCRIPT_PURCHASE = 'script_purchase',
  VIDEO_GENERATION = 'video_generation',

  // 会员专属功能
  MEMBER_ONLY = 'member_only',
  GALLERY_GENERATION = 'gallery_generation'
}

// 权限配置
export const PERMISSION_CONFIG = {
  // 免费用户限制
  FREE_USER_LIMITS: {
    // TEXT_CHAT_DAILY: 100, // 每日文本对话次数
    // CHARACTER_CREATE_MAX: 1, // 最大角色创建数量
  },

  // 积分消费规则
  POINTS_COST: {
    IMAGE_GENERATION: 10,
    VOICE_GENERATION: 5,
    SCRIPT_PURCHASE: 50,
    PREMIUM_TEMPLATE: 20,
    GALLERY_GENERATION: 15
  },

  // 会员等级角色创建限制
  MEMBER_CHARACTER_LIMITS: {
    Pro: 5,
    Elite: 20,
    Ultra: -1 // -1 表示无限制
  }
} as const

/**
 * 权限检查中间件
 * 检查用户是否有权限执行特定操作
 */
export function permissionMiddleware(
  permissionType: PermissionType,
  options: {
    pointsCost?: number
    requireMembership?: boolean
    customCheck?: (
      env: Env,
      userId: string,
      membershipInfo: any,
      pointsInfo: any
    ) => Promise<PermissionCheckResult>
  } = {}
) {
  return async (
    c: Context<{
      Bindings: Env
      Variables: { user: any; permissionResult?: PermissionCheckResult }
    }>,
    next: Next
  ) => {
    try {
      const supabaseUser = c.get('user')
      if (!supabaseUser) {
        throwBusinessError(ErrorCode.AUTH_UNAUTHORIZED)
      }

      // 解析本地用户ID
      const localUserId = await ensureUserExists(c.env, supabaseUser.id, supabaseUser.email || '')

      // 检查权限
      const permissionResult = await checkPermission(c.env, localUserId, permissionType, options)

      if (!permissionResult.hasPermission) {
        // 根据权限类型和检查结果直接确定错误码，不依赖文本内容
        let errorCode = ErrorCode.AUTH_FORBIDDEN

        // 根据权限类型和用户状态确定具体错误码
        if (permissionType === PermissionType.VOICE_GENERATION) {
          if (permissionResult.userType === 'free') {
            // 免费用户尝试使用语音生成 -> 需要会员权限
            errorCode = ErrorCode.MEMBERSHIP_INSUFFICIENT_LEVEL
          } else if (permissionResult.userType === 'member') {
            // 会员用户但积分不足
            errorCode = ErrorCode.INSUFFICIENT_POINTS
          }
        } else if (
          permissionType === PermissionType.IMAGE_GENERATION ||
          permissionType === PermissionType.VIDEO_GENERATION ||
          permissionType === PermissionType.SCRIPT_PURCHASE ||
          permissionType === PermissionType.GALLERY_GENERATION
        ) {
          // 积分消费类功能
          if (permissionResult.userType === 'free') {
            errorCode = ErrorCode.MEMBERSHIP_INSUFFICIENT_LEVEL
          } else {
            errorCode = ErrorCode.INSUFFICIENT_POINTS
          }
        } else if (permissionType === PermissionType.MEMBER_ONLY) {
          // 会员专属功能
          errorCode = ErrorCode.MEMBERSHIP_INSUFFICIENT_LEVEL
        }

        throwBusinessError(errorCode, {
          userType: permissionResult.userType,
          pointsAvailable: permissionResult.pointsAvailable,
          membershipInfo: permissionResult.membershipInfo,
          reason: permissionResult.reason,
          permissionType: permissionType
        })
      }

      // 将权限检查结果存储到上下文中，供后续处理使用
      c.set('permissionResult', permissionResult)

      await next()
    } catch (error) {
      // 如果是业务错误，直接抛出让全局错误处理器处理
      if (error && typeof error === 'object' && 'code' in error) {
        throw error
      }
      console.error('权限检查中间件错误:', error)
      throwBusinessError(ErrorCode.SYSTEM_ERROR, {
        originalError: error instanceof Error ? error.message : String(error)
      })
    }
  }
}

/**
 * 核心权限检查函数
 */
export async function checkPermission(
  env: Env,
  userId: string,
  permissionType: PermissionType,
  options: {
    pointsCost?: number
    requireMembership?: boolean
    customCheck?: (
      env: Env,
      userId: string,
      membershipInfo: any,
      pointsInfo: any
    ) => Promise<PermissionCheckResult>
  } = {}
): Promise<PermissionCheckResult> {
  // 获取用户会员状态和积分信息
  const [membershipInfo, pointsInfo] = await Promise.all([
    checkUserMembership(env, userId),
    getUserPoints(env, userId)
  ])

  const isMember = membershipInfo.isMember
  const userType = isMember ? 'member' : 'free'
  const pointsAvailable = pointsInfo.availablePoints

  // 如果有自定义检查函数，优先使用
  if (options.customCheck) {
    return await options.customCheck(env, userId, membershipInfo, pointsInfo)
  }

  // 如果要求必须是会员
  if (options.requireMembership && !isMember) {
    return {
      hasPermission: false,
      reason: '该功能需要会员权限',
      userType,
      pointsAvailable
    }
  }

  // 检查积分消费类型的权限
  const pointsCost =
    options.pointsCost || (PERMISSION_CONFIG.POINTS_COST as any)[permissionType] || 0

  if (pointsCost > 0) {
    if (pointsAvailable < pointsCost) {
      return {
        hasPermission: false,
        reason: `积分不足，需要${pointsCost}积分，当前可用${pointsAvailable}积分`,
        userType,
        pointsAvailable
      }
    }
  }

  // 根据具体权限类型进行检查
  switch (permissionType) {
    case PermissionType.TEXT_CHAT:
      return await checkTextChatPermission(env, userId, userType, membershipInfo)

    case PermissionType.CHARACTER_CREATE:
      return await checkCharacterCreatePermission(env, userId, userType, membershipInfo)

    case PermissionType.IMAGE_GENERATION:
      return await checkImageGenerationPermission(
        env,
        userId,
        userType,
        pointsAvailable,
        pointsCost
      )

    case PermissionType.VOICE_GENERATION:
      return await checkVoiceGenerationPermission(
        env,
        userId,
        userType,
        pointsAvailable,
        pointsCost
      )

    case PermissionType.SCRIPT_PURCHASE:
      return await checkScriptPurchasePermission(env, userId, userType, pointsAvailable, pointsCost)

    case PermissionType.VIDEO_GENERATION:
      return await checkVideoGenerationPermission(
        env,
        userId,
        userType,
        pointsAvailable,
        pointsCost
      )

    case PermissionType.GALLERY_GENERATION:
      return await checkGalleryGenerationPermission(
        env,
        userId,
        userType,
        pointsAvailable,
        pointsCost
      )

    case PermissionType.MEMBER_ONLY:
      return {
        hasPermission: isMember,
        reason: isMember ? undefined : '该功能仅限会员使用',
        userType,
        pointsAvailable,
        membershipInfo: membershipInfo.subscription
          ? {
              planId: membershipInfo.subscription.planId,
              planName: '', // 需要关联查询获取
              endDate: membershipInfo.subscription.endDate
            }
          : undefined
      }

    default:
      return {
        hasPermission: true,
        userType,
        pointsAvailable
      }
  }
}

/**
 * 文本对话权限检查
 */
async function checkTextChatPermission(
  env: Env,
  userId: string,
  userType: 'free' | 'member',
  membershipInfo: any
): Promise<PermissionCheckResult> {
  if (userType === 'member') {
    // 会员用户文本对话无限制
    return {
      hasPermission: true,
      userType,
      pointsAvailable: 0
    }
  }

  // 免费用户需要检查每日限制
  // TODO: 实现每日对话次数统计
  // 暂时返回允许，后续可以加入日志统计
  return {
    hasPermission: true,
    userType,
    pointsAvailable: 0,
    reason: '免费用户每日限制100次对话'
  }
}

/**
 * 角色创建权限检查
 */
async function checkCharacterCreatePermission(
  env: Env,
  userId: string,
  userType: 'free' | 'member',
  membershipInfo: any
): Promise<PermissionCheckResult> {
  // TODO: 实现角色数量统计查询
  // 暂时返回允许，后续需要查询用户已创建的角色数量

  if (userType === 'free') {
    return {
      hasPermission: true, // 暂时允许，需要后续实现数量检查
      userType,
      pointsAvailable: 0,
      reason: '免费用户最多创建1个角色'
    }
  }

  return {
    hasPermission: true,
    userType,
    pointsAvailable: 0
  }
}

/**
 * 图片生成权限检查
 */
async function checkImageGenerationPermission(
  env: Env,
  userId: string,
  userType: 'free' | 'member',
  pointsAvailable: number,
  pointsCost: number
): Promise<PermissionCheckResult> {
  if (userType === 'free') {
    // 免费用户需要检查每日限制
    // TODO: 实现每日生成次数统计
    return {
      hasPermission: true, // 暂时允许，需要后续实现每日限制检查
      userType,
      pointsAvailable,
      reason: '免费用户每日限制1次图片生成'
    }
  }

  // 会员用户检查积分
  if (pointsAvailable < pointsCost) {
    return {
      hasPermission: false,
      reason: `积分不足，需要${pointsCost}积分，当前可用${pointsAvailable}积分`,
      userType,
      pointsAvailable
    }
  }

  return {
    hasPermission: true,
    userType,
    pointsAvailable
  }
}

/**
 * 语音生成权限检查
 */
async function checkVoiceGenerationPermission(
  env: Env,
  userId: string,
  userType: 'free' | 'member',
  pointsAvailable: number,
  pointsCost: number
): Promise<PermissionCheckResult> {
  if (userType === 'free') {
    return {
      hasPermission: false,
      reason: '语音生成功能仅限会员使用',
      userType,
      pointsAvailable
    }
  }

  // 会员用户检查积分
  if (pointsAvailable < pointsCost) {
    return {
      hasPermission: false,
      reason: `积分不足，需要${pointsCost}积分，当前可用${pointsAvailable}积分`,
      userType,
      pointsAvailable
    }
  }

  return {
    hasPermission: true,
    userType,
    pointsAvailable
  }
}

/**
 * 剧本购买权限检查
 */
async function checkScriptPurchasePermission(
  env: Env,
  userId: string,
  userType: 'free' | 'member',
  pointsAvailable: number,
  pointsCost: number
): Promise<PermissionCheckResult> {
  if (userType === 'free') {
    return {
      hasPermission: false,
      reason: '剧本购买功能仅限会员使用',
      userType,
      pointsAvailable
    }
  }

  // 会员用户检查积分
  if (pointsAvailable < pointsCost) {
    return {
      hasPermission: false,
      reason: `积分不足，需要${pointsCost}积分，当前可用${pointsAvailable}积分`,
      userType,
      pointsAvailable
    }
  }

  return {
    hasPermission: true,
    userType,
    pointsAvailable
  }
}

/**
 * 视频生成权限检查
 */
async function checkVideoGenerationPermission(
  env: Env,
  userId: string,
  userType: 'free' | 'member',
  pointsAvailable: number,
  pointsCost: number
): Promise<PermissionCheckResult> {
  if (userType === 'free') {
    return {
      hasPermission: false,
      reason: '视频生成功能仅限会员使用',
      userType,
      pointsAvailable
    }
  }

  // 会员用户检查积分
  if (pointsAvailable < pointsCost) {
    return {
      hasPermission: false,
      reason: `积分不足，需要${pointsCost}积分，当前可用${pointsAvailable}积分`,
      userType,
      pointsAvailable
    }
  }

  return {
    hasPermission: true,
    userType,
    pointsAvailable
  }
}

/**
 * 写真集生成权限检查
 */
async function checkGalleryGenerationPermission(
  env: Env,
  userId: string,
  userType: 'free' | 'member',
  pointsAvailable: number,
  pointsCost: number
): Promise<PermissionCheckResult> {
  if (userType === 'free') {
    return {
      hasPermission: false,
      reason: '写真集生成功能仅限会员使用',
      userType,
      pointsAvailable
    }
  }

  // 会员用户检查积分
  if (pointsAvailable < pointsCost) {
    return {
      hasPermission: false,
      reason: `积分不足，需要${pointsCost}积分，当前可用${pointsAvailable}积分`,
      userType,
      pointsAvailable
    }
  }

  return {
    hasPermission: true,
    userType,
    pointsAvailable
  }
}

/**
 * 简化的权限检查函数 - 仅检查会员状态
 * @param env 环境变量
 * @param localUserId 本地数据库用户ID（不是Supabase用户ID）
 */
export async function checkMembershipOnly(env: Env, localUserId: string): Promise<boolean> {
  const membershipInfo = await checkUserMembership(env, localUserId)
  return membershipInfo.isMember
}

/**
 * 简化的积分检查函数
 * @param env 环境变量
 * @param localUserId 本地数据库用户ID（不是Supabase用户ID）
 */
export async function checkPointsBalance(
  env: Env,
  localUserId: string,
  requiredPoints: number
): Promise<boolean> {
  const pointsInfo = await getUserPoints(env, localUserId)
  return pointsInfo.availablePoints >= requiredPoints
}
