import type { Context, Next } from 'hono'
import {
  BusinessError,
  isBusinessError,
  ErrorCode,
  getHttpStatusFromErrorCode
} from '../types/errors'
import { createErrorResponse } from '../types/responses'
import { getErrorMessage } from '../i18n/messages/errors'

/**
 * 生成请求ID的辅助函数
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
}

/**
 * 从上下文中获取或生成请求ID
 */
function getRequestId(c: Context): string {
  // 尝试从请求头获取
  const headerRequestId = c.req.header('x-request-id') || c.req.header('request-id')
  if (headerRequestId) {
    return headerRequestId
  }

  // 尝试从上下文变量获取
  const contextRequestId = c.get('requestId')
  if (contextRequestId) {
    return contextRequestId
  }

  // 生成新的请求ID并保存到上下文
  const newRequestId = generateRequestId()
  c.set('requestId', newRequestId)
  return newRequestId
}

/**
 * 从上下文中获取当前语言
 */
function getLanguageFromContext(c: Context): string {
  return c.get('language') || 'zh'
}

/**
 * 判断是否为开发环境
 */
function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development'
}

/**
 * 全局错误处理中间件
 * 统一处理所有未捕获的错误并返回标准格式的错误响应
 */
export function globalErrorHandler() {
  return async (c: Context, next: Next) => {
    try {
      await next()
    } catch (error) {
      const requestId = getRequestId(c)
      const language = getLanguageFromContext(c)

      console.error(`[${requestId}] 全局错误处理:`, error)

      // 处理业务错误
      if (isBusinessError(error)) {
        const httpStatus = error.statusCode || getHttpStatusFromErrorCode(error.code)
        const errorMessage = getErrorMessage(error.code, language, error.data)

        const response = createErrorResponse(
          errorMessage,
          error.code,
          error.data,
          requestId,
          isDevelopment() ? error.stack : undefined
        )

        return c.json(response, httpStatus as any)
      }

      // 处理 Hono 的 HTTPException
      if (error instanceof Error && 'status' in error) {
        const httpError = error as any
        const status = httpError.status || 500
        const message = httpError.message || 'Unknown error'

        // 尝试映射到对应的错误码
        let errorCode: ErrorCode = ErrorCode.SYSTEM_ERROR
        if (status === 401) {
          errorCode = ErrorCode.AUTH_UNAUTHORIZED
        } else if (status === 403) {
          errorCode = ErrorCode.AUTH_FORBIDDEN
        } else if (status === 404) {
          errorCode = ErrorCode.RESOURCE_NOT_FOUND
        } else if (status === 429) {
          errorCode = ErrorCode.RATE_LIMIT_EXCEEDED
        } else if (status >= 500) {
          errorCode = ErrorCode.INTERNAL_SERVER_ERROR
        }

        const errorMessage = getErrorMessage(errorCode, language)
        const response = createErrorResponse(
          errorMessage,
          errorCode,
          { originalMessage: message },
          requestId,
          isDevelopment() ? error.stack : undefined
        )

        return c.json(response, status)
      }

      // 处理其他类型的错误
      let errorMessage = 'Unknown error occurred'
      let errorCode = ErrorCode.SYSTEM_ERROR
      let errorData: Record<string, any> | undefined

      if (error instanceof Error) {
        errorMessage = error.message
        errorData = { name: error.name }

        // 根据错误消息尝试识别错误类型
        if (error.message.includes('validation') || error.message.includes('invalid')) {
          errorCode = ErrorCode.VALIDATION_ERROR
        } else if (error.message.includes('not found')) {
          errorCode = ErrorCode.RESOURCE_NOT_FOUND
        } else if (
          error.message.includes('unauthorized') ||
          error.message.includes('authentication')
        ) {
          errorCode = ErrorCode.AUTH_UNAUTHORIZED
        } else if (error.message.includes('forbidden') || error.message.includes('permission')) {
          errorCode = ErrorCode.AUTH_FORBIDDEN
        }
      }

      const localizedMessage = getErrorMessage(errorCode, language, errorData)
      const response = createErrorResponse(
        localizedMessage,
        errorCode,
        errorData,
        requestId,
        isDevelopment() ? (error as Error)?.stack : undefined
      )

      return c.json(response, 500 as any)
    }
  }
}

/**
 * 请求ID中间件
 * 为每个请求生成或获取请求ID
 */
export function requestIdMiddleware() {
  return async (c: Context, next: Next) => {
    const requestId = getRequestId(c)

    // 设置响应头
    c.header('x-request-id', requestId)

    await next()
  }
}

/**
 * 手动抛出业务错误的辅助函数
 * 可以在路由处理函数中使用
 */
export function throwBusinessError(
  code: ErrorCode,
  data?: Record<string, any>,
  statusCode?: number,
  message?: string
): never {
  throw new BusinessError(code, data, statusCode, message)
}

/**
 * 创建业务错误响应的辅助函数
 * 用于在路由中直接返回错误响应而不抛出异常
 */
export function createBusinessErrorResponse(
  c: Context,
  code: ErrorCode,
  data?: Record<string, any>,
  statusCode?: number
) {
  const language = getLanguageFromContext(c)
  const requestId = getRequestId(c)
  const httpStatus = statusCode || getHttpStatusFromErrorCode(code)
  const errorMessage = getErrorMessage(code, language, data)

  const response = createErrorResponse(errorMessage, code, data, requestId)

  return c.json(response, httpStatus as any)
}
