import { z } from 'zod'
import { Context } from 'hono'
import { throwBusinessError } from '@/middleware/global-error-handler'
import { ErrorCode } from '@/types/errors'
import type { SupportedLanguage } from '@/i18n/config'

// 创建国际化验证中间件
export const createI18nValidator = (schema: z.ZodSchema) => {
  return async (c: Context<{
    Variables: {
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>, next: () => Promise<void>) => {
    try {
      const data = await c.req.json()
      schema.parse(data)
      await next()
    } catch (error) {
      if (error instanceof z.ZodError) {
        const t = c.get('t')
        const firstError = error.errors[0]
        let message = t('invalid_input')

        // 根据错误类型和字段提供具体的国际化消息
        if (firstError.path.includes('email')) {
          if (firstError.code === 'invalid_string' && firstError.validation === 'email') {
            message = t('email_invalid')
          }
        } else if (firstError.path.includes('password')) {
          if (firstError.code === 'too_small') {
            message = t('password_min_length', { length: firstError.minimum.toString() })
          }
        } else if (firstError.path.includes('code')) {
          if (firstError.code === 'too_small') {
            message = t('code_min_length', { length: firstError.minimum.toString() })
          }
        } else if (firstError.path.includes('refresh_token')) {
          message = t('refresh_token_required')
        }

        throwBusinessError(ErrorCode.VALIDATION_ERROR, { message })
      }
      throw error
    }
  }
}