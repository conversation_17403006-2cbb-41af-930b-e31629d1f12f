import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'
import type { Env } from '@/types/env'
import { ApiError } from '@/types/api'
import { BusinessError, getHttpStatusFromErrorCode } from '@/types/errors'
import { getErrorMessage } from '@/i18n/messages/errors'

import adminRoutes from './modules/admin'
import appRoutes from './modules/app'

import { queueHandler } from '@/queues'
import { scheduledHandler } from '@/utils/scheduled'
import { languageMiddleware } from '@/middleware/language'

// 创建 Hono 应用实例
const app = new Hono<{ Bindings: Env }>()

// 全局中间件
app.use('*', logger())
app.use('*', prettyJSON())
app.use('*', languageMiddleware)
app.use(
  '*',
  cors({
    origin: origin => {
      // 允许的源列表
      const allowedOrigins = [
        'http://localhost:5173',
        'http://localhost:3000',
        'https://localhost',
        'https://localhost:5173',
        'http://*************:5173',
        'https://*************:5173',
        'http://*************:5173',
        'https://*************:5173',
        'https://localhost:3000',
        'capacitor://localhost',
        'ionic://localhost',
        'file://',
        // 生产环境域名
        'https://pleasurehub.app',
        'https://www.pleasurehub.app',
        'https://api.pleasurehub.app',
        'https://demo.pleasurehub.app'
      ]

      // 如果没有 origin（比如直接访问），允许
      if (!origin) return null

      // 检查是否在允许列表中
      if (allowedOrigins.includes(origin)) return origin

      // 开发环境允许 localhost 的任何端口
      if (origin.match(/^https?:\/\/localhost(:\d+)?$/)) return origin

      // 默认拒绝
      return null
    },
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization'],
    credentials: true // 支持 credentials
  })
)

// 健康检查端点
app.get('/health', c => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'pleasurehub-backend-service'
  })
})

// API 路由组
const api = new Hono<{ Bindings: Env }>()

// 只保留模块路由挂载
api.route('/admin', adminRoutes)
api.route('/app', appRoutes)

// 挂载 API 路由
app.route('/api', api)

// 404 处理
app.notFound(c => {
  return c.json(
    {
      error: 'Not Found',
      message: `Route ${c.req.method} ${c.req.path} not found`
    },
    404
  )
})

// 全局错误处理
app.onError((err, c) => {
  console.error('Global error handler:', err)

  // 处理 BusinessError
  if (err instanceof BusinessError) {
    const httpStatus = err.statusCode || getHttpStatusFromErrorCode(err.code)
    const language = c.get('language') || 'zh'
    const errorMessage = getErrorMessage(err.code, language, err.data)

    return c.json(
      {
        success: false,
        error: errorMessage,
        code: err.code,
        data: err.data,
        timestamp: err.timestamp
      },
      httpStatus as any
    )
  }

  // 处理 ApiError
  if (err instanceof ApiError) {
    return c.json(
      {
        error: err.message,
        code: err.code
      },
      err.statusCode as any
    )
  }

  return c.json(
    {
      error: 'Internal Server Error'
    },
    500
  )
})




// 按照 Cloudflare Workers 要求的格式导出
const worker = {
  fetch: app.fetch.bind(app),
  queue: queueHandler,
  scheduled: scheduledHandler
}

export default worker
