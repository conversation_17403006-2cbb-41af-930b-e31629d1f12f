import type { Env } from '@/types/env'
import type {
  ImageGenerationTask,
  ImageGenerationProgress,
  ImageAttachment,
  GeneratingStatusAttachment,
  CompletedImageAttachment
} from '@/types/image'

import { getMessageById, updateMessageAttachments } from '@/modules/app/chat/repositories/message.repository'
import { QueueErrorHandler } from '../utils/queue-handler'
import { updateMediaGeneration, getMediaGenerationById } from '@/lib/db/queries/media-generation'
import { createServicePointsManager } from '@/lib/membership/service-points'
import { RunPodImageService } from '@/lib/services/runpod-image-service'

/**
 * RunPod 图片队列消费者
 * 处理图片生成任务，使用 RunPod API（包含生图和换脸）
 */
export class ImageRunpodQueueConsumer {
  constructor(private env: Env) {}

  /**
   * 处理单个图片生成任务
   */
  async process(task: ImageGenerationTask): Promise<void> {
    console.log('🎨 [RunPod] 开始生成:', task.taskId)

    // 获取媒体生成记录ID
    const mediaGenerationId = task.metadata?.mediaGenerationId

    if (mediaGenerationId) {
      // 更新媒体生成记录为处理中状态
      try {
        await updateMediaGeneration(this.env, mediaGenerationId, {
          status: 'processing'
        })
      } catch (error) {
        console.error('❌ [RunPod] 更新失败:', error)
      }
    }

    try {
      // 第一步：使用 RunPod 生成图片
      console.log('🎨 [RunPod] 第一步：生成图片')
      await this.updateGenerationProgress(
        task.messageId,
        {
          status: 'starting',
          progress: 10,
          message: '正在生成图片...'
        },
        mediaGenerationId
      )

      const generatedImageUrl = await this.generateImageWithRunPod(task, mediaGenerationId)

      await this.updateGenerationProgress(
        task.messageId,
        {
          status: 'processing',
          progress: 80,
          message: '正在上传...'
        },
        mediaGenerationId
      )

      // 上传到 R2 获取永久链接
      const runPodService = new RunPodImageService(this.env)
      const r2ImageUrl = await runPodService.downloadAndUploadToR2(
        generatedImageUrl,
        task.taskId,
        'generated-images-runpod'
      )

      // 更新完成状态，使用 R2 URL
      await this.updateCompletedImage(task.messageId, r2ImageUrl, task.taskId)

      // 更新媒体生成记录为完成状态，使用 R2 URL
      if (mediaGenerationId) {
        try {
          await updateMediaGeneration(this.env, mediaGenerationId, {
            status: 'completed',
            outputUrls: [r2ImageUrl], // 使用 R2 URL 而不是临时 URL
            completedAt: new Date()
          })
        } catch (error) {
          console.error('❌ [RunPod] 更新失败:', error)
        }
      }

      console.log('✅ [RunPod] 生成完成')
    } catch (error) {
      const errorMessage = QueueErrorHandler.handleTaskError(task.taskId, error)
      await this.updateFailureStatus(task.messageId, errorMessage)

      // 更新媒体生成记录为失败状态
      if (mediaGenerationId) {
        try {
          await updateMediaGeneration(this.env, mediaGenerationId, {
            status: 'failed',
            errorMessage
          })
        } catch (updateError) {
          console.error('❌ [RunPod] 更新失败:', updateError)
        }
      }

      // 生成失败，退还积分
      await this.refundPointsForFailedGeneration(task.userId, task.taskId)

      throw new Error(`[RunPod] 生成失败: ${errorMessage}`)
    }
  }

  /**
   * 使用 RunPod 生成图片（使用公共服务）
   */
  private async generateImageWithRunPod(
    task: ImageGenerationTask,
    mediaGenerationId?: string
  ): Promise<string> {
    const runPodService = new RunPodImageService(this.env)

    // 准备参数
    const params = {
      prompt: task.prompt,
      characterAvatar: task.characterAvatar,
      width: task.metadata?.width,
      height: task.metadata?.height
    }

    // 进度回调
    const progressCallback = async (progress: number, message: string) => {
      await this.updateGenerationProgress(
        task.messageId,
        {
          status: 'processing',
          progress,
          message
        },
        mediaGenerationId
      )
    }

    // 使用服务生成图片
    return await runPodService.generateImage(params, progressCallback)
  }

  /**
   * 更新生成进度
   */
  private async updateGenerationProgress(
    messageId: string,
    progress: Omit<ImageGenerationProgress, 'messageId' | 'timestamp'>,
    mediaGenerationId?: string
  ): Promise<void> {
    console.log('📊 [RunPod] 更新进度:', progress.progress + '%')

    // 如果有媒体生成记录ID，更新进度到数据库
    if (mediaGenerationId) {
      try {
        // 先获取现有的媒体生成记录，保留原有的 metadata
        const currentRecord = await getMediaGenerationById(this.env, mediaGenerationId)
        const existingMetadata = currentRecord?.metadata || {}

        await updateMediaGeneration(this.env, mediaGenerationId, {
          metadata: {
            ...existingMetadata, // 保留原有的 metadata
            progress: progress.progress,
            status: progress.status,
            message: progress.message,
            timestamp: new Date().toISOString()
          }
        })
        console.log('📊 [RunPod] 媒体生成记录进度已更新:', progress.progress + '%')
      } catch (error) {
        console.warn('⚠️ [RunPod] 更新媒体生成记录进度失败:', error)
      }
    }

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ [RunPod] 消息不存在')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: ImageAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as ImageAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除之前的生成状态附件
      const filteredAttachments = existingAttachments.filter(
        (att: ImageAttachment) => !att.contentType?.startsWith('image/generating')
      )

      // 添加新的状态附件
      const statusAttachment: GeneratingStatusAttachment = {
        url: `generating://${progress.status}`,
        name: progress.message,
        contentType: 'image/generating',
        metadata: {
          status: progress.status,
          progress: progress.progress,
          timestamp: new Date().toISOString(),
          taskId: messageId // 使用 messageId 作为 taskId
        }
      }

      const updatedAttachments = [...filteredAttachments, statusAttachment]

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: updatedAttachments
      })
    } catch (error) {
      console.warn('⚠️ [RunPod] 更新进度失败')
    }
  }

  /**
   * 更新完成的图片
   */
  private async updateCompletedImage(
    messageId: string,
    imageUrl: string,
    taskId: string
  ): Promise<void> {
    console.log('🎉 [RunPod] 图片完成')

    // imageUrl 已经是 R2 URL，直接使用
    const finalImageUrl = imageUrl

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ [RunPod] 消息不存在')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: ImageAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as ImageAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除生成状态附件，添加真实图片附件
      const filteredAttachments = existingAttachments.filter(
        (att: ImageAttachment) => !att.contentType?.startsWith('image/generating')
      )

      const imageAttachment: CompletedImageAttachment = {
        url: finalImageUrl,
        name: `generated-image-${Date.now()}.png`,
        contentType: 'image/png',
        metadata: {
          taskId,
          generatedAt: new Date().toISOString()
        }
      }

      const updatedAttachments = [...filteredAttachments, imageAttachment]

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: updatedAttachments
      })
    } catch (error) {
      console.error('❌ [RunPod] 更新图片失败')
      throw error
    }
  }

  /**
   * 更新失败状态
   */
  private async updateFailureStatus(messageId: string, error: string): Promise<void> {
    console.log('❌ [RunPod] 失败:', error)

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ [RunPod] 消息不存在')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: ImageAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as ImageAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除生成状态附件
      const filteredAttachments = existingAttachments.filter(
        (att: ImageAttachment) => !att.contentType?.startsWith('image/generating')
      )

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: filteredAttachments
      })
    } catch (updateError) {
      console.warn('⚠️ [RunPod] 更新状态失败')
    }
  }

  /**
   * 退还积分（生成失败时）
   */
  private async refundPointsForFailedGeneration(userId: string, taskId: string): Promise<void> {
    try {
      const pointsManager = createServicePointsManager(this.env)

      // 图片生成固定消费10积分
      const pointsToRefund = 10

      // 使用积分退还方法
      const refundResult = await pointsManager.refundPoints(userId, {
        amount: pointsToRefund,
        source: 'refund',
        sourceId: taskId,
        description: `图片生成失败退还 - 任务ID: ${taskId}`
      })

      if (refundResult.success) {
        console.log(`✅ [RunPod] 退还积分 ${pointsToRefund}`)
      } else {
        console.error(`❌ [RunPod] 退还失败: ${refundResult.error}`)
      }
    } catch (error) {
      console.error('[RunPod] 退还异常:', error)
    }
  }
}
