import type { Env } from '@/types/env'
import type { FaceSwapTask, FaceSwapProgress } from '@/types/face-swap'
import type {
  ImageAttachment,
  GeneratingStatusAttachment,
  CompletedImageAttachment
} from '@/types/image'
import { getMessageById, updateMessageAttachments } from '../../modules/app/chat/repositories/message.repository'
import { QueueErrorHandler } from '../utils/queue-handler'
import { updateMediaGeneration, getMediaGenerationById } from '@/lib/db/queries/media-generation'
import { createServicePointsManager } from '@/lib/membership/service-points'
import { RunPodImageService } from '@/lib/services/runpod-image-service'

/**
 * 换脸队列消费者
 * 处理换脸任务，使用 RunPod API（包含生图和换脸）
 */
export class FaceSwapQueueConsumer {
  constructor(private env: Env) {}

  /**
   * 检查字符串是否是有效的 UUID 格式
   */
  private isValidUUID(str: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    return uuidRegex.test(str)
  }

  /**
   * 处理单个换脸任务
   */
  async process(task: FaceSwapTask): Promise<void> {
    console.log('🔄 开始处理任务:', task.taskId)

    // 获取媒体生成记录ID
    const mediaGenerationId = task.metadata?.mediaGenerationId

    if (mediaGenerationId) {
      // 更新媒体生成记录为处理中状态
      try {
        await updateMediaGeneration(this.env, mediaGenerationId, {
          status: 'processing'
        })
      } catch (error) {
        console.error('❌ 更新失败:', error)
      }
    }

    try {
      // 使用 RunPod 直接处理生图+换脸
      console.log('🎨 [RunPod] 开始图片处理')
      await this.updateFaceSwapProgress(
        task.messageId,
        {
          status: 'starting',
          progress: 10,
          message: '正在生成图片...'
        },
        mediaGenerationId
      )

      const generatedImageUrl = await this.generateImageWithRunPod(task, mediaGenerationId)

      await this.updateFaceSwapProgress(
        task.messageId,
        {
          status: 'processing',
          progress: 80,
          message: '正在上传...'
        },
        mediaGenerationId
      )

      // 上传到 R2 获取永久链接
      const runPodService = new RunPodImageService(this.env)
      const r2ImageUrl = await runPodService.downloadAndUploadToR2(
        generatedImageUrl,
        task.taskId,
        'face-swap-images'
      )

      // 更新完成状态，使用 R2 URL
      await this.updateCompletedImage(task.messageId, r2ImageUrl, task.taskId)

      // 更新媒体生成记录为完成状态，使用 R2 URL
      if (mediaGenerationId) {
        try {
          // 先获取当前记录以保留原有 metadata
          const currentRecord = await getMediaGenerationById(this.env, mediaGenerationId)
          const existingMetadata = currentRecord?.metadata || {}

          await updateMediaGeneration(this.env, mediaGenerationId, {
            status: 'completed',
            outputUrls: [r2ImageUrl],
            completedAt: new Date(),
            metadata: {
              ...existingMetadata, // 保留原有 metadata
              progress: 100,
              status: 'completed',
              message: '生成完成',
              timestamp: new Date().toISOString()
            }
          })
          console.log('✅ [RunPod] 媒体生成记录状态已更新为完成')
        } catch (error) {
          console.error('❌ [RunPod] 更新失败:', error)
        }
      }

      console.log('✅ [RunPod] 处理完成')
    } catch (error) {
      const errorMessage = QueueErrorHandler.handleTaskError(task.taskId, error)
      await this.updateFailureStatus(task.messageId, errorMessage)

      // 更新媒体生成记录为失败状态
      if (mediaGenerationId) {
        try {
          // 先获取当前记录以保留原有 metadata
          const currentRecord = await getMediaGenerationById(this.env, mediaGenerationId)
          const existingMetadata = currentRecord?.metadata || {}

          await updateMediaGeneration(this.env, mediaGenerationId, {
            status: 'failed',
            errorMessage,
            metadata: {
              ...existingMetadata, // 保留原有 metadata
              status: 'failed',
              message: errorMessage,
              timestamp: new Date().toISOString()
            }
          })
          console.log('❌ [RunPod] 媒体生成记录状态已更新为失败')
        } catch (updateError) {
          console.error('❌ 更新失败:', updateError)
        }
      }

      // 图片处理失败，退还积分
      await this.refundPointsForFailedGeneration(task.userId, task.taskId)

      throw new Error(`图片处理失败: ${errorMessage}`)
    }
  }

  /**
   * 使用 RunPod 生成图片（使用公共服务）
   */
  private async generateImageWithRunPod(
    task: FaceSwapTask,
    mediaGenerationId?: string
  ): Promise<string> {
    const runPodService = new RunPodImageService(this.env)

    // 准备参数
    const params = {
      prompt: task.metadata?.originalPrompt || '',
      characterAvatar: task.swapImageUrl,
      width: task.metadata?.width,
      height: task.metadata?.height
    }

    // 进度回调
    const progressCallback = async (progress: number, message: string) => {
      await this.updateFaceSwapProgress(
        task.messageId,
        {
          status: 'processing',
          progress,
          message
        },
        mediaGenerationId
      )
    }

    // 使用服务生成图片
    const imageUrl = await runPodService.generateImage(params, progressCallback)

    return imageUrl
  }

  /**
   * 更新换脸进度
   */
  private async updateFaceSwapProgress(
    messageId: string,
    progress: Omit<FaceSwapProgress, 'messageId' | 'timestamp'>,
    mediaGenerationId?: string
  ): Promise<void> {
    console.log('📊 更新进度:', progress.progress + '%')

    // 如果有媒体生成记录ID，更新进度到数据库
    if (mediaGenerationId) {
      try {
        // 先获取现有的媒体生成记录，保留原有的 metadata
        const currentRecord = await getMediaGenerationById(this.env, mediaGenerationId)
        const existingMetadata = currentRecord?.metadata || {}

        await updateMediaGeneration(this.env, mediaGenerationId, {
          metadata: {
            ...existingMetadata, // 保留原有的 metadata
            progress: progress.progress,
            status: progress.status,
            message: progress.message,
            timestamp: new Date().toISOString()
          }
        })
        console.log('📊 媒体生成记录进度已更新:', progress.progress + '%')
      } catch (error) {
        console.warn('⚠️ 更新媒体生成记录进度失败:', error)
      }
    }

    // 检查是否是 UUID 格式，如果不是则跳过消息更新
    if (!this.isValidUUID(messageId)) {
      console.log('📊 非UUID格式的messageId，跳过消息更新（可能是写真集生成任务）')
      return
    }

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ 消息不存在，跳过消息更新')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: ImageAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as ImageAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除之前的生成状态附件
      const filteredAttachments = existingAttachments.filter(
        (att: ImageAttachment) => !att.contentType?.startsWith('image/generating')
      )

      // 添加新的状态附件
      const statusAttachment: GeneratingStatusAttachment = {
        url: `generating://${progress.status}`,
        name: progress.message,
        contentType: 'image/generating',
        metadata: {
          status: progress.status as any,
          progress: progress.progress,
          timestamp: new Date().toISOString(),
          taskId: messageId // 使用 messageId 作为 taskId
        }
      }

      const updatedAttachments = [...filteredAttachments, statusAttachment]

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: updatedAttachments
      })
    } catch (error) {
      console.warn('⚠️ 更新进度失败')
    }
  }

  /**
   * 更新完成的图片
   */
  private async updateCompletedImage(
    messageId: string,
    imageUrl: string,
    taskId: string
  ): Promise<void> {
    console.log('🎉 图片生成完成')

    // 检查是否是 UUID 格式，如果不是则跳过消息更新
    if (!this.isValidUUID(messageId)) {
      console.log('🎉 非UUID格式的messageId，跳过消息更新（可能是写真集生成任务）')
      return
    }

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ 消息不存在，跳过消息更新')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: ImageAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as ImageAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除生成状态附件，添加真实图片附件
      const filteredAttachments = existingAttachments.filter(
        (att: ImageAttachment) => !att.contentType?.startsWith('image/generating')
      )

      const imageAttachment: CompletedImageAttachment = {
        url: imageUrl,
        name: `face-swap-${Date.now()}.png`,
        contentType: 'image/png',
        metadata: {
          taskId,
          generatedAt: new Date().toISOString()
        }
      }

      const updatedAttachments = [...filteredAttachments, imageAttachment]

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: updatedAttachments
      })
    } catch (error) {
      console.error('❌ 更新图片失败')
      throw error
    }
  }

  /**
   * 更新失败状态
   */
  private async updateFailureStatus(messageId: string, error: string): Promise<void> {
    console.log('❌ 失败:', error)

    // 检查是否是 UUID 格式，如果不是则跳过消息更新
    if (!this.isValidUUID(messageId)) {
      console.log('❌ 非UUID格式的messageId，跳过消息更新（可能是写真集生成任务）')
      return
    }

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ 消息不存在，跳过消息更新')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: ImageAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as ImageAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除生成状态附件
      const filteredAttachments = existingAttachments.filter(
        (att: ImageAttachment) => !att.contentType?.startsWith('image/generating')
      )

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: filteredAttachments
      })
    } catch (updateError) {
      console.warn('⚠️ 更新状态失败')
    }
  }

  /**
   * 退还积分（图片处理失败时）
   */
  private async refundPointsForFailedGeneration(userId: string, taskId: string): Promise<void> {
    try {
      const pointsManager = createServicePointsManager(this.env)

      // 图片处理固定消费10积分
      const pointsToRefund = 10

      // 使用积分退还方法
      const refundResult = await pointsManager.refundPoints(userId, {
        amount: pointsToRefund,
        source: 'refund',
        sourceId: taskId,
        description: `图片处理失败退还 - 任务ID: ${taskId}`
      })

      if (refundResult.success) {
        console.log(`✅ 退还积分 ${pointsToRefund}`)
      } else {
        console.error(`❌ 退还失败: ${refundResult.error}`)
      }
    } catch (error) {
      console.error('退还异常:', error)
    }
  }
}
