import type { Env } from '@/types/env'
import type { AudioProcessingTask } from '@/types/audio'
import { uploadToR2, IMAGE_UPLOAD_OPTIONS, getR2ConfigFromEnv } from '@/lib/utils/r2-upload'
import { updateMessageAttachments } from '@/modules/app/chat/repositories/message.repository'
import { QueueErrorHandler } from '../utils/queue-handler'

/**
 * 音频队列消费者
 * 处理 TTS 音频的上传和消息更新
 */
export class AudioQueueConsumer {
  constructor(private env: Env) {}

  /**
   * 处理单个音频任务
   */
  async process(task: AudioProcessingTask): Promise<void> {
    console.log('📦 处理音频任务:', task.taskId)
    console.log('📊 任务详情:', {
      taskId: task.taskId,
      messageId: task.messageId,
      chatId: task.chatId,
      textLength: task.text?.length || 0,
      audioUrl: task.audioUrl,
      isTemporary: task.isTemporary
    })

    try {
      let finalAudioUrl: string

      if (task.isTemporary) {
        console.log('🔄 处理临时音频文件...')
        // 从临时URL下载音频
        const audioBuffer = await this.downloadAudioFromUrl(task.audioUrl)
        console.log('📥 临时音频下载完成，大小:', audioBuffer.byteLength, 'bytes')

        // 上传到最终位置
        finalAudioUrl = await this.uploadAudioToR2(audioBuffer, task.taskId)
        console.log('📤 音频已上传到最终位置:', finalAudioUrl)

        // 清理临时文件
        await this.cleanupTempFile(task.audioUrl)
        console.log('🗑️ 临时文件已清理:', task.audioUrl)
      } else {
        // 兼容旧的处理方式（如果还有遗留任务）
        console.log('⚠️ 处理旧格式任务，直接使用URL:', task.audioUrl)
        finalAudioUrl = task.audioUrl
      }

      // 更新消息附件（如果有messageId）
      if (task.messageId && task.chatId) {
        console.log('🔄 开始更新消息附件...')
        await this.updateMessageWithAudio(task.messageId, finalAudioUrl)
        console.log('💾 消息附件已更新:', task.messageId)
      }

      console.log('✅ 音频任务处理完成:', task.taskId)
    } catch (error) {
      console.error('❌ 音频任务处理失败:', task.taskId, error)
      const errorMessage = QueueErrorHandler.handleTaskError(task.taskId, error)
      throw new Error(`音频处理失败: ${errorMessage}`)
    }
  }

  /**
   * 转换音频数据格式
   */
  private async convertAudioData(audioData: ArrayBuffer | string): Promise<ArrayBuffer> {
    if (typeof audioData === 'string') {
      // 从Base64转换为ArrayBuffer，分块处理避免栈溢出
      console.log('🔄 开始Base64解码，数据长度:', audioData.length)

      try {
        // 使用更高效的方法：直接使用 Uint8Array.from 和 atob
        // 但对于大文件，我们需要分块处理
        const chunkSize = 1024 * 1024 // 1MB chunks
        const chunks: Uint8Array[] = []

        for (let i = 0; i < audioData.length; i += chunkSize) {
          const chunk = audioData.slice(i, i + chunkSize)
          const binaryString = atob(chunk)
          const bytes = new Uint8Array(binaryString.length)

          for (let j = 0; j < binaryString.length; j++) {
            bytes[j] = binaryString.charCodeAt(j)
          }

          chunks.push(bytes)
        }

        // 合并所有块
        const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0)
        const result = new Uint8Array(totalLength)
        let offset = 0

        for (const chunk of chunks) {
          result.set(chunk, offset)
          offset += chunk.length
        }

        console.log('✅ Base64解码完成，输出长度:', result.length)
        return result.buffer
      } catch (error) {
        console.error('❌ Base64解码失败:', error)
        throw new Error(`Base64解码失败: ${error instanceof Error ? error.message : String(error)}`)
      }
    }
    return audioData
  }

  /**
   * 从URL下载音频文件
   */
  private async downloadAudioFromUrl(url: string): Promise<ArrayBuffer> {
    try {
      console.log('📥 开始下载音频文件:', url)

      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`下载失败: HTTP ${response.status}`)
      }

      const audioBuffer = await response.arrayBuffer()
      console.log('✅ 音频文件下载完成，大小:', audioBuffer.byteLength, 'bytes')

      return audioBuffer
    } catch (error) {
      console.error('❌ 下载音频文件失败:', error)
      throw new Error(`下载音频文件失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 清理临时文件
   */
  private async cleanupTempFile(tempUrl: string): Promise<void> {
    try {
      console.log('🗑️ 开始清理临时文件:', tempUrl)

      // 从URL中提取文件路径
      const urlParts = new URL(tempUrl)
      const pathParts = urlParts.pathname.split('/')
      const fileName = pathParts[pathParts.length - 1]
      const folderPath = pathParts.slice(1, -1).join('/')

      // 获取R2配置
      const r2Config = getR2ConfigFromEnv(this.env)
      if (!r2Config) {
        console.warn('⚠️ R2配置不完整，无法清理临时文件')
        return
      }

      // 构建完整的对象键
      const objectKey = folderPath ? `${folderPath}/${fileName}` : fileName

      // 删除R2对象
      await this.env.BUCKET.delete(objectKey)
      console.log('✅ 临时文件已清理:', objectKey)
    } catch (error) {
      console.error('❌ 清理临时文件失败:', error)
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 上传音频到 R2
   */
  private async uploadAudioToR2(audioBuffer: ArrayBuffer, taskId: string): Promise<string> {
    const r2Config = getR2ConfigFromEnv(this.env)
    if (!r2Config) {
      throw new Error('R2配置不完整')
    }

    const fileName = `tts_${taskId}.mp3`
    const uploadOptions = {
      ...IMAGE_UPLOAD_OPTIONS,
      fileName,
      folder: 'audio',
      allowedTypes: [
        'audio/mpeg',
        'audio/mp3',
        'audio/wav',
        'audio/ogg',
        'application/octet-stream'
      ],
      maxSize: 50 * 1024 * 1024 // 50MB
    }

    const result = await uploadToR2(audioBuffer, r2Config, uploadOptions)

    if (!result.success || !result.url) {
      throw new Error(result.error || '上传音频文件失败')
    }

    return result.url
  }

  /**
   * 更新消息附件（追加音频附件，保留现有附件）
   */
  private async updateMessageWithAudio(messageId: string, audioUrl: string): Promise<void> {
    await updateMessageAttachments(this.env, {
      messageId,
      attachments: [
        {
          url: audioUrl,
          name: '语音',
          contentType: 'audio/mpeg'
        }
      ]
    })
  }
}
