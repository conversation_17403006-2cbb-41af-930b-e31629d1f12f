import { Hono } from 'hono'
import type { Env } from '@/types/env'
import adminConfigRoutes from '@/routes/admin/admin-config'
import adminReferralRoutes from '@/routes/admin/admin-referral'
import adminUsersRoutes from '@/routes/admin/admin-users'
import adminMembershipRoutes from '@/routes/admin/admin-membership'
import adminMarketingRoutes from '@/routes/admin/admin-marketing'
import adminDevicesRoutes from '@/routes/admin/admin-devices'
import adminDeviceCommandSetsRoutes from '@/routes/admin/admin-device-command-sets'
import adminDeviceFunctionsRoutes from '@/routes/admin/admin-device-functions'
import adminDeviceModesRoutes from '@/routes/admin/admin-device-modes'
import adminOrdersRoutes from '@/routes/admin/admin-orders'
import adminPointsRoutes from '@/routes/admin/admin-points'
import adminScriptsRoutes from '@/routes/admin/admin-scripts'
import adminTemplatesRoutes from '@/routes/admin/admin-templates'
import adminCharactersRoutes from '@/routes/admin/admin-characters'

const admin = new Hono<{ Bindings: Env }>()
admin.route('/config', adminConfigRoutes)
admin.route('/referral', adminReferralRoutes)
admin.route('/users', adminUsersRoutes)
admin.route('/membership', adminMembershipRoutes)
admin.route('/marketing', adminMarketingRoutes)
admin.route('/devices', adminDevicesRoutes)
admin.route('/devices/command-sets', adminDeviceCommandSetsRoutes)
admin.route('/devices/functions', adminDeviceFunctionsRoutes)
admin.route('/devices/modes', adminDeviceModesRoutes)
admin.route('/orders', adminOrdersRoutes)
admin.route('/points', adminPointsRoutes)
admin.route('/content/scripts', adminScriptsRoutes)
admin.route('/content/templates', adminTemplatesRoutes)
admin.route('/characters', adminCharactersRoutes)

export default admin