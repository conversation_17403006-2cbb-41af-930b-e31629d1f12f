import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, handleSupabaseSingleResult } from '@/lib/db/supabase-types'
import type { Env } from '@/types/env'
import type { Chat } from '@/lib/db/schema'

const TABLE_NAMES = {
  chat: 'chat',
  message: 'message'
}

export async function getChatFromDB(env: Env, chatId: string): Promise<Chat | null> {
  const supabase = getSupabase(env)
  const result = await supabase
    .from(TABLE_NAMES.chat)
    .select('*')
    .eq('id', chatId)
    .single()

  const { data, error } = handleSupabaseSingleResult(result)
  if (error) return null
  return data
}

export async function createChatInDB(env: Env, { userId, title, characterId }: { userId: string; title: string; characterId?: string }): Promise<Chat> {
  const supabase = getSupabase(env)
  const result = await supabase
    .from(TABLE_NAMES.chat)
    .insert({
  id: crypto.randomUUID(),
  user_id: userId,
  title,
    character_id: characterId,
    created_at: new Date(),
    updated_at: new Date()
})
    .select()
    .single()

  const { data, error } = handleSupabaseSingleResult(result)
  if (error) throw new Error(`Failed to create chat: ${error.message}`)
  return data
}

export async function updateChatInDB(env: Env, { userId, chatId, title, visibility }: { userId: string; chatId: string; title?: string; visibility?: 'private' | 'public' }): Promise<Chat | null> {
  const supabase = getSupabase(env)
  const updates: Partial<Chat> = { updatedAt: new Date() }
  if (title) updates.title = title
  if (visibility) updates.visibility = visibility

  const result = await supabase
    .from(TABLE_NAMES.chat)
    .update(updates)
    .eq('id', chatId)
    .eq('user_id', userId)
    .select()
    .single()

  const { data, error } = handleSupabaseSingleResult(result)
  if (error) return null
  return data
}

export async function deleteChatFromDB(env: Env, userId: string, chatId: string): Promise<boolean> {
  const supabase = getSupabase(env)
  // First delete messages
  await supabase.from(TABLE_NAMES.message).delete().eq('chat_id', chatId)

  // Then delete chat
  const result = await supabase
    .from(TABLE_NAMES.chat)
    .delete()
    .eq('id', chatId)
    .eq('user_id', userId)
    .select()
    .single()

  const { error } = handleSupabaseSingleResult(result)
  return !error
}

export async function getChatsByUserId(env: Env, { userId, limit = 10, startingAfter, endingBefore }: { userId: string; limit?: number; startingAfter?: string; endingBefore?: string }): Promise<{ chats: Chat[], hasMore: boolean }> {
  const supabase = getSupabase(env)
  let query = supabase
      .from(TABLE_NAMES.chat)
      .select('*')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false });

    if (startingAfter) {
      query = query.gt('id', startingAfter);
    }

    if (endingBefore) {
      query = query.lt('id', endingBefore);
    }

    const result = await query.limit(limit + 1)

  const { data, error } = handleSupabaseResult(result)
  if (error) return { chats: [], hasMore: false }
  const hasMore = (data || []).length > limit; return { chats: hasMore ? (data || []).slice(0, -1) : (data || []), hasMore }
}

export async function getChatsByRoleAndUserId(env: Env, { userId, roleId, limit = 5 }: { userId: string; roleId: string; limit?: number }): Promise<Chat[]> {
  const supabase = getSupabase(env)
  const result = await supabase
    .from(TABLE_NAMES.chat)
    .select('*')
    .eq('user_id', userId)
    .eq('role', roleId)
    .order('updated_at', { ascending: false })
    .limit(limit)

  const { data, error } = handleSupabaseResult(result)
  if (error) return []
  return data || []
}

export async function updateChatVisibilityInDB(
  env: Env,
  {
    chatId,
    visibility,
  }: {
    chatId: string;
    visibility: 'private' | 'public';
  }
): Promise<Chat[] | null> {
  const supabase = getSupabase(env)
  const result = await supabase
    .from(TABLE_NAMES.chat)
    .update({ visibility })
    .eq('id', chatId)
    .select()

  const { data, error } = handleSupabaseResult(result)
  if (error) return null
  return data || []
}

export async function updateChatLastActivityInDB(
  env: Env,
  {
    chatId,
  }: {
    chatId: string;
  }
): Promise<Chat[] | null> {
  const supabase = getSupabase(env)
  const result = await supabase
    .from(TABLE_NAMES.chat)
    .update({ updated_at: new Date() })
    .eq('id', chatId)
    .select()

  const { data, error } = handleSupabaseResult(result)
  if (error) return null
  return data || []
}

export async function getChatsByCharacterAndUserIdInDB(
  env: Env,
  {
    userId,
    characterId,
    limit = 5,
  }: {
    userId: string;
    characterId: string;
    limit?: number;
  }
): Promise<Chat[]> {
  const supabase = getSupabase(env)
  const result = await supabase
    .from(TABLE_NAMES.chat)
    .select('*')
    .eq('user_id', userId)
    .eq('character_id', characterId)
    .order('created_at', { ascending: false })
    .limit(limit)

  const { data, error } = handleSupabaseResult(result)
  if (error) return []
  return data || []
}

export async function getMessageCountByUserIdInDB(
  env: Env,
  {
    userId,
    differenceInHours,
  }: {
    userId: string;
    differenceInHours: number;
  }
): Promise<number> {
  const supabase = getSupabase(env)
  const hoursAgo = new Date(Date.now() - differenceInHours * 60 * 60 * 1000)

  // 先获取用户的聊天ID列表
  const { data: userChats, error: chatError } = await supabase
    .from(TABLE_NAMES.chat)
    .select('id')
    .eq('user_id', userId)

  if (chatError) return 0

  const chatIds = (userChats || []).map((chat: any) => chat.id)

  if (chatIds.length === 0) {
    return 0
  }

  // 然后统计这些聊天中的用户消息数量
  const { count, error } = await supabase
    .from(TABLE_NAMES.message)
    .select('id', { count: 'exact', head: true })
    .eq('role', 'user')
    .gte('created_at', hoursAgo.toISOString())
    .in('chat_id', chatIds)

  if (error) return 0
  return count ?? 0
}

export async function deleteMessagesByChatIdAfterTimestampInDB(
  env: Env,
  {
    chatId,
    timestamp,
  }: {
    chatId: string;
    timestamp: Date;
  }
): Promise<boolean> {
  try {
    const supabase = getSupabase(env)

    // 先查询要删除的消息
    const messagesToDeleteResult = await supabase
      .from(TABLE_NAMES.message)
      .select('id')
      .eq('chat_id', chatId)
      .gte('created_at', timestamp.toISOString())

    const { data: messagesToDelete, error: queryError } = handleSupabaseResult(messagesToDeleteResult)
    if (queryError) throw queryError

    const messageIds = (messagesToDelete || []).map((msg: any) => msg.id)

    if (messageIds.length > 0) {
      const result = await supabase.from(TABLE_NAMES.message).delete().in('id', messageIds)
      if (result.error) throw result.error
    }
    return true
  } catch (error) {
    console.error('Failed to delete messages by chat id after timestamp', error)
    return false
  }
}