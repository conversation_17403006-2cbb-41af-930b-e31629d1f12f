import { getSupabase } from '@/lib/db/queries/base'
import type { Env } from '@/types/env'

export type Message = {
  id: string;
  chat_id: string;
  content: string;
  role: 'user' | 'assistant';
  attachments?: Array<{ url: string; type: string }>;
  created_at: string;
  updated_at: string;
};

const TABLE_NAMES = {
  message: 'message'
};

function handleSupabaseResult<T>(result: {
  data: T | null;
  error: any;
}): {
  data: T | null;
  error: any;
} {
  if (result.error) {
    console.error('Supabase error:', result.error);
    return { data: null, error: result.error };
  }
  return { data: result.data, error: null };
}


export async function getMessageById(env: Env, messageId: string): Promise<Message | null> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from(TABLE_NAMES.message)
    .select('*')
    .eq('id', messageId)
    .single();
  
  const { data, error } = handleSupabaseResult(result);
  if (error) return null;
  return data;
}

export async function getMessagesByChatId(env: Env, chatId: string): Promise<Message[] | null> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from(TABLE_NAMES.message)
    .select('*')
    .eq('chat_id', chatId)
    .order('created_at', { ascending: true });
  
  const { data, error } = handleSupabaseResult(result);
  if (error) return null;
  return data;
}

export async function updateMessageAttachments(
  env: Env,
  messageId: string,
  attachments: any[]
): Promise<Message | null> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from(TABLE_NAMES.message)
    .update({ attachments })
    .eq('id', messageId)
    .single();
  
  const { data, error } = handleSupabaseResult(result);
  if (error) return null;
  return data;
}

export async function saveMessages(
  env: Env,
  messages: Omit<Message, 'id' | 'created_at' | 'updated_at'>[]
): Promise<Message[] | null> {
  const supabase = getSupabase(env);

  if (!messages.length) {
    return [];
  }

  const result = await supabase
    .from('message')
    .insert(messages)
    .select();

  if (result.error) {
    console.error('Error saving messages:', result.error);
    return null;
  }

  return result.data as Message[];
}