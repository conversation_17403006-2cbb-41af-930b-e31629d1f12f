import { Hono } from 'hono'
import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import { getChat<PERSON>and<PERSON>, create<PERSON>hatHandler, update<PERSON>hat<PERSON>andler, deleteChatHandler } from '../controllers/chat.controller'
import { createChatSchema, updateChatSchema } from '@/lib/chat/handlers'

const route = new Hono()
route.use(authMiddleware, languageMiddleware)
route.get('/:id', getChatHandler)
route.post('/', zValidator('json', createChatSchema), createChatHandler)
route.put('/:id', zValidator('json', updateChatSchema), updateChatHandler)
route.delete('/:id', deleteChatHandler)

export default route