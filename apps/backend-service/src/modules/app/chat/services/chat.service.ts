import { getChatFromDB, createChatInDB, updateChatInDB, deleteChatFromDB } from '../repositories/chat.repository'
import type { Env } from '@/types/env'
import type { Chat } from '@/lib/db/schema'

export async function getChatById(env: Env, chatId: string): Promise<Chat | null> {
  return getChatFromDB(env, chatId)
}

export async function createChat(env: Env, userId: string, data: { title: string; characterId?: string }): Promise<Chat> {
  return createChatInDB(env, { ...data, userId })
}

export async function updateChat(env: Env, userId: string, chatId: string, data: { title?: string; visibility?: 'private' | 'public' }): Promise<Chat | null> {
  return updateChatInDB(env, { ...data, userId, chatId })
}

export async function deleteChat(env: Env, userId: string, chatId: string): Promise<boolean> {
  return deleteChatFromDB(env, userId, chatId)
}