import type { Context } from 'hono'
import { getChatById, createChat, updateChat, deleteChat } from '../services/chat.service'
import type { Env } from '@/types/env'
import { zValidator } from '@hono/zod-validator'
import type { createChatSchema, updateChatSchema } from '@/lib/chat/handlers'
import type { Chat } from '@/lib/db/schema'

// 定义上下文类型，包含验证和用户信息
type ChatContext = Context<{
  Bindings: Env,
  Variables: {
    user: { id: string }
  },
  Validator: {
    json: z.infer<typeof createChatSchema> | z.infer<typeof updateChatSchema>
  }
}>

export const getChatHandler = async (c: ChatContext) => {
  const chatId = c.req.param('id')
  const chat = await getChatById(c.env, chatId)
  return chat ? c.json({ success: true, data: chat }) : c.json({ success: false, message: 'Chat not found' }, 404)
}

export const createChatHandler = async (c: ChatContext) => {
  const data = c.req.valid('json')
  const userId = c.get('user').id
  const chat = await createChat(c.env, userId, data)
  return c.json({ success: true, data: chat }, 201)
}

export const updateChatHandler = async (c: ChatContext) => {
  const chatId = c.req.param('id')
  const data = c.req.valid('json')
  const userId = c.get('user').id
  const updatedChat = await updateChat(c.env, userId, chatId, data)
  return updatedChat ? c.json({ success: true, data: updatedChat }) : c.json({ success: false, message: 'Chat not found' }, 404)
}

export const deleteChatHandler = async (c: ChatContext) => {
  const chatId = c.req.param('id')
  const userId = c.get('user').id
  await deleteChat(c.env, userId, chatId)
  return c.json({ success: true, message: 'Chat deleted successfully' })
}