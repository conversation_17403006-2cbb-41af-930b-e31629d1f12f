import { z } from 'zod'
import type { CharacterCreateData, CharacterUpdateData, CharacterQueryParams } from '@/types/entities/character'

export class ValidationService {
  // 角色创建验证模式
  private createCharacterSchema = z.object({
    name: z.string().min(1).max(50),
    description: z.string().max(500).optional(),
    relationship: z.string().max(50).optional(),
    ethnicity: z.string().max(30).optional(),
    gender: z.enum(['male', 'female', 'other']).optional(),
    age: z.string().max(20).optional(),
    eyeColor: z.string().max(30).optional(),
    hairStyle: z.string().max(50).optional(),
    hairColor: z.string().max(30).optional(),
    faceShape: z.string().max(30).optional(),
    bodyType: z.string().max(50).optional(),
    breastSize: z.string().max(20).optional(),
    buttSize: z.string().max(20).optional(),
    personality: z.string().max(500).optional(),
    clothing: z.string().max(200).optional(),
    voice: z.string().max(100).optional(),
    voiceModelId: z.string().uuid().optional(),
    keywords: z.string().max(200).default(''),
    prompt: z.string().max(2000).default(''),
    imageUrl: z.string().url().optional(),
    category: z.string().max(50).optional(),
    isPublic: z.boolean().default(false)
  })

  // 角色更新验证模式
  private updateCharacterSchema = this.createCharacterSchema.partial()

  // 查询参数验证模式
  private queryParamsSchema = z.object({
    page: z.number().int().min(1).optional(),
    limit: z.number().int().min(1).max(100).optional(),
    offset: z.number().int().min(0).optional(),
    category: z.string().max(50).optional()
  })

  /**
   * 验证角色创建数据
   */
  validateCreateData(data: unknown): Omit<CharacterCreateData, 'userId'> {
    return this.createCharacterSchema.parse(data)
  }

  /**
   * 验证角色更新数据
   */
  validateUpdateData(data: unknown): CharacterUpdateData {
    return this.updateCharacterSchema.parse(data)
  }

  /**
   * 验证查询参数
   */
  validateQueryParams(params: unknown): CharacterQueryParams {
    return this.queryParamsSchema.parse(params)
  }

  /**
   * 获取创建角色的验证模式（用于中间件）
   */
  getCreateCharacterSchema() {
    return this.createCharacterSchema
  }

  /**
   * 获取更新角色的验证模式（用于中间件）
   */
  getUpdateCharacterSchema() {
    return this.updateCharacterSchema
  }
} 