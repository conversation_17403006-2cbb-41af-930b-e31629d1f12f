import { getCachedDbUserId } from '@/lib/cache/cache-utils'
import type { Env } from '@/types/env'
import type { MediaQueryParams, MediaGenerationData, FormattedMediaImage, FormattedMediaVideo } from '@/types/entities/character'
import { MediaRepository } from '../repositories'

export class MediaService {
  private mediaRepo: MediaRepository

  constructor() {
    this.mediaRepo = new MediaRepository()
  }

  /**
   * 获取角色媒体生成记录
   */
  async getCharacterMedia(env: Env, supabaseUserId: string, params: MediaQueryParams): Promise<{
    mediaGenerations: MediaGenerationData[]
    total: number
    hasMore: boolean
  }> {
    // 获取数据库用户ID
    const dbUserId = await getCachedDbUserId(env, supabaseUserId)
    if (!dbUserId) {
      throw new Error('用户不存在')
    }

    const mediaGenerations = await this.mediaRepo.getUserMediaGenerations(env, dbUserId, params)

    return {
      mediaGenerations,
      total: mediaGenerations.length,
      hasMore: mediaGenerations.length === (params.limit || 20)
    }
  }

  /**
   * 获取角色图片列表
   */
  async getCharacterImages(env: Env, supabaseUserId: string, characterId: string, limit = 20, offset = 0): Promise<{
    images: FormattedMediaImage[]
    total: number
    hasMore: boolean
  }> {
    // 获取数据库用户ID
    const dbUserId = await getCachedDbUserId(env, supabaseUserId)
    if (!dbUserId) {
      throw new Error('用户不存在')
    }

    const mediaGenerations = await this.mediaRepo.getUserMediaGenerations(env, dbUserId, {
      characterId,
      mediaType: 'image',
      status: 'completed',
      limit,
      offset
    })

    // 提取图片URL
    const images = mediaGenerations
      .filter(mg => mg.outputUrls && Array.isArray(mg.outputUrls) && mg.outputUrls.length > 0)
      .flatMap(mg =>
        (mg.outputUrls as string[]).map(url => ({
          id: mg.id,
          url,
          prompt: mg.prompt || undefined,
          createdAt: mg.createdAt,
          generationType: mg.generationType,
          metadata: mg.metadata
        }))
      )

    return {
      images,
      total: images.length,
      hasMore: mediaGenerations.length === limit
    }
  }

  /**
   * 获取角色视频列表
   */
  async getCharacterVideos(env: Env, supabaseUserId: string, characterId: string, limit = 20, offset = 0): Promise<{
    videos: FormattedMediaVideo[]
    total: number
    hasMore: boolean
  }> {
    // 获取数据库用户ID
    const dbUserId = await getCachedDbUserId(env, supabaseUserId)
    if (!dbUserId) {
      throw new Error('用户不存在')
    }

    const mediaGenerations = await this.mediaRepo.getUserMediaGenerations(env, dbUserId, {
      characterId,
      mediaType: 'video',
      status: 'completed',
      limit,
      offset
    })

    // 提取视频URL
    const videos = mediaGenerations
      .filter(mg => mg.outputUrls && Array.isArray(mg.outputUrls) && mg.outputUrls.length > 0)
      .flatMap(mg =>
        (mg.outputUrls as string[]).map(url => ({
          id: mg.id,
          url,
          prompt: mg.prompt || undefined,
          createdAt: mg.createdAt,
          generationType: mg.generationType,
          metadata: mg.metadata
        }))
      )

    return {
      videos,
      total: videos.length,
      hasMore: mediaGenerations.length === limit
    }
  }
} 