import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import type { Env } from '@/types/env'
import { CharacterController } from '../controllers'
import { ValidationService } from '../services'

const charactersRouter = new Hono<{ Bindings: Env }>()

// 创建控制器实例
const characterController = new CharacterController()
const validationService = new ValidationService()

// ==================== 获取系统角色列表 ====================
charactersRouter.get('/system', languageMiddleware, async c => {
  return await characterController.getSystemCharacters(c as any)
})

// ==================== 获取公开角色列表 ====================
charactersRouter.get('/public', languageMiddleware, async c => {
  return await characterController.getPublicCharacters(c as any)
})

// ==================== 获取角色统计信息 ====================
charactersRouter.get('/stats', authMiddleware, languageMiddleware, async c => {
  return await characterController.getStats(c as any)
})

// ==================== 获取用户角色列表 ====================
charactersRouter.get('/', authMiddleware, languageMiddleware, async c => {
  return await characterController.getUserCharacters(c as any)
})

// ==================== 创建新角色 ====================
charactersRouter.post(
  '/',
  authMiddleware,
  languageMiddleware,
  zValidator('json', validationService.getCreateCharacterSchema()),
  async c => {
    return await characterController.create(c as any)
  }
)

// ==================== 获取角色详情 ====================
charactersRouter.get('/:id', languageMiddleware, async c => {
  return await characterController.getById(c as any)
})

// ==================== 更新角色 ====================
charactersRouter.put(
  '/:id',
  authMiddleware,
  languageMiddleware,
  zValidator('json', validationService.getUpdateCharacterSchema()),
  async c => {
    return await characterController.update(c as any)
  }
)

// ==================== 删除角色 ====================
charactersRouter.delete('/:id', authMiddleware, languageMiddleware, async c => {
  return await characterController.delete(c as any)
})

export { charactersRouter } 