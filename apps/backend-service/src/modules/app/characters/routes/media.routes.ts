import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { authMiddleware } from '@/middleware/auth'
import type { Env } from '@/types/env'
import { MediaController } from '../controllers'

const mediaRouter = new Hono<{ Bindings: Env }>()

// 创建控制器实例
const mediaController = new MediaController()

// 查询参数验证
const getCharacterMediaSchema = z.object({
  mediaType: z.enum(['image', 'video', 'audio']).optional(),
  generationType: z.enum(['multimodal_chat', 'standalone', 'template_based']).optional(),
  status: z.enum(['pending', 'processing', 'completed', 'failed', 'cancelled']).optional(),
  limit: z
    .string()
    .optional()
    .transform(val => (val ? Number.parseInt(val) : 20)),
  offset: z
    .string()
    .optional()
    .transform(val => (val ? Number.parseInt(val) : 0))
})

/**
 * GET /api/character-media/:characterId
 * 获取角色的媒体生成记录
 */
mediaRouter.get(
  '/:characterId',
  authMiddleware,
  zValidator('query', getCharacterMediaSchema),
  async c => {
    return await mediaController.getCharacterMedia(c as any)
  }
)

/**
 * GET /api/character-media/:characterId/images
 * 获取角色的图片生成记录
 */
mediaRouter.get('/:characterId/images', authMiddleware, async c => {
  return await mediaController.getCharacterImages(c as any)
})

/**
 * GET /api/character-media/:characterId/videos
 * 获取角色的视频生成记录
 */
mediaRouter.get('/:characterId/videos', authMiddleware, async c => {
  return await mediaController.getCharacterVideos(c as any)
})

export { mediaRouter } 