import { getUserMediaGenerations } from '@/lib/db/queries/media-generation'
import type { Env } from '@/types/env'
import type { MediaQueryParams, MediaGenerationData } from '@/types/entities/character'

export class MediaRepository {
  /**
   * 获取用户媒体生成记录
   */
  async getUserMediaGenerations(env: Env, userId: string, params: MediaQueryParams): Promise<MediaGenerationData[]> {
    try {
      console.log('🔍 [MEDIA-REPO] 查询角色媒体记录:', {
        userId,
        ...params
      })

      const mediaGenerations = await getUserMediaGenerations(env, userId, {
        characterId: params.characterId,
        mediaType: params.mediaType,
        generationType: params.generationType,
        status: params.status || 'completed', // 默认只返回已完成的记录
        limit: params.limit,
        offset: params.offset
      })

             // 转换为统一的数据格式
       return mediaGenerations.map(mg => ({
         id: mg.id,
         characterId: mg.characterId || '',
         chatId: mg.chatId,
         messageId: mg.messageId,
         mediaType: mg.mediaType,
         generationType: mg.generationType,
         prompt: mg.prompt,
         negativePrompt: mg.negativePrompt,
         inputImageUrl: mg.inputImageUrl,
         outputUrls: (mg.outputUrls as string[]) || [],
         status: mg.status,
         errorMessage: mg.errorMessage,
         pointsUsed: mg.pointsUsed,
         generationTime: mg.generationTime,
         completedAt: mg.completedAt ? (mg.completedAt instanceof Date ? mg.completedAt.toISOString() : mg.completedAt) : null,
         createdAt: mg.createdAt instanceof Date ? mg.createdAt.toISOString() : mg.createdAt,
         updatedAt: mg.updatedAt instanceof Date ? mg.updatedAt.toISOString() : mg.updatedAt,
         metadata: mg.metadata
       }))
    } catch (error) {
      console.error('❌ [MEDIA-REPO] 查询角色媒体记录失败:', error)
      throw error
    }
  }
} 