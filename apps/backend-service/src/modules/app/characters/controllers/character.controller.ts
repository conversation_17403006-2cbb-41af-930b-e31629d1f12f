import type { Context } from 'hono'
import type { Env } from '@/types/env'
import type { SupportedLanguage } from '@/i18n/messages'
import type { CharacterResponse, CharacterListResponse, CharacterStatsResponse, ApiResponse } from '@/types/responses/character'
import { CharacterService, ValidationService } from '../services'

export class CharacterController {
  private characterService: CharacterService
  private validationService: ValidationService

  constructor() {
    this.characterService = new CharacterService()
    this.validationService = new ValidationService()
  }

  /**
   * 创建新角色
   */
  async create(c: Context<{
    Bindings: Env
    Variables: {
      user: any
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>): Promise<Response> {
    const user = c.get('user')
    const t = c.get('t')

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('characters.user_not_found')
        },
        401
      )
    }

    try {
      // 获取并验证请求数据
      const requestData = await c.req.json()
      const validatedData = this.validationService.validateCreateData(requestData)

      // 创建角色
      const newCharacter = await this.characterService.createCharacter(c.env, user.id, validatedData)

      // 返回成功响应
      const response: CharacterResponse = {
        success: true,
        data: newCharacter
      }

      return c.json(response, 201)
    } catch (error) {
      console.error('创建角色失败:', error)
      
      // 处理权限错误
      if (error instanceof Error && error.message.includes('角色创建上限')) {
        return c.json(
          {
            success: false,
            message: error.message
          },
          403
        )
      }

      return c.json(
        {
          success: false,
          message: t('characters.create_failed')
        },
        500
      )
    }
  }

  /**
   * 获取用户角色列表
   */
  async getUserCharacters(c: Context<{
    Bindings: Env
    Variables: {
      user: any
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>): Promise<Response> {
    const user = c.get('user')
    const t = c.get('t')

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('characters.user_not_found')
        },
        401
      )
    }

    try {
      const charactersData = await this.characterService.getUserCharacters(c.env, user.id)

      const response: CharacterListResponse = {
        success: true,
        data: charactersData,
        pagination: {
          total: charactersData.length
        }
      }

      return c.json(response, 200)
    } catch (error) {
      console.error('获取用户角色列表失败:', error)
      return c.json(
        {
          success: false,
          message: t('characters.get_user_characters_failed')
        },
        500
      )
    }
  }

  /**
   * 获取角色详情
   */
  async getById(c: Context<{
    Bindings: Env
    Variables: {
      user?: any
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>): Promise<Response> {
    const characterId = c.req.param('id')
    const user = c.get('user')
    const t = c.get('t')

    try {
      const character = await this.characterService.getCharacterById(c.env, characterId, user?.id)

      const response: CharacterResponse = {
        success: true,
        data: character
      }

      return c.json(response, 200)
    } catch (error) {
      console.error('获取角色详情失败:', error)
      
      if (error instanceof Error) {
        if (error.message.includes('不存在')) {
          return c.json(
            {
              success: false,
              message: t('characters.character_not_exist')
            },
            404
          )
        }
        if (error.message.includes('权限') || error.message.includes('登录')) {
          return c.json(
            {
              success: false,
              message: t('characters.private_character_login_required')
            },
            401
          )
        }
      }

      return c.json(
        {
          success: false,
          message: t('characters.get_character_details_failed')
        },
        500
      )
    }
  }

  /**
   * 更新角色信息
   */
  async update(c: Context<{
    Bindings: Env
    Variables: {
      user: any
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>): Promise<Response> {
    const user = c.get('user')
    const characterId = c.req.param('id')
    const t = c.get('t')

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('characters.user_not_found')
        },
        401
      )
    }

    try {
      // 获取并验证请求数据
      const requestData = await c.req.json()
      const validatedData = this.validationService.validateUpdateData(requestData)

      // 更新角色
      const updatedCharacter = await this.characterService.updateCharacter(c.env, characterId, user.id, validatedData)

      // 返回成功响应
      const response: CharacterResponse = {
        success: true,
        data: updatedCharacter
      }

      return c.json(response, 200)
    } catch (error) {
      console.error('更新角色失败:', error)
      
      if (error instanceof Error) {
        if (error.message.includes('不存在')) {
          return c.json(
            {
              success: false,
              message: t('characters.character_not_exist')
            },
            404
          )
        }
        if (error.message.includes('权限')) {
          return c.json(
            {
              success: false,
              message: t('characters.no_permission_modify')
            },
            403
          )
        }
      }

      return c.json(
        {
          success: false,
          message: t('characters.update_failed')
        },
        500
      )
    }
  }

  /**
   * 删除角色
   */
  async delete(c: Context<{
    Bindings: Env
    Variables: {
      user: any
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>): Promise<Response> {
    const user = c.get('user')
    const characterId = c.req.param('id')
    const t = c.get('t')

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('characters.user_not_found')
        },
        401
      )
    }

    try {
      // 删除角色
      await this.characterService.deleteCharacter(c.env, characterId, user.id)

      // 返回成功响应
      const response: ApiResponse = {
        success: true,
        message: t('characters.delete_success')
      }

      return c.json(response, 200)
    } catch (error) {
      console.error('删除角色失败:', error)
      
      if (error instanceof Error) {
        if (error.message.includes('不存在')) {
          return c.json(
            {
              success: false,
              message: t('characters.character_not_exist')
            },
            404
          )
        }
        if (error.message.includes('权限')) {
          return c.json(
            {
              success: false,
              message: t('characters.no_permission_delete')
            },
            403
          )
        }
      }

      return c.json(
        {
          success: false,
          message: t('characters.delete_failed')
        },
        500
      )
    }
  }

  /**
   * 获取公开角色列表
   */
  async getPublicCharacters(c: Context<{
    Bindings: Env
    Variables: {
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>): Promise<Response> {
    const t = c.get('t')

    try {
      // 获取查询参数
      const page = Number.parseInt(c.req.query('page') || '1')
      const limit = Number.parseInt(c.req.query('limit') || '20')
      const category = c.req.query('category') || undefined

      // 计算偏移量
      const offset = (page - 1) * limit

      const charactersData = await this.characterService.getPublicCharacters(c.env, {
        limit,
        offset,
        category
      })

      const response: CharacterListResponse = {
        success: true,
        data: charactersData,
        pagination: {
          page,
          limit,
          total: charactersData.length,
          hasMore: charactersData.length === limit
        }
      }

      return c.json(response, 200)
    } catch (error) {
      console.error('获取公开角色列表失败:', error)
      return c.json(
        {
          success: false,
          message: t('characters.get_public_failed')
        },
        500
      )
    }
  }

  /**
   * 获取系统角色列表
   */
  async getSystemCharacters(c: Context<{
    Bindings: Env
    Variables: {
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>): Promise<Response> {
    const t = c.get('t')

    try {
      // 获取查询参数
      const page = Number.parseInt(c.req.query('page') || '1')
      const limit = Number.parseInt(c.req.query('limit') || '50')
      const category = c.req.query('category') || undefined

      // 计算偏移量
      const offset = (page - 1) * limit

      const charactersData = await this.characterService.getSystemCharacters(c.env, {
        limit,
        offset,
        category
      })

      const response: CharacterListResponse = {
        success: true,
        data: charactersData,
        pagination: {
          page,
          limit,
          total: charactersData.length,
          hasMore: charactersData.length === limit
        }
      }

      return c.json(response, 200)
    } catch (error) {
      console.error('获取系统角色列表失败:', error)
      return c.json(
        {
          success: false,
          message: t('characters.get_system_failed')
        },
        500
      )
    }
  }

  /**
   * 获取角色统计信息
   */
  async getStats(c: Context<{
    Bindings: Env
    Variables: {
      user: any
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>): Promise<Response> {
    const user = c.get('user')
    const t = c.get('t')

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('characters.user_not_found')
        },
        401
      )
    }

    try {
      const stats = await this.characterService.getUserCharacterStats(c.env, user.id)

      const response: CharacterStatsResponse = {
        success: true,
        data: stats,
        message: t('characters.get_stats_success')
      }

      return c.json(response)
    } catch (error) {
      console.error('获取角色统计失败:', error)
      return c.json(
        {
          success: false,
          message: t('characters.get_stats_failed')
        },
        500
      )
    }
  }
} 