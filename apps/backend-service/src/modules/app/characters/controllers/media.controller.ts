import type { Context } from 'hono'
import type { Env } from '@/types/env'
import type { SupportedLanguage } from '@/i18n/messages'
import type { MediaListResponse, ImageListResponse, VideoListResponse } from '@/types/responses/character'
import { MediaService } from '../services'

export class MediaController {
  private mediaService: MediaService

  constructor() {
    this.mediaService = new MediaService()
  }

  /**
   * 获取角色媒体生成记录
   */
  async getCharacterMedia(c: Context<{
    Bindings: Env
    Variables: {
      user: any
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>): Promise<Response> {
    const user = c.get('user')
    const characterId = c.req.param('characterId')
    const t = c.get('t')

    if (!user) {
      return c.json({ success: false, message: t('character-media.user_not_found') }, 401)
    }

    try {
      // 获取查询参数
      const mediaType = c.req.query('mediaType') as 'image' | 'video' | 'audio' | undefined
      const generationType = c.req.query('generationType') as 'multimodal_chat' | 'standalone' | 'template_based' | undefined
      const status = c.req.query('status') as 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | undefined
      const limit = Number.parseInt(c.req.query('limit') || '20')
      const offset = Number.parseInt(c.req.query('offset') || '0')

      const result = await this.mediaService.getCharacterMedia(c.env, user.id, {
        characterId,
        mediaType,
        generationType,
        status,
        limit,
        offset
      })

      const response: MediaListResponse = {
        success: true,
        data: result
      }

      return c.json(response)
    } catch (error) {
      console.error('❌ [CHARACTER-MEDIA] 查询角色媒体记录失败:', error)
      return c.json(
        {
          success: false,
          message: t('character-media.query_media_failed'),
          error: error instanceof Error ? error.message : String(error)
        },
        500
      )
    }
  }

  /**
   * 获取角色图片列表
   */
  async getCharacterImages(c: Context<{
    Bindings: Env
    Variables: {
      user: any
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>): Promise<Response> {
    const user = c.get('user')
    const characterId = c.req.param('characterId')
    const t = c.get('t')

    if (!user) {
      return c.json({ success: false, message: t('character-media.user_not_found') }, 401)
    }

    try {
      const limit = Number.parseInt(c.req.query('limit') || '20')
      const offset = Number.parseInt(c.req.query('offset') || '0')

      const result = await this.mediaService.getCharacterImages(c.env, user.id, characterId, limit, offset)

      const response: ImageListResponse = {
        success: true,
        data: result
      }

      return c.json(response)
    } catch (error) {
      console.error('❌ [CHARACTER-MEDIA] 查询角色图片失败:', error)
      return c.json(
        {
          success: false,
          message: t('character-media.query_images_failed'),
          error: error instanceof Error ? error.message : String(error)
        },
        500
      )
    }
  }

  /**
   * 获取角色视频列表
   */
  async getCharacterVideos(c: Context<{
    Bindings: Env
    Variables: {
      user: any
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>): Promise<Response> {
    const user = c.get('user')
    const characterId = c.req.param('characterId')
    const t = c.get('t')

    if (!user) {
      return c.json({ success: false, message: t('character-media.user_not_found') }, 401)
    }

    try {
      const limit = Number.parseInt(c.req.query('limit') || '20')
      const offset = Number.parseInt(c.req.query('offset') || '0')

      const result = await this.mediaService.getCharacterVideos(c.env, user.id, characterId, limit, offset)

      const response: VideoListResponse = {
        success: true,
        data: result
      }

      return c.json(response)
    } catch (error) {
      console.error('❌ [CHARACTER-MEDIA] 查询角色视频失败:', error)
      return c.json(
        {
          success: false,
          message: t('character-media.query_videos_failed'),
          error: error instanceof Error ? error.message : String(error)
        },
        500
      )
    }
  }
} 