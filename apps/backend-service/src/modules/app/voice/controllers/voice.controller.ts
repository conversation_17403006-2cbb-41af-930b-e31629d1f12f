import { Context } from 'hono';
import type { Env } from '@/types/env';
import {
  getActiveVoiceModels,
  getVoiceModelsByGender,
  getVoiceModelById,
  getVoiceModelByModelId,
  createVoiceModel,
  updateVoiceModel,
  getVoiceSamplesByModelId,
  getVoiceSampleByLanguage,
  createVoiceSample,
  updateVoiceSample,
  deleteVoiceSample,
  getVoiceModelWithSamples,
  getAllVoiceModelsWithDefaultSamples
} from '../services/voice.service';
import { getCachedVoiceModelsList, clearAllVoiceModelsListCache } from '@/lib/cache/cache-utils';
import type { VoiceModel, VoiceSample } from '@/lib/db/schema';

// ==================== 公开接口控制器 ====================

export async function getVoiceList(c: Context<{ Bindings: Env }>): Promise<Response> {
  try {
    const env = c.env;
    const query = c.req.query();

    let voiceModels: (VoiceModel | (VoiceModel & { defaultSample: VoiceSample | null }))[];

    if (query.withSamples === 'true') {
      if (query.gender) {
        const gender = query.gender as 'male' | 'female' | 'neutral';
        voiceModels = await getCachedVoiceModelsList(
          env,
          async () => {
            const allModels = await getAllVoiceModelsWithDefaultSamples(env);
            return allModels.filter(model => model.gender === gender);
          },
          'withSamples',
          gender
        );
      } else {
        voiceModels = await getCachedVoiceModelsList(
          env,
          () => getAllVoiceModelsWithDefaultSamples(env),
          'withSamples'
        );
      }
    } else if (query.gender) {
      const gender = query.gender as 'male' | 'female' | 'neutral';
      voiceModels = await getCachedVoiceModelsList(
        env,
        () => getVoiceModelsByGender(env, gender),
        'gender',
        gender
      );
    } else {
      voiceModels = await getCachedVoiceModelsList(env, () => getActiveVoiceModels(env), 'all');
    }

    return c.json({
      success: true,
      data: voiceModels
    });
  } catch (error) {
    console.error('获取声音模型列表失败:', error);
    return c.json(
      {
        success: false,
        message: '获取声音模型失败'
      },
      500
    );
  }
}

export async function getVoiceDetail(c: Context<{ Bindings: Env }>): Promise<Response> {
  try {
    const env = c.env;
    const id = c.req.param('id');
    const withSamples = c.req.query('withSamples') === 'true';

    let voiceModel;
    if (withSamples) {
      voiceModel = await getVoiceModelWithSamples(env, id);
    } else {
      voiceModel = await getVoiceModelById(env, id);
    }

    if (!voiceModel) {
      return c.json(
        {
          success: false,
          message: '声音模型不存在'
        },
        404
      );
    }

    return c.json({
      success: true,
      data: voiceModel
    });
  } catch (error) {
    console.error('获取声音模型详情失败:', error);
    return c.json(
      {
        success: false,
        message: '获取声音模型详情失败'
      },
      500
    );
  }
}

export async function getVoiceSamples(c: Context<{ Bindings: Env }>): Promise<Response> {
  try {
    const env = c.env;
    const id = c.req.param('id');
    const language = c.req.query('language');

    let samples;
    if (language) {
      const sample = await getVoiceSampleByLanguage(env, id, language);
      samples = sample ? [sample] : [];
    } else {
      samples = await getVoiceSamplesByModelId(env, id);
    }

    return c.json({
      success: true,
      data: samples
    });
  } catch (error) {
    console.error('获取声音示例失败:', error);
    return c.json(
      {
        success: false,
        message: '获取声音示例失败'
      },
      500
    );
  }
}

// ==================== 管理员接口控制器 ====================

export async function createVoice(c: Context<{ Bindings: Env }>): Promise<Response> {
  try {
    const env = c.env;
    const data = c.req.valid('json');

    // 检查modelId是否已存在
    const existingModel = await getVoiceModelByModelId(env, data.modelId);
    if (existingModel) {
      return c.json(
        {
          success: false,
          message: '模型ID已存在'
        },
        400
      );
    }

    const [newVoiceModel] = await createVoiceModel(env, data);

    // 清除声音模型列表缓存
    await clearAllVoiceModelsListCache(env);

    return c.json(
      {
        success: true,
        data: newVoiceModel
      },
      201
    );
  } catch (error) {
    console.error('创建声音模型失败:', error);
    return c.json(
      {
        success: false,
        message: '创建声音模型失败'
      },
      500
    );
  }
}

export async function updateVoice(c: Context<{ Bindings: Env }>): Promise<Response> {
  try {
    const env = c.env;
    const id = c.req.param('id');
    const data = c.req.valid('json');

    // 检查声音模型是否存在
    const existingModel = await getVoiceModelById(env, id);
    if (!existingModel) {
      return c.json(
        {
          success: false,
          message: '声音模型不存在'
        },
        404
      );
    }

    const [updatedVoiceModel] = await updateVoiceModel(env, id, data);

    // 清除声音模型列表缓存
    await clearAllVoiceModelsListCache(env);

    return c.json({
      success: true,
      data: updatedVoiceModel
    });
  } catch (error) {
    console.error('更新声音模型失败:', error);
    return c.json(
      {
        success: false,
        message: '更新声音模型失败'
      },
      500
    );
  }
}

export async function createVoiceSampleHandler(c: Context<{ Bindings: Env }>): Promise<Response> {
  try {
    const env = c.env;
    const voiceModelId = c.req.param('id');
    const data = c.req.valid('json');

    // 检查声音模型是否存在
    const existingModel = await getVoiceModelById(env, voiceModelId);
    if (!existingModel) {
      return c.json(
        {
          success: false,
          message: '声音模型不存在'
        },
        404
      );
    }

    const newSample = await createVoiceSample(env, data, voiceModelId);

    return c.json(
      {
        success: true,
        data: newSample
      },
      201
    );
  } catch (error) {
    console.error('创建声音示例失败:', error);
    return c.json(
      {
        success: false,
        message: '创建声音示例失败'
      },
      500
    );
  }
}

export async function updateVoiceSampleHandler(c: Context<{ Bindings: Env }>): Promise<Response> {
  try {
    const env = c.env;
    const sampleId = c.req.param('sampleId');
    const data = c.req.valid('json');

    const updatedSample = await updateVoiceSample(env, sampleId, data);

    if (!updatedSample) {
      return c.json(
        {
          success: false,
          message: '声音示例不存在'
        },
        404
      );
    }

    return c.json({
      success: true,
      data: updatedSample
    });
  } catch (error) {
    console.error('更新声音示例失败:', error);
    return c.json(
      {
        success: false,
        message: '更新声音示例失败'
      },
      500
    );
  }
}

export async function deleteVoiceSampleHandler(c: Context<{ Bindings: Env }>): Promise<Response> {
  try {
    const env = c.env;
    const sampleId = c.req.param('sampleId');

    const deletedSample = await deleteVoiceSample(env, sampleId);

    if (!deletedSample) {
      return c.json(
        {
          success: false,
          message: '声音示例不存在'
        },
        404
      );
    }

    return c.json({
      success: true,
      message: '声音示例删除成功'
    });
  } catch (error) {
    console.error('删除声音示例失败:', error);
    return c.json(
      {
        success: false,
        message: '删除声音示例失败'
      },
      500
    );
  }
}

// ==================== 调试接口控制器 ====================

export async function getVoiceCacheStats(c: Context<{ Bindings: Env }>): Promise<Response> {
  try {
    const env = c.env;
    const stats = await env.VOICE_MODEL_CACHE.stats();

    return c.json({
      success: true,
      data: {
        cache: stats,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('获取声音模型缓存统计失败:', error);
    return c.json(
      {
        success: false,
        message: '获取缓存统计失败',
        error: error instanceof Error ? error.message : String(error)
      },
      500
    );
  }
}

export async function clearVoiceCache(c: Context<{ Bindings: Env }>): Promise<Response> {
  try {
    const env = c.env;

    await clearAllVoiceModelsListCache(env);

    return c.json({
      success: true,
      message: '声音模型列表缓存已清除'
    });
  } catch (error) {
    console.error('清除声音模型列表缓存失败:', error);
    return c.json(
      {
        success: false,
        message: '清除缓存失败',
        error: error instanceof Error ? error.message : String(error)
      },
      500
    );
  }
}