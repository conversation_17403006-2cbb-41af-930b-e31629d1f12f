import { getSupabase } from '@/lib/db/queries/base';
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '@/lib/db/supabase-types';
import type { VoiceModel, VoiceSample } from '@/lib/db/schema';
import type { Env } from '@/types/env';

// ==================== 声音模型相关操作 ====================

export async function getActiveVoiceModelsRepo(env: Env): Promise<VoiceModel[]> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from(TABLE_NAMES.voiceModel)
    .select('*')
    .eq('is_active', true)
    .order('sort_order')
    .order('name');

  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;
  return data || [];
}

export async function getVoiceModelsByGenderRepo(env: Env, gender: 'male' | 'female' | 'neutral'): Promise<VoiceModel[]> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from(TABLE_NAMES.voiceModel)
    .select('*')
    .eq('is_active', true)
    .eq('gender', gender)
    .order('sort_order')
    .order('name');

  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;
  return data || [];
}

export async function getVoiceModelByIdRepo(env: Env, id: string): Promise<VoiceModel | null> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from(TABLE_NAMES.voiceModel)
    .select('*')
    .eq('id', id)
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) return null;
  return data;
}

export async function getVoiceModelByModelIdRepo(env: Env, modelId: string): Promise<VoiceModel | null> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from(TABLE_NAMES.voiceModel)
    .select('*')
    .eq('model_id', modelId)
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) return null;
  return data;
}

export async function createVoiceModelRepo(
  env: Env,
  data: {
    modelId: string;
    name: string;
    displayName: string;
    description?: string;
    gender: 'male' | 'female' | 'neutral';
    language?: string;
    supportedLanguages?: string[];
    category?: string;
    tags?: string[];
    isPremium?: boolean;
    sortOrder?: number;
  }
): Promise<VoiceModel[]> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from(TABLE_NAMES.voiceModel)
    .insert({
      model_id: data.modelId,
      name: data.name,
      display_name: data.displayName,
      description: data.description,
      gender: data.gender,
      language: data.language || 'zh-CN',
      supported_languages: data.supportedLanguages,
      category: data.category,
      tags: data.tags,
      is_premium: data.isPremium || false,
      sort_order: data.sortOrder || 0,
    })
    .select();

  const { data: voiceModels, error } = handleSupabaseResult(result);
  if (error) throw error;
  return voiceModels || [];
}

export async function updateVoiceModelRepo(
  env: Env,
  id: string,
  data: Partial<{
    name: string;
    displayName: string;
    description: string;
    gender: 'male' | 'female' | 'neutral';
    language: string;
    supportedLanguages: string[];
    category: string;
    tags: string[];
    isActive: boolean;
    isPremium: boolean;
    sortOrder: number;
  }>
): Promise<VoiceModel[]> {
  const supabase = getSupabase(env);

  // 转换字段名为 snake_case
  const updateData: any = {};
  if (data.name !== undefined) updateData.name = data.name;
  if (data.displayName !== undefined) updateData.display_name = data.displayName;
  if (data.description !== undefined) updateData.description = data.description;
  if (data.gender !== undefined) updateData.gender = data.gender;
  if (data.language !== undefined) updateData.language = data.language;
  if (data.supportedLanguages !== undefined)
    updateData.supported_languages = data.supportedLanguages;
  if (data.category !== undefined) updateData.category = data.category;
  if (data.tags !== undefined) updateData.tags = data.tags;
  if (data.isActive !== undefined) updateData.is_active = data.isActive;
  if (data.isPremium !== undefined) updateData.is_premium = data.isPremium;
  if (data.sortOrder !== undefined) updateData.sort_order = data.sortOrder;

  updateData.updated_at = new Date().toISOString();

  const result = await supabase
    .from(TABLE_NAMES.voiceModel)
    .update(updateData)
    .eq('id', id)
    .select();

  const { data: voiceModels, error } = handleSupabaseResult(result);
  if (error) throw error;
  return voiceModels || [];
}

// ==================== 声音示例相关操作 ====================

export async function getVoiceSamplesByModelIdRepo(env: Env, voiceModelId: string): Promise<VoiceSample[]> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from(TABLE_NAMES.voiceSample)
    .select('*')
    .eq('voice_model_id', voiceModelId)
    .order('is_default', { ascending: false })
    .order('language');

  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;
  return data || [];
}

export async function getVoiceSampleByLanguageRepo(
  env: Env,
  voiceModelId: string,
  language: string
): Promise<VoiceSample | null> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from(TABLE_NAMES.voiceSample)
    .select('*')
    .eq('voice_model_id', voiceModelId)
    .eq('language', language)
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) return null;
  return data;
}

export async function createVoiceSampleRepo(
  env: Env,
  data: {
    voiceModelId: string;
    language: string;
    sampleText: string;
    audioUrl?: string;
    duration?: number;
    fileSize?: number;
    isDefault?: boolean;
  }
): Promise<VoiceSample[]> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from(TABLE_NAMES.voiceSample)
    .insert({
      voice_model_id: data.voiceModelId,
      language: data.language,
      sample_text: data.sampleText,
      audio_url: data.audioUrl,
      duration: data.duration?.toString(),
      file_size: data.fileSize,
      is_default: data.isDefault || false,
    })
    .select();

  const { data: voiceSamples, error } = handleSupabaseResult(result);
  if (error) throw error;
  return voiceSamples || [];
}

export async function updateVoiceSampleRepo(
  env: Env,
  id: string,
  data: Partial<{
    sampleText: string;
    audioUrl: string;
    duration: number;
    fileSize: number;
    isDefault: boolean;
  }>
): Promise<VoiceSample[]> {
  const supabase = getSupabase(env);

  // 转换字段名为 snake_case
  const updateData: any = {};
  if (data.sampleText !== undefined) updateData.sample_text = data.sampleText;
  if (data.audioUrl !== undefined) updateData.audio_url = data.audioUrl;
  if (data.duration !== undefined) updateData.duration = data.duration.toString();
  if (data.fileSize !== undefined) updateData.file_size = data.fileSize;
  if (data.isDefault !== undefined) updateData.is_default = data.isDefault;

  const result = await supabase
    .from(TABLE_NAMES.voiceSample)
    .update(updateData)
    .eq('id', id)
    .select();

  const { data: voiceSamples, error } = handleSupabaseResult(result);
  if (error) throw error;
  return voiceSamples || [];
}

export async function deleteVoiceSampleRepo(env: Env, id: string): Promise<VoiceSample[]> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from(TABLE_NAMES.voiceSample)
    .delete()
    .eq('id', id)
    .select();

  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;
  return data || [];
}

export async function getVoiceModelWithSamplesRepo(env: Env, id: string): Promise<(VoiceModel & { samples: VoiceSample[] }) | null> {
  // 获取声音模型
  const model = await getVoiceModelByIdRepo(env, id);
  if (!model) return null;

  // 获取示例
  const samples = await getVoiceSamplesByModelIdRepo(env, id);

  return {
    ...model,
    samples,
  };
}

export async function getAllVoiceModelsWithDefaultSamplesRepo(env: Env): Promise<(VoiceModel & { defaultSample: VoiceSample | null })[]> {
  // 获取所有活跃的声音模型
  const models = await getActiveVoiceModelsRepo(env);

  // 为每个模型获取默认示例
  const modelsWithSamples = await Promise.all(
    models.map(async (model) => {
      const supabase = getSupabase(env);
      const result = await supabase
        .from(TABLE_NAMES.voiceSample)
        .select('*')
        .eq('voice_model_id', model.id)
        .eq('is_default', true)
        .limit(1);

      const { data: samples, error } = handleSupabaseResult(result);
      if (error) console.warn('获取默认示例失败', error);

      return {
        ...model,
        defaultSample: samples && samples.length > 0 ? samples[0] : null,
      };
    })
  );

  return modelsWithSamples;
}