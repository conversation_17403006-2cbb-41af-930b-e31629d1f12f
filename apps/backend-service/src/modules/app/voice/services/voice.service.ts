import type { Env } from '@/types/env';
import type { VoiceModel, VoiceSample } from '@/lib/db/schema';
import {
  getActiveVoiceModelsRepo,
  getVoiceModelsByGenderRepo,
  getVoiceModelByIdRepo,
  getVoiceModelByModelIdRepo,
  createVoiceModelRepo,
  updateVoiceModelRepo,
  getVoiceSamplesByModelIdRepo,
  getVoiceSampleByLanguageRepo,
  createVoiceSampleRepo,
  updateVoiceSampleRepo,
  deleteVoiceSampleRepo,
  getVoiceModelWithSamplesRepo,
  getAllVoiceModelsWithDefaultSamplesRepo
} from '../repositories/voice.repository';

// ==================== 声音模型相关操作 ====================

export async function getActiveVoiceModels(env: Env): Promise<VoiceModel[]> {
  try {
    return await getActiveVoiceModelsRepo(env);
  } catch (error) {
    console.error('获取声音模型失败', error);
    throw error;
  }
}

export async function getVoiceModelsByGender(
  env: Env,
  gender: 'male' | 'female' | 'neutral'
): Promise<VoiceModel[]> {
  try {
    return await getVoiceModelsByGenderRepo(env, gender);
  } catch (error) {
    console.error('根据性别获取声音模型失败', error);
    throw error;
  }
}

export async function getVoiceModelById(env: Env, id: string): Promise<VoiceModel | null> {
  try {
    return await getVoiceModelByIdRepo(env, id);
  } catch (error) {
    console.error('获取声音模型详情失败', error);
    throw error;
  }
}

export async function getVoiceModelByModelId(
  env: Env,
  modelId: string
): Promise<VoiceModel | null> {
  try {
    return await getVoiceModelByModelIdRepo(env, modelId);
  } catch (error) {
    console.error('根据modelId获取声音模型失败', error);
    throw error;
  }
}

export async function createVoiceModel(
  env: Env,
  data: {
    modelId: string;
    name: string;
    displayName: string;
    description?: string;
    gender: 'male' | 'female' | 'neutral';
    language?: string;
    supportedLanguages?: string[];
    category?: string;
    tags?: string[];
    isPremium?: boolean;
    sortOrder?: number;
  }
): Promise<VoiceModel[]> {
  try {
    return await createVoiceModelRepo(env, data);
  } catch (error) {
    console.error('创建声音模型失败', error);
    throw error;
  }
}

export async function updateVoiceModel(
  env: Env,
  id: string,
  data: Partial<{
    name: string;
    displayName: string;
    description: string;
    gender: 'male' | 'female' | 'neutral';
    language: string;
    supportedLanguages: string[];
    category: string;
    tags: string[];
    isActive: boolean;
    isPremium: boolean;
    sortOrder: number;
  }>
): Promise<VoiceModel[]> {
  try {
    return await updateVoiceModelRepo(env, id, data);
  } catch (error) {
    console.error('更新声音模型失败', error);
    throw error;
  }
}

// ==================== 声音示例相关操作 ====================

export async function getVoiceSamplesByModelId(
  env: Env,
  voiceModelId: string
): Promise<VoiceSample[]> {
  try {
    return await getVoiceSamplesByModelIdRepo(env, voiceModelId);
  } catch (error) {
    console.error('获取声音示例失败', error);
    throw error;
  }
}

export async function getVoiceSampleByLanguage(
  env: Env,
  voiceModelId: string,
  language: string
): Promise<VoiceSample | null> {
  try {
    return await getVoiceSampleByLanguageRepo(env, voiceModelId, language);
  } catch (error) {
    console.error('获取特定语言声音示例失败', error);
    throw error;
  }
}

export async function createVoiceSample(
  env: Env,
  data: {
    voiceModelId: string;
    language: string;
    sampleText: string;
    audioUrl?: string;
    duration?: number;
    fileSize?: number;
    isDefault?: boolean;
  }
): Promise<VoiceSample[]> {
  try {
    return await createVoiceSampleRepo(env, data);
  } catch (error) {
    console.error('创建声音示例失败', error);
    throw error;
  }
}

export async function updateVoiceSample(
  env: Env,
  id: string,
  data: Partial<{
    sampleText: string;
    audioUrl: string;
    duration: number;
    fileSize: number;
    isDefault: boolean;
  }>
): Promise<VoiceSample[]> {
  try {
    return await updateVoiceSampleRepo(env, id, data);
  } catch (error) {
    console.error('更新声音示例失败', error);
    throw error;
  }
}

export async function deleteVoiceSample(env: Env, id: string): Promise<VoiceSample[]> {
  try {
    return await deleteVoiceSampleRepo(env, id);
  } catch (error) {
    console.error('删除声音示例失败', error);
    throw error;
  }
}

export async function getVoiceModelWithSamples(
  env: Env,
  id: string
): Promise<(VoiceModel & { samples: VoiceSample[] }) | null> {
  try {
    return await getVoiceModelWithSamplesRepo(env, id);
  } catch (error) {
    console.error('获取声音模型及示例失败', error);
    throw error;
  }
}

export async function getAllVoiceModelsWithDefaultSamples(
  env: Env
): Promise<(VoiceModel & { defaultSample: VoiceSample | null })[]> {
  try {
    return await getAllVoiceModelsWithDefaultSamplesRepo(env);
  } catch (error) {
    console.error('获取所有声音模型及默认示例失败', error);
    throw error;
  }
}