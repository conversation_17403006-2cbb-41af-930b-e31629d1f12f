import { Hono } from 'hono'
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { authMiddleware } from '@/middleware/auth';
import {
  getVoiceList,
  getVoiceDetail,
  getVoiceSamples,
  createVoice,
  updateVoice,
  createVoiceSampleHandler,
  updateVoiceSampleHandler,
  deleteVoiceSampleHandler,
  getVoiceCacheStats,
  clearVoiceCache
} from '../controllers/voice.controller';
import type { Env } from '@/types/env';

const route = new Hono<{ Bindings: Env }>();

// ==================== 验证模式 ====================

// 声音模型创建验证模式
const createVoiceModelSchema = z.object({
  modelId: z.string().min(1, '模型ID不能为空').max(100, '模型ID不能超过100个字符'),
  name: z.string().min(1, '声音名称不能为空').max(50, '声音名称不能超过50个字符'),
  displayName: z.string().min(1, '显示名称不能为空').max(100, '显示名称不能超过100个字符'),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
  gender: z.enum(['male', 'female', 'neutral'], {
    errorMap: () => ({ message: '请选择有效的性别' })
  }),
  language: z.string().max(10, '语言代码不能超过10个字符').default('zh-CN'),
  supportedLanguages: z.array(z.string()).optional(),
  category: z.string().max(50, '分类不能超过50个字符').optional(),
  tags: z.array(z.string()).optional(),
  isPremium: z.boolean().default(false),
  sortOrder: z.number().int().min(0).default(0)
});

// 声音模型更新验证模式
const updateVoiceModelSchema = createVoiceModelSchema.partial();

// 声音示例创建验证模式
const createVoiceSampleSchema = z.object({
  language: z.string().min(1, '语言代码不能为空').max(10, '语言代码不能超过10个字符'),
  sampleText: z.string().min(1, '示例文本不能为空'),
  audioUrl: z.string().url('请提供有效的音频URL').optional(),
  duration: z.number().int().min(0).optional(),
  fileSize: z.number().int().min(0).optional(),
  isDefault: z.boolean().default(false)
});

// 声音示例更新验证模式
const updateVoiceSampleSchema = createVoiceSampleSchema.partial();

// ==================== 公开接口路由 ====================

/**
 * 获取所有可用的声音模型（带缓存）
 * GET /api/voices
 */
route.get('/', getVoiceList);

/**
 * 根据ID获取声音模型详情
 * GET /api/voices/:id
 */
route.get('/:id', getVoiceDetail);

/**
 * 获取声音模型的示例音频
 * GET /api/voices/:id/samples
 */
route.get('/:id/samples', getVoiceSamples);

// ==================== 管理员接口路由 ====================

/**
 * 创建声音模型（需要管理员权限）
 * POST /api/voices
 */
route.post('/', authMiddleware, zValidator('json', createVoiceModelSchema), createVoice);

/**
 * 更新声音模型（需要管理员权限）
 * PUT /api/voices/:id
 */
route.put('/:id', authMiddleware, zValidator('json', updateVoiceModelSchema), updateVoice);

/**
 * 为声音模型添加示例音频（需要管理员权限）
 * POST /api/voices/:id/samples
 */
route.post(
  '/:id/samples',
  authMiddleware,
  zValidator('json', createVoiceSampleSchema),
  createVoiceSampleHandler
);

/**
 * 更新声音示例（需要管理员权限）
 * PUT /api/voices/:voiceId/samples/:sampleId
 */
route.put(
  '/:voiceId/samples/:sampleId',
  authMiddleware,
  zValidator('json', updateVoiceSampleSchema),
  updateVoiceSampleHandler
);

/**
 * 删除声音示例（需要管理员权限）
 * DELETE /api/voices/:voiceId/samples/:sampleId
 */
route.delete('/:voiceId/samples/:sampleId', authMiddleware, deleteVoiceSampleHandler);

// ==================== 调试接口路由 ====================

/**
 * 获取声音模型缓存统计（调试用）
 * GET /api/voices/cache/stats
 */
route.get('/cache/stats', getVoiceCacheStats);

/**
 * 清除所有声音模型列表缓存（调试用）
 * DELETE /api/voices/cache/clear
 */
route.delete('/cache/clear', authMiddleware, clearVoiceCache);

export default route;