/**
 * 支付订单业务逻辑服务
 */
import type { Env } from '@/types/env';
import { createPaymentOrder, getPaymentOrderByOrderNo, updatePaymentOrderStatus } from '../repositories/payment.repository';
import { getMembershipPlanById, createUserSubscription, getUserActiveSubscription, updateSubscriptionStatus, updateUserPoints } from '@/lib/db/queries/membership';
import { pointsPackageQueries } from '@/lib/db/queries/points-package';
import { calculateAndCreateCommission } from '@/lib/commission/service';
import { createPointsCycleManager } from '@/lib/membership/points-cycle';
import type { CreateOrderRequest } from '@/lib/payment/real-payment-service';
import { createPaymentService } from '@/lib/payment/payment-factory';
import type { PaymentMethod } from '@/lib/payment/payment-config';

/**
 * 根据订单号处理支付成功（会员订阅或积分包）
 */
export async function activateMembershipByOrderNo(env: Env, orderNo: string) {
  try {
    console.log('🎉 [PAYMENT] 根据订单号处理支付成功:', orderNo);

    // 从数据库获取支付订单信息
    const dbOrder = await getPaymentOrderByOrderNo(env, orderNo);

    if (!dbOrder) {
      throw new Error('支付订单不存在');
    }

    if (dbOrder.status !== 'paid') {
      throw new Error('订单未支付，无法激活会员');
    }

    // 检查是会员计划还是积分包
    if (dbOrder.pointsPackageId) {
      // 处理积分包购买
      console.log('🔍 [PAYMENT] 处理积分包购买:', dbOrder.pointsPackageId);
      await processPointsPackagePurchase(env, dbOrder);
      return;
    }

    if (!dbOrder.planId) {
      throw new Error('订单缺少会员计划信息');
    }

    // 获取会员计划信息
    const plan = await getMembershipPlanById(env, dbOrder.planId);
    if (!plan) {
      throw new Error('会员计划不存在');
    }

    const isUpgrade = dbOrder.isUpgrade;
    const currentSubscriptionId = dbOrder.currentSubscriptionId;

    // 计算订阅期限
    const startDate = new Date();
    const endDate = new Date(startDate.getTime() + plan.durationDays * 24 * 60 * 60 * 1000);

    console.log('🔍 [PAYMENT] 准备创建订阅:', {
      userId: dbOrder.userId,
      planId: plan.id,
      paymentId: dbOrder.externalOrderId,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      isUpgrade,
      currentSubscriptionId,
    });

    // 如果是升级，先获取旧订阅信息（在取消之前）
    let oldSubscription = null;
    if (isUpgrade && currentSubscriptionId) {
      console.log('🔍 [PAYMENT] 获取旧订阅信息:', currentSubscriptionId);
      oldSubscription = await getUserActiveSubscription(env, dbOrder.userId);
      console.log('📋 [PAYMENT] 旧订阅信息:', {
        id: oldSubscription?.id,
        planId: oldSubscription?.planId,
        endDate: oldSubscription?.endDate,
      });
    }

    // 如果是升级，取消当前订阅
    if (isUpgrade && currentSubscriptionId) {
      console.log('🔄 [PAYMENT] 取消现有订阅:', currentSubscriptionId);
      await updateSubscriptionStatus(env, currentSubscriptionId, 'cancelled');
    }

    // 创建用户订阅
    const subscription = await createUserSubscription(env, {
      userId: dbOrder.userId,
      planId: plan.id,
      startDate,
      endDate,
      status: 'active' as const,
      paymentId: dbOrder.externalOrderId || orderNo,
      autoRenew: false,
    });

    console.log('✅ [PAYMENT] 订阅创建成功:', subscription[0]);

    // 处理积分（升级 vs 新订阅）
    if (isUpgrade && oldSubscription && currentSubscriptionId) {
      await handleUpgradePoints(
        env,
        dbOrder.userId,
        plan,
        dbOrder.externalOrderId || orderNo,
        oldSubscription,
        currentSubscriptionId
      );
    } else {
      await grantMembershipPoints(env, dbOrder.userId, plan, dbOrder.externalOrderId || orderNo);
    }

    console.log('🎊 [PAYMENT] 会员激活完成:', {
      userId: dbOrder.userId,
      planName: plan.name,
      pointsGranted: plan.pointsIncluded,
      validUntil: endDate,
      isUpgrade,
    });

    // 计算和创建佣金记录
    try {
      const commissionResult = await calculateAndCreateCommission(env, dbOrder.id);
      if (
        commissionResult.success &&
        commissionResult.commissionAmount &&
        commissionResult.commissionAmount > 0
      ) {
        console.log('💰 [COMMISSION] 会员订阅佣金结算成功:', {
          orderId: dbOrder.id,
          orderNo: dbOrder.orderNo,
          inviterId: commissionResult.inviterId,
          commissionAmount: commissionResult.commissionAmount,
        });
      }
    } catch (commissionError) {
      console.error('❌ [COMMISSION] 佣金计算失败，但不影响会员激活:', commissionError);
      // 佣金计算失败不影响会员激活流程
    }
  } catch (error) {
    console.error('❌ [PAYMENT] 根据订单号激活会员订阅失败:', error);
    throw error;
  }
}

/**
 * 发放会员积分
 */
export async function grantMembershipPoints(env: Env, userId: string, plan: any, paymentId: string) {
  try {
    // 发放积分并记录交易
    await updateUserPoints(
      env,
      userId,
      plan.pointsIncluded,
      'earn',
      'subscription',
      undefined,
      `订阅${plan.name}获得积分 (支付ID: ${paymentId})`
    );

    console.log('💰 [PAYMENT] 积分发放成功:', {
      userId,
      pointsGranted: plan.pointsIncluded,
      planName: plan.name,
      paymentId,
    });
  } catch (error) {
    console.error('❌ [PAYMENT] 积分发放失败:', error);
    throw error;
  }
}

/**
 * 处理升级积分
 */
export async function handleUpgradePoints(
  env: Env,
  userId: string,
  plan: any,
  paymentId: string,
  oldSubscription: any,
  oldSubscriptionId: string
) {
  try {
    console.log('🚀 [PAYMENT] 处理升级积分:', { userId, planName: plan.name });

    const cycleManager = createPointsCycleManager(env);

    // 映射计划名称到会员等级
    const levelMap: Record<string, 'FREE' | 'PRO' | 'ELITE' | 'ULTRA'> = {
      free: 'FREE',
      Free: 'FREE',
      pro: 'PRO',
      Pro: 'PRO',
      elite: 'ELITE',
      Elite: 'ELITE',
      ultra: 'ULTRA',
      Ultra: 'ULTRA',
    };

    const toLevel = levelMap[plan.name] || 'PRO';

    // 获取旧订阅的会员等级
    let fromLevel: 'FREE' | 'PRO' | 'ELITE' | 'ULTRA' = 'FREE';
    if (oldSubscription) {
      const oldPlan = await getMembershipPlanById(env, oldSubscription.planId);
      console.log('🔍 [PAYMENT] 旧订阅计划信息:', {
        oldSubscriptionId,
        oldPlanId: oldSubscription.planId,
        oldPlanName: oldPlan?.name,
        oldPlanMapping: oldPlan ? levelMap[oldPlan.name] : 'not found',
      });
      if (oldPlan) {
        fromLevel = levelMap[oldPlan.name] || 'FREE';
      }
    }

    console.log('🔍 [PAYMENT] 目标计划信息:', {
      targetPlanName: plan.name,
      targetPlanMapping: levelMap[plan.name],
    });

    // 计算剩余天数
    const now = new Date();
    const endDate = new Date(oldSubscription.endDate);
    const remainingDays = Math.max(
      0,
      Math.floor((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    );

    console.log('🔍 [PAYMENT] 升级积分计算参数:', {
      fromLevel,
      toLevel,
      remainingDays,
      userId,
    });

    // 使用升级补差逻辑
    const result = await cycleManager.handleMembershipUpgrade(
      userId,
      fromLevel,
      toLevel,
      remainingDays
    );

    console.log('💰 [PAYMENT] 升级积分处理成功:', {
      userId,
      planName: plan.name,
      fromLevel,
      toLevel,
      bonusPoints: result.bonusPoints,
      paymentId,
    });
  } catch (error) {
    console.error('❌ [PAYMENT] 升级积分处理失败:', error);
    throw error;
  }
}

/**
 * 处理积分包购买
 */
export async function processPointsPackagePurchase(env: Env, dbOrder: any) {
  try {
    console.log('💰 [PAYMENT] 开始处理积分包购买:', {
      orderId: dbOrder.orderNo,
      userId: dbOrder.userId,
      pointsPackageId: dbOrder.pointsPackageId,
    });

    // 获取积分包信息
    const pointsPackage = await pointsPackageQueries.getPackageById(env, dbOrder.pointsPackageId);
    if (!pointsPackage) {
      throw new Error('积分包不存在');
    }

    console.log('📦 [PAYMENT] 积分包信息:', {
      id: pointsPackage.id,
      name: pointsPackage.name,
      points: pointsPackage.points,
      bonusPoints: pointsPackage.bonusPoints || 0,
    });

    // 计算总积分 = 基础积分 + 赠送积分
    const totalPoints = pointsPackage.points + (pointsPackage.bonusPoints || 0);

    // 发放积分
    console.log('💰 [PAYMENT] 开始发放积分:', {
      userId: dbOrder.userId,
      totalPoints,
      basePoints: pointsPackage.points,
      bonusPoints: pointsPackage.bonusPoints || 0,
    });

    const updatedPoints = await updateUserPoints(
      env,
      dbOrder.userId,
      totalPoints,
      'earn',
      'purchase',
      undefined,
      `购买${pointsPackage.name}获得积分 (订单: ${dbOrder.orderNo})`
    );

    console.log('✅ [PAYMENT] 积分发放成功:', {
      userId: dbOrder.userId,
      newAvailablePoints: updatedPoints.availablePoints,
      newTotalPoints: updatedPoints.totalPoints,
    });

    console.log('✅ [PAYMENT] 积分包处理成功:', {
      userId: dbOrder.userId,
      packageName: pointsPackage.name,
      pointsGranted: totalPoints,
      basePoints: pointsPackage.points,
      bonusPoints: pointsPackage.bonusPoints || 0,
      orderId: dbOrder.orderNo,
    });

    // 计算和创建佣金记录
    try {
      const commissionResult = await calculateAndCreateCommission(env, dbOrder.id);
      if (
        commissionResult.success &&
        commissionResult.commissionAmount &&
        commissionResult.commissionAmount > 0
      ) {
        console.log('💰 [COMMISSION] 积分包购买佣金结算成功:', {
          orderId: dbOrder.id,
          orderNo: dbOrder.orderNo,
          inviterId: commissionResult.inviterId,
          commissionAmount: commissionResult.commissionAmount,
        });
      }
    } catch (commissionError) {
      console.error('❌ [COMMISSION] 佣金计算失败，但不影响积分包处理:', commissionError);
    }
  } catch (error) {
    console.error('❌ [PAYMENT] 积分包处理失败:', error);
    throw error;
  }
}

/**
 * 创建支付订单（包含业务逻辑）
 */
export async function createPaymentOrderWithLogic(env: Env, orderData: CreateOrderRequest) {
  try {
    const { amount, currency = 'CNY', description, userId, planId, paymentMethod, metadata } = orderData;

    // 获取目标会员计划信息
    const targetPlan = await getMembershipPlanById(env, planId!);
    if (!targetPlan) {
      throw new Error('会员计划不存在');
    }

    // 生成订单号
    const orderNo = `ORDER_${Date.now()}_${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
    const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30分钟后过期

    // 创建支付服务
    const paymentService = createPaymentService(env);
    const orderResult = await paymentService.createOrder({
      amount,
      currency,
      description,
      userId,
      planId: planId!,
      paymentMethod: paymentMethod as PaymentMethod,
      metadata,
    });

    if (!orderResult.success) {
      throw new Error(orderResult.error);
    }

    return {
      success: true,
      orderNo,
      paymentResult: orderResult,
    };
  } catch (error) {
    console.error('❌ [PAYMENT] 创建支付订单业务逻辑失败:', error);
    throw error;
  }
}