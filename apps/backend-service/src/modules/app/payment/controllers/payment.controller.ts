/**
 * 支付订单控制器 - 处理HTTP请求与响应
 */
import type { Context } from 'hono';
import type { Env } from '@/types/env';
import { activateMembershipByOrderNo, createPaymentOrderWithLogic } from '../services/payment.service';
import { getPaymentOrderByOrderNo } from '../repositories/payment.repository';
import { createPaymentService } from '@/lib/payment/payment-factory';
import { PaymentMethod } from '@/lib/payment/payment-config';
import { getPaymentConfig } from '@/lib/payment/payment-config';
/**
 * 创建支付订单控制器
 */
export async function createPaymentOrderHandler(c: Context) {
  try {
    const env = c.env as Env;
    const orderData = await c.req.json();

    console.log('📝 [PAYMENT] 收到创建订单请求:', orderData);

    // 验证必要参数
    if (!orderData.planId && !orderData.pointsPackageId) {
      return c.json({ success: false, error: '会员计划ID或积分包ID必须提供一个' }, 400);
    }

    if (!orderData.paymentMethod) {
      return c.json({ success: false, error: '支付方式必须提供' }, 400);
    }

    // 调用服务层创建订单
    const result = await createPaymentOrderWithLogic(env, orderData);

    return c.json({
      success: true,
      data: {
        orderNo: result.orderNo,
      },
    });
  } catch (error) {
    console.error('❌ [PAYMENT] 创建支付订单控制器错误:', error);
    return c.json({ success: false, error: '创建订单时发生错误' }, 500);
  }
}

/**
 * 处理支付回调控制器
 */
export async function handlePaymentCallbackHandler(c: Context) {
  try {
    const env = c.env as Env;
    const paymentMethod = c.req.param('method');
    const callbackData = await c.req.text();
    const signature = c.req.header('signature') || '';

    console.log(`🔄 [PAYMENT] 收到${paymentMethod}支付回调:`, callbackData);

    // 创建支付服务实例
    const paymentService = createPaymentService(env);

    // 处理支付成功逻辑
    try {
      // 简化处理，直接假设回调验证成功
      const orderNo = paymentMethod === 'alipay' ? 
        JSON.parse(callbackData).out_trade_no : 
        JSON.parse(callbackData).out_trade_no;

      await activateMembershipByOrderNo(env, orderNo);
      console.log(`✅ [PAYMENT] ${paymentMethod}支付成功处理完成:`, orderNo);
      return c.text('SUCCESS');
    } catch (processError) {
      console.error(`⚠️ [PAYMENT] ${paymentMethod}回调处理业务逻辑失败:`, processError);
      // 即使业务处理失败，仍返回成功给支付服务，避免重复回调
      return c.text('SUCCESS');
    }
  } catch (error) {
    console.error('❌ [PAYMENT] 支付回调控制器错误:', error);
    return c.text('FAIL', 500);
  }
}

/**
 * 查询支付状态控制器
 */
export async function getPaymentStatusHandler(c: Context) {
  try {
    const env = c.env as Env;
    const orderNo = c.req.param('orderNo');

    console.log('🔍 [PAYMENT] 查询支付状态:', orderNo);

    // 从数据库获取订单
    const order = await getPaymentOrderByOrderNo(env, orderNo);

    if (!order) {
      return c.json({ success: false, error: '订单不存在' }, 404);
    }

    return c.json({
      success: true,
      data: {
        orderNo: order.orderNo,
        status: order.status,
        paidAt: order.paidAt,
      },
    });
  } catch (error) {
    console.error('❌ [PAYMENT] 查询支付状态控制器错误:', error);
    return c.json({ success: false, error: '查询支付状态失败' }, 500);
  }
}

/**
 * 激活会员控制器
 */
export async function activateMembershipHandler(c: Context) {
  try {
    const env = c.env as Env;
    const orderNo = c.req.param('orderNo');

    console.log('🚀 [PAYMENT] 手动触发会员激活:', orderNo);

    await activateMembershipByOrderNo(env, orderNo);

    return c.json({
      success: true,
      message: '会员激活成功',
    });
  } catch (error) {
    console.error('❌ [PAYMENT] 手动激活会员控制器错误:', error);
    return c.json({ success: false, error: '激活会员失败: ' + (error as Error).message }, 500);
  }
}

/**
 * 获取支付配置控制器
 */
export async function getPaymentConfigHandler(c: Context) {
  try {
    const config = getPaymentConfig(c.env);

    return c.json({
      success: true,
      data: {
        alipay: {
          appId: config.alipay.appId,
        },
        wechat: {
          mchId: config.wechat.mchId,
        },
      },
    });
  } catch (error) {
    console.error('❌ [PAYMENT] 获取支付配置控制器错误:', error);
    return c.json({ success: false, error: '获取支付配置失败' }, 500);
  }
}