/**
 * 支付订单路由 - 定义API端点
 */
import { Hono } from 'hono';
import {
  createPaymentOrderHandler,
  handlePaymentCallbackHandler,
  getPaymentStatusHandler,
  activateMembershipHandler,
  getPaymentConfigHandler
} from '../controllers/payment.controller';
// 创建支付路由实例
const paymentRoute = new Hono();

// 公开路由
paymentRoute.get('/config', getPaymentConfigHandler);
paymentRoute.post('/callback/:method', handlePaymentCallbackHandler);
paymentRoute.get('/status/:orderNo', getPaymentStatusHandler);

// 需要认证的路由

paymentRoute.post('/create-order', createPaymentOrderHandler);
paymentRoute.post('/activate/:orderNo', activateMembershipHandler);

// 导出路由
export default paymentRoute;