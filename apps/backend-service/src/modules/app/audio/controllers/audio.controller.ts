import { Context } from 'hono'
import type { Env } from '@/types/env'
import { z } from 'zod'
import { audioEffectQuerySchema, createAudioEffectSchema, updateAudioEffectSchema, audioCategoryQuerySchema, createAudioCategorySchema, updateAudioCategorySchema, searchAudioSchema, randomAudioSchema, batchCreateAudioEffectsSchema } from '../validators/audio.validator';
// 定义ID参数验证schema
const idParamSchema = z.string().uuid({ message: '无效的ID格式' });
import { getAudioEffects, getAudioEffectById, createAudioEffect, updateAudioEffect, deleteAudioEffect, getAudioCategories, getAudioCategoryById, createAudioCategory, updateAudioCategory, deleteAudioCategory, searchAudioByTags, getRandomAudioEffects, getAllAudioTags, getAudioStatistics, batchCreateAudioEffects } from '../services/audio.service'

// 音频效果控制器
export const getAudioEffectHandler = async (c: Context<{ Bindings: Env; ValidatedQuery: z.infer<typeof audioEffectQuerySchema> }>) => {
  const query = c.req.valid('query')
  const result = await getAudioEffects(c.env, query)
  return c.json(result)
}

export const createAudioEffectHandler = async (c: Context<{ Bindings: Env; ValidatedJson: z.infer<typeof createAudioEffectSchema> }>) => {
  const data = c.req.valid('json')
  const audioEffect = await createAudioEffect(c.env, data)
  return c.json({ audioEffect }, 201)
}

export const updateAudioEffectHandler = async (c: Context<{ Bindings: Env; ValidatedJson: z.infer<typeof updateAudioEffectSchema> }>) => {
  const id = idParamSchema.parse(c.req.param('id'))
  const data = c.req.valid('json')
  try {
  const audioEffect = await updateAudioEffect(c.env, id, data)
  return audioEffect ? c.json({ data: audioEffect }) : c.notFound()
} catch (error) {
  return c.json({ error: error instanceof Error ? error.message : '更新失败' }, 400)
}

}

export const deleteAudioEffectHandler = async (c: Context<{ Bindings: Env }>) => {
  const id = idParamSchema.parse(c.req.param('id'))
  try {
  const success = await deleteAudioEffect(c.env, id)
  return success ? c.json({ message: '音频效果已删除', data: null }) : c.notFound()
} catch (error) {
  return c.json({ error: error instanceof Error ? error.message : '删除失败' }, 400)
}

}

// 音频分类控制器

// 音频搜索控制器
export const searchAudioHandler = async (c: Context<{ Bindings: Env; ValidatedQuery: z.infer<typeof searchAudioSchema> }>) => {
  const { tags, limit } = c.req.valid('query')
  const result = await searchAudioByTags(c.env, tags, { limit })
  return c.json(result)
}

// 随机音频控制器
export const getRandomAudioHandler = async (c: Context<{ Bindings: Env; ValidatedQuery: z.infer<typeof randomAudioSchema> }>) => {
  const { limit } = c.req.valid('query')
  const result = await getRandomAudioEffects(c.env, { limit })
  return c.json(result)
}

// 音频标签控制器
export const getAllAudioTagsHandler = async (c: Context<{ Bindings: Env }>) => {
  const tags = await getAllAudioTags(c.env)
  return c.json({ tags })
}

// 音频统计控制器
export const getAudioStatsHandler = async (c: Context<{ Bindings: Env }>) => {
  const stats = await getAudioStatistics(c.env)
  return c.json({ stats })
}

// 批量创建音频控制器
export const batchCreateAudioHandler = async (c: Context<{ Bindings: Env; ValidatedJson: z.infer<typeof batchCreateAudioEffectsSchema> }>) => {
  const audioEffects = c.req.valid('json')
  try {
  const result = await batchCreateAudioEffects(c.env, audioEffects)
  return c.json({ data: result, count: result.length }, 201)
} catch (error) {
  return c.json({ error: error instanceof Error ? error.message : '批量创建失败' }, 400)
}

}

// 音频分类控制器
export const getAudioCategoryHandler = async (c: Context<{ Bindings: Env; ValidatedQuery: z.infer<typeof audioCategoryQuerySchema> }>) => {
  const query = c.req.valid('query')
  const categories = await getAudioCategories(c.env, query)
  return c.json({ categories })
}

export const createAudioCategoryHandler = async (c: Context<{ Bindings: Env; ValidatedJson: z.infer<typeof createAudioCategorySchema> }>) => {
  const data = c.req.valid('json')
  const category = await createAudioCategory(c.env, data)
  return c.json({ category }, 201)
}

export const updateAudioCategoryHandler = async (c: Context<{ Bindings: Env; ValidatedJson: z.infer<typeof updateAudioCategorySchema> }>) => {
  const id = idParamSchema.parse(c.req.param('id'))
  const data = c.req.valid('json')
  const category = await updateAudioCategory(c.env, id, data)
  return category ? c.json({ category }) : c.notFound()
}

export const deleteAudioCategoryHandler = async (c: Context<{ Bindings: Env }>) => {
  const id = idParamSchema.parse(c.req.param('id'))
  const success = await deleteAudioCategory(c.env, id)
  return success ? c.json({ message: '音频分类已删除' }) : c.notFound()
}