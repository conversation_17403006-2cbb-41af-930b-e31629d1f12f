import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, handleSupabaseSingleResult } from '@/lib/db/supabase-types'
import type { Env } from '@/types/env'
import type { AudioEffect, AudioCategory } from '@/lib/db/schema'
import type { AudioEffectQuery, CreateAudioEffectRequest, UpdateAudioEffectRequest, AudioCategoryQuery, CreateAudioCategoryRequest, UpdateAudioCategoryRequest, PaginatedResponse } from '@/types/audio'

// 音频效果仓库

export async function incrementAudioUsageFromDB(env: Env, id: string): Promise<void> {
  const supabase = getSupabase(env);

  // 先查询当前使用次数
  const currentResult = await supabase
    .from('AudioEffect')
    .select('usage_count')
    .eq('id', id)
    .single();

  const { data: current, error } = handleSupabaseSingleResult(currentResult);
  if (error || !current) return;

  // 更新使用次数
  await supabase
    .from('AudioEffect')
    .update({
      usage_count: (current.usage_count || 0) + 1,
      updated_at: new Date().toISOString(),
    })
    .eq('id', id);
}

// 通用音频搜索函数
export async function searchAudioEffectsFromDB(
  env: Env,
  searchTerm: string,
  options: {
    limit?: number;
    categoryId?: string;
    tags?: string[];
  } = {}
): Promise<any> {
  const supabase = getSupabase(env);
  const { limit = 50, categoryId, tags = [] } = options;

  let queryBuilder = supabase
    .from('AudioEffect')
    .select('*')
    .eq('is_active', true)
    .eq('is_public', true);

  // 搜索条件
  if (searchTerm) {
    queryBuilder = queryBuilder.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
  }

  if (categoryId) {
    queryBuilder = queryBuilder.eq('category_id', categoryId);
  }

  if (tags.length > 0) {
    tags.forEach(tag => queryBuilder = queryBuilder.contains('tags', [tag]));
  }

  queryBuilder = queryBuilder.order('usage_count', { ascending: false }).limit(limit);

  const result = await queryBuilder;
  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;

  return { data: data || [], total: (data || []).length };
}

// 数据验证辅助函数
export function validateAudioEffectData(data: any): string[] {
  const errors: string[] = [];

  if (data.name && data.name.length > 100) {
    errors.push('音效名称不能超过100个字符');
  }

  if (data.duration !== undefined && data.duration <= 0) {
    errors.push('音效时长必须大于0');
  }

  if (data.url && !isValidUrl(data.url)) {
    errors.push('音效URL格式不正确');
  }

  return errors;
}

function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// 音频效果仓库
export async function getAudioEffectsFromDB(env: Env, query: AudioEffectQuery): Promise<PaginatedResponse<AudioEffect>> {
  const supabase = getSupabase(env)
  const { page = 1, pageSize = 20, tags = [], search, sortBy = 'createdAt', sortOrder = 'desc', isActive = true, categoryId } = query
  const offset = (page - 1) * pageSize

  let queryBuilder = supabase.from('AudioEffect').select('*', { count: 'exact' })

  if (isActive !== undefined) queryBuilder = queryBuilder.eq('is_active', isActive)
  if (categoryId) queryBuilder = queryBuilder.eq('category_id', categoryId)
  if (search) queryBuilder = queryBuilder.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
  if (tags.length > 0) tags.forEach(tag => queryBuilder = queryBuilder.contains('tags', [tag]))

  const sortColumn = sortBy === 'createdAt' ? 'created_at' : sortBy === 'usageCount' ? 'usage_count' : sortBy === 'duration' ? 'duration' : sortBy === 'avgPitch' ? 'avg_pitch' : sortBy === 'avgLoudness' ? 'avg_loudness' : 'created_at'
  queryBuilder = queryBuilder.order(sortColumn, { ascending: sortOrder === 'asc' })
  queryBuilder = queryBuilder.range(offset, offset + pageSize - 1)

  const result = await queryBuilder
  const { data, error, count } = result
  if (error) throw error

  const { data: convertedData } = handleSupabaseResult({ data, error: null })
  const total = count || 0
  const totalPages = Math.ceil(total / pageSize)

  return { data: convertedData || [], pagination: { page, pageSize, total, totalPages } }
}

export async function getAudioEffectByIdFromDB(env: Env, id: string): Promise<AudioEffect | null> {
  const supabase = getSupabase(env)
  const result = await supabase.from('AudioEffect').select('*').eq('id', id).single()
  const { data, error } = handleSupabaseSingleResult(result)
  return error ? null : data
}

export async function createAudioEffectFromDB(env: Env, data: CreateAudioEffectRequest): Promise<AudioEffect> {
  const supabase = getSupabase(env)
  const result = await supabase
    .from('AudioEffect')
    .insert({
      name: data.name,
      description: data.description,
      category_id: data.categoryId,
      tags: data.tags,
      url: data.url,
      duration: data.duration.toString(),
      avg_pitch: data.avgPitch?.toString(),
      avg_loudness: data.avgLoudness?.toString(),
      energy_variation: data.energyVariation?.toString(),
      is_public: data.isPublic ?? true
    })
    .select()
    .single()

  const { data: audioEffect, error } = handleSupabaseSingleResult(result)
  if (error) throw error
  return audioEffect
}

export async function updateAudioEffectFromDB(env: Env, id: string, data: UpdateAudioEffectRequest): Promise<AudioEffect | null> {
  const supabase = getSupabase(env)
  const updateData: any = { updated_at: new Date().toISOString() }
  if (data.name !== undefined) updateData.name = data.name
  if (data.description !== undefined) updateData.description = data.description
  if (data.categoryId !== undefined) updateData.category_id = data.categoryId
  if (data.tags !== undefined) updateData.tags = data.tags
  if (data.url !== undefined) updateData.url = data.url
  if (data.duration !== undefined) updateData.duration = data.duration.toString()
  if (data.avgPitch !== undefined) updateData.avg_pitch = data.avgPitch?.toString()
  if (data.avgLoudness !== undefined) updateData.avg_loudness = data.avgLoudness?.toString()
  if (data.energyVariation !== undefined) updateData.energy_variation = data.energyVariation?.toString()
  if (data.isPublic !== undefined) updateData.is_public = data.isPublic
  if (data.isActive !== undefined) updateData.is_active = data.isActive

  const result = await supabase.from('AudioEffect').update(updateData).eq('id', id).select().single()
  const { data: audioEffect, error } = handleSupabaseSingleResult(result)
  return error ? null : audioEffect
}

export async function deleteAudioEffectFromDB(env: Env, id: string): Promise<boolean> {
  const supabase = getSupabase(env)
  const result = await supabase.from('AudioEffect').delete().eq('id', id)
  return !result.error
}

// 音频搜索与统计仓库

export async function searchAudioByTagsFromDB(env: Env, tags: string[], options: { limit?: number; categoryId?: string }) {
  const supabase = getSupabase(env);
  const { limit = 50, categoryId } = options;

  let queryBuilder = supabase
    .from('AudioEffect')
    .select('*')
    .eq('is_active', true)
    .eq('is_public', true);

  if (categoryId) {
    queryBuilder = queryBuilder.eq('category_id', categoryId);
  }

  if (tags.length > 0) {
    tags.forEach(tag => queryBuilder = queryBuilder.contains('tags', [tag]));
  }

  queryBuilder = queryBuilder.order('usage_count', { ascending: false }).limit(limit);

  const result = await queryBuilder;
  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;

  return { data: data || [], total: (data || []).length };
}

export async function getRandomAudioEffectsFromDB(env: Env, options: { limit?: number; categoryId?: string; tags?: string[] } = {}) {
  const supabase = getSupabase(env);
  const { limit = 10, categoryId, tags = [] } = options;

  let queryBuilder = supabase
    .from('AudioEffect')
    .select('*')
    .eq('is_active', true)
    .eq('is_public', true);

  if (categoryId) {
    queryBuilder = queryBuilder.eq('category_id', categoryId);
  }

  if (tags.length > 0) {
    tags.forEach(tag => queryBuilder = queryBuilder.contains('tags', [tag]));
  }

  // 获取更多结果然后随机选择
  const result = await queryBuilder.limit(limit * 3);
  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;

  // 客户端随机排序并限制数量
  const shuffled = (data || []).sort(() => Math.random() - 0.5).slice(0, limit);

  return { data: shuffled, count: shuffled.length };
}

export async function getAllAudioTagsFromDB(env: Env): Promise<string[]> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from('AudioEffect')
    .select('tags')
    .eq('is_active', true)
    .eq('is_public', true);

  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;

  const allTags = new Set<string>();
  (data || []).forEach((item: any) => {
    if (item.tags && Array.isArray(item.tags)) {
      item.tags.forEach((tag: string) => allTags.add(tag));
    }
  });

  return Array.from(allTags);
}

export async function getAudioStatisticsFromDB(env: Env): Promise<any> {
  const supabase = getSupabase(env);

  // 总音频数
  const { count: totalAudios } = await supabase
    .from('AudioEffect')
    .select('*', { count: 'exact', head: true })
    .eq('is_active', true);

  // 总分类数
  const { count: totalCategories } = await supabase
    .from('AudioCategory')
    .select('*', { count: 'exact', head: true })
    .eq('is_active', true);

  // 音频详情（用于计算平均时长和总使用量）
  const audioDetailsResult = await supabase
    .from('AudioEffect')
    .select('duration, usage_count')
    .eq('is_active', true);

  const { data: audioDetails } = handleSupabaseResult(audioDetailsResult);

  const avgDuration =
    audioDetails && audioDetails.length > 0
      ? audioDetails.reduce((sum: number, item: any) => sum + (Number(item.duration) || 0), 0) /
        audioDetails.length
      : 0;

  const totalUsage = audioDetails
    ? audioDetails.reduce((sum: number, item: any) => sum + (item.usage_count || 0), 0)
    : 0;

  return {
    totalAudios: totalAudios || 0,
    avgDuration,
    categoriesCount: totalCategories || 0,
    totalUsage
  };
}

export async function batchCreateAudioEffectsFromDB(env: Env, effects: any[]): Promise<any[]> {
  const results = [];
  for (const effect of effects) {
    try {
      const result = await createAudioEffectFromDB(env, effect);
      results.push(result);
    } catch (error) {
      console.error('批量创建音频效果失败:', error);
      results.push(null);
    }
  }
  return results;
}

// 音频分类仓库
export async function getAudioCategoriesFromDB(env: Env, query: AudioCategoryQuery = {}): Promise<AudioCategory[]> {
  const supabase = getSupabase(env)
  const { isActive = true, parentId } = query
  let queryBuilder = supabase.from('AudioCategory').select('*').order('sort_order')

  if (isActive !== undefined) queryBuilder = queryBuilder.eq('is_active', isActive)
  if (parentId !== undefined) queryBuilder = parentId === null ? queryBuilder.is('parent_id', null) : queryBuilder.eq('parent_id', parentId)

  const result = await queryBuilder
  const { data, error } = handleSupabaseResult(result)
  if (error) throw error
  return data || []
}

export async function getAudioCategoryByIdFromDB(env: Env, id: string): Promise<AudioCategory | null> {
  const supabase = getSupabase(env)
  const result = await supabase.from('AudioCategory').select('*').eq('id', id).single()
  const { data, error } = handleSupabaseSingleResult(result)
  return error ? null : data
}

export async function createAudioCategoryFromDB(env: Env, data: CreateAudioCategoryRequest): Promise<AudioCategory> {
  const supabase = getSupabase(env)
  const result = await supabase
    .from('AudioCategory')
    .insert({
      name: data.name,
      display_name: data.displayName,
      description: data.description,
      parent_id: data.parentId,
      sort_order: data.sortOrder || 0
    })
    .select()
    .single()

  const { data: category, error } = handleSupabaseSingleResult(result)
  if (error) throw error
  return category
}

export async function updateAudioCategoryFromDB(env: Env, id: string, data: UpdateAudioCategoryRequest): Promise<AudioCategory | null> {
  const supabase = getSupabase(env)
  const updateData: any = { updated_at: new Date().toISOString() }
  if (data.name !== undefined) updateData.name = data.name
  if (data.displayName !== undefined) updateData.display_name = data.displayName
  if (data.description !== undefined) updateData.description = data.description
  if (data.parentId !== undefined) updateData.parent_id = data.parentId
  if (data.sortOrder !== undefined) updateData.sort_order = data.sortOrder
  if (data.isActive !== undefined) updateData.is_active = data.isActive

  const result = await supabase.from('AudioCategory').update(updateData).eq('id', id).select().single()
  const { data: category, error } = handleSupabaseSingleResult(result)
  return error ? null : category
}

export async function deleteAudioCategoryFromDB(env: Env, id: string): Promise<boolean> {
  const supabase = getSupabase(env)
  const result = await supabase.from('AudioCategory').delete().eq('id', id)
  return !result.error
}