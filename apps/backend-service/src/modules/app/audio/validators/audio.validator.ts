import { z } from 'zod'

// 音频效果查询验证
export const audioEffectQuerySchema = z.object({
  page: z
    .string()
    .optional()
    .transform((val) => (val ? Number.parseInt(val) : 1)),
  pageSize: z
    .string()
    .optional()
    .transform((val) => (val ? Number.parseInt(val) : 20)),
  tags: z
    .string()
    .optional()
    .transform((val) => (val ? val.split(',').filter(Boolean) : [])),
  search: z.string().optional(),
  sortBy: z
    .enum(['id', 'duration', 'avgPitch', 'avgLoudness', 'createdAt', 'usageCount'])
    .optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  isActive: z
    .string()
    .optional()
    .transform((val) => val !== 'false'),
  categoryId: z.string().optional(),
})

// 音频效果创建验证
export const createAudioEffectSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  categoryId: z.string().optional(),
  tags: z.array(z.string()),
  url: z.string().url(),
  duration: z.number().positive(),
  avgPitch: z.number().optional(),
  avgLoudness: z.number().optional(),
  energyVariation: z.number().optional(),
  isPublic: z.boolean().optional(),
})

// 音频效果更新验证
export const updateAudioEffectSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  categoryId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  url: z.string().url().optional(),
  duration: z.number().positive().optional(),
  avgPitch: z.number().optional(),
  avgLoudness: z.number().optional(),
  energyVariation: z.number().optional(),
  isPublic: z.boolean().optional(),
  isActive: z.boolean().optional(),
})

// 搜索音频验证
export const searchAudioSchema = z.object({
  tags: z.string().min(1).transform((val: string): string[] => val.split(',').filter(Boolean)),
  limit: z.string().optional().transform((val: string | undefined): number => val ? Number.parseInt(val, 10) : 20)
})

// 随机音频验证
export const randomAudioSchema = z.object({
  limit: z.string().optional().transform((val: string | undefined): number => val ? Number.parseInt(val, 10) : 10)
})

// 音频分类查询验证
export const audioCategoryQuerySchema = z.object({
  parentId: z
    .string()
    .optional()
    .transform((val) => (val === 'null' ? null : val)),
  isActive: z
    .string()
    .optional()
    .transform((val) => val !== 'false'),
})

// 音频分类创建验证
export const createAudioCategorySchema = z.object({
  name: z.string().min(1),
  displayName: z.string().optional(),
  description: z.string().optional(),
  parentId: z.string().optional(),
  sortOrder: z.number().optional(),
})

// 批量创建音频效果验证
export const batchCreateAudioEffectsSchema = z.array(createAudioEffectSchema);

// 音频分类更新验证
export const updateAudioCategorySchema = z.object({
  name: z.string().min(1).optional(),
  displayName: z.string().optional(),
  description: z.string().optional(),
  parentId: z.string().optional(),
  sortOrder: z.number().optional(),
  isActive: z.boolean().optional(),
})