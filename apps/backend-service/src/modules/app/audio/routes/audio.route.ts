import { Hono } from 'hono'
import { z } from 'zod'
import { getAudioEffectHandler, createAudioEffectHandler, updateAudioEffectHandler, deleteAudioEffectHandler, getAudioCategoryHandler, createAudioCategoryHandler, update<PERSON>udioCategoryHandler, deleteAudioCategoryHandler, searchAudioHandler, getRandomAudioHandler, getAllAudioTagsHandler, getAudioStatsHandler, batchCreateAudioHandler } from '../controllers/audio.controller'
import { searchAudioSchema, randomAudioSchema } from '../validators/audio.validator'
import { authMiddleware } from '@/middleware/auth'
import { zValidator } from '@hono/zod-validator'
import { audioEffectQuerySchema, createAudioEffectSchema, updateAudioEffectSchema, audioCategoryQuerySchema, createAudioCategorySchema, updateAudioCategorySchema } from '../validators/audio.validator'

const route = new Hono()

// 音频效果路由
route.get('/', zValidator('query', audioEffectQuerySchema), getAudioEffectHandler)
route.post('/', authMiddleware, zValidator('json', createAudioEffectSchema), createAudioEffectHandler)
route.get('/:id', getAudioEffectHandler)
route.put('/:id', authMiddleware, zValidator('json', updateAudioEffectSchema), updateAudioEffectHandler)
route.delete('/:id', authMiddleware, deleteAudioEffectHandler)

// 音频分类路由
route.get('/categories', zValidator('query', audioCategoryQuerySchema), getAudioCategoryHandler)
route.post('/categories', authMiddleware, zValidator('json', createAudioCategorySchema), createAudioCategoryHandler)
route.get('/categories/:id', getAudioCategoryHandler)
route.put('/categories/:id', authMiddleware, zValidator('json', updateAudioCategorySchema), updateAudioCategoryHandler)
route.delete('/categories/:id', authMiddleware, deleteAudioCategoryHandler)

// 其他功能路由
route.get('/search', zValidator('query', searchAudioSchema), searchAudioHandler)
route.get('/random', zValidator('query', randomAudioSchema), getRandomAudioHandler)
route.get('/tags', getAllAudioTagsHandler)
route.get('/stats', getAudioStatsHandler)
route.post('/batch', authMiddleware, zValidator('json', z.object({ audioEffects: z.array(createAudioEffectSchema) })), batchCreateAudioHandler)

export default route