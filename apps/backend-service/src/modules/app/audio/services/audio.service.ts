import { getAudioEffectsFromDB, getAudioEffectByIdFromDB, createAudioEffectFromDB, updateAudioEffectFromDB, deleteAudioEffectFromDB, getAudioCategoriesFromDB, getAudioCategoryByIdFromDB, createAudioCategoryFromDB, updateAudioCategoryFromDB, deleteAudioCategoryFromDB, searchAudioByTagsFromDB, getRandomAudioEffectsFromDB, getAllAudioTagsFromDB, getAudioStatisticsFromDB, batchCreateAudioEffectsFromDB } from '../repositories/audio.repository'

import type { Env } from '@/types/env'
import type { AudioEffectQuery, CreateAudioEffectRequest, UpdateAudioEffectRequest, AudioCategoryQuery, CreateAudioCategoryRequest, UpdateAudioCategoryRequest } from '@/types/audio'

// 音频效果服务
export const searchAudioByTags = async (env: Env, tags: string[], options: { limit?: number }) => {
  return searchAudioByTagsFromDB(env, tags, options)
}

export const getRandomAudioEffects = async (env: Env, options: { limit?: number }) => {
  return getRandomAudioEffectsFromDB(env, options)
}

export const getAllAudioTags = async (env: Env) => {
  return getAllAudioTagsFromDB(env)
}

export const getAudioStatistics = async (env: Env) => {
  return getAudioStatisticsFromDB(env)
}

export const batchCreateAudioEffects = async (env: Env, audioEffects: CreateAudioEffectRequest[]) => {
  return batchCreateAudioEffectsFromDB(env, audioEffects)
}

export async function getAudioEffects(env: Env, query: AudioEffectQuery) {
  return getAudioEffectsFromDB(env, query)
}

export async function getAudioEffectById(env: Env, id: string) {
  return getAudioEffectByIdFromDB(env, id)
}

export async function createAudioEffect(env: Env, data: CreateAudioEffectRequest) {
  return createAudioEffectFromDB(env, data)
}

export async function updateAudioEffect(env: Env, id: string, data: UpdateAudioEffectRequest) {
  return updateAudioEffectFromDB(env, id, data)
}

export async function deleteAudioEffect(env: Env, id: string) {
  return deleteAudioEffectFromDB(env, id)
}

// 音频分类服务
export async function getAudioCategories(env: Env, query: AudioCategoryQuery) {
  return getAudioCategoriesFromDB(env, query)
}

export async function getAudioCategoryById(env: Env, id: string) {
  return getAudioCategoryByIdFromDB(env, id)
}

export async function createAudioCategory(env: Env, data: CreateAudioCategoryRequest) {
  return createAudioCategoryFromDB(env, data)
}

export async function updateAudioCategory(env: Env, id: string, data: UpdateAudioCategoryRequest) {
  return updateAudioCategoryFromDB(env, id, data)
}

export async function deleteAudioCategory(env: Env, id: string) {
  return deleteAudioCategoryFromDB(env, id)
}