import { Hono } from 'hono'
import { createI18nValidator } from '@/middleware/i18n-validator'
import { z } from 'zod'
import {
  send<PERSON><PERSON><PERSON><PERSON><PERSON>,
  verify<PERSON>ode<PERSON><PERSON><PERSON>,
  register<PERSON><PERSON><PERSON>,
  login<PERSON><PERSON><PERSON>,
  send<PERSON><PERSON>in<PERSON><PERSON><PERSON>and<PERSON>,
  login<PERSON>ode<PERSON>and<PERSON>,
  logout<PERSON>andler,
  getProfileHandler,
  getSessionHandler,
  refreshTokenHandler,
  adminLoginHandler,
  adminGetProfileHandler,
  adminLogoutHandler
} from '../controllers/auth.controller'
import { authMiddleware, optionalAuthMiddleware } from '@/middleware/auth'
import type { SupportedLanguage } from '@/i18n/config'
import type { Env } from '@/types/env'
// 验证模式定义
const registerSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  name: z.string().optional(),
  inviteCode: z.string().optional()
})

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6)
})

const sendCodeSchema = z.object({
  email: z.string().email()
})

const verifyCodeSchema = z.object({
  email: z.string().email(),
  code: z.string().min(6),
  name: z.string().optional(),
  password: z.string().optional(),
  inviteCode: z.string().optional()
})

const loginCodeSchema = z.object({
  email: z.string().email(),
  code: z.string().min(6)
})

const refreshTokenSchema = z.object({
  refresh_token: z.string().min(1)
})


const route = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
  }
}>()

// 公共认证路由
route.post('/send-code', createI18nValidator(sendCodeSchema), sendCodeHandler)
route.post('/verify-code', createI18nValidator(verifyCodeSchema), verifyCodeHandler)
route.post('/register', createI18nValidator(registerSchema), registerHandler)
route.post('/login', createI18nValidator(loginSchema), loginHandler)
route.post('/send-login-code', createI18nValidator(sendCodeSchema), sendLoginCodeHandler)
route.post('/login-code', createI18nValidator(loginCodeSchema), loginCodeHandler)
route.post('/logout', optionalAuthMiddleware, logoutHandler)
route.get('/profile', authMiddleware, getProfileHandler)
route.get('/session', optionalAuthMiddleware, getSessionHandler)
route.post('/refresh', createI18nValidator(refreshTokenSchema), refreshTokenHandler)

// 管理员路由
route.post('/admin/login', createI18nValidator(loginSchema), adminLoginHandler)
route.get('/admin/profile', authMiddleware, adminGetProfileHandler)
route.post('/admin/logout', authMiddleware, adminLogoutHandler)

export default route