import { Context } from 'hono'
import type { Env } from '@/types/env'
import { sendCodeService, verifyCodeService, registerService, loginService, sendLoginCodeService, loginCodeService, logoutService, getProfileService, getSessionService, refreshTokenService, adminLoginService, adminGetProfileService, adminLogoutService } from '../services/auth.service'
import { throwBusinessError } from '@/middleware/global-error-handler'
import { createSuccessResponse } from '@/types/responses'
import { ErrorCode } from '@/types/errors'

// 发送验证码
export const sendCodeHandler = async (c: Context) => {
  const t = c.get('t')
  const data = await c.req.json()
  const result = await sendCodeService(c.env, data.email)
  if (!result.success) {
    throwBusinessError(ErrorCode.EMAIL_SEND_FAILED, { message: t('auth.send_code_failed') })
  }
  return c.json(createSuccessResponse({ message: t('auth.code_sent') }))
}

// 验证验证码并注册
export const verifyCodeHandler = async (c: Context) => {
  const t = c.get('t')
  const data = await c.req.json()
  const result = await verifyCodeService(c.env, data)
  return c.json(createSuccessResponse(result), 201)
}

// 用户注册
export const registerHandler = async (c: Context) => {
  const t = c.get('t')
  const data = await c.req.json()
  const result = await registerService(c.env, data)
  return c.json(createSuccessResponse(result), 201)
}

// 用户登录
export const loginHandler = async (c: Context) => {
  const t = c.get('t')
  const data = await c.req.json()
  const result = await loginService(c.env, data)
  return c.json(createSuccessResponse(result))
}

// 发送登录验证码
export const sendLoginCodeHandler = async (c: Context) => {
  const t = c.get('t')
  const data = await c.req.json()
  const result = await sendLoginCodeService(c.env, data.email)
  if (!result.success) {
    throwBusinessError(ErrorCode.EMAIL_SEND_FAILED, { message: t('auth.send_code_failed') })
  }
  return c.json(createSuccessResponse({ message: t('auth.code_sent') }))
}

// 验证码登录
export const loginCodeHandler = async (c: Context) => {
  const t = c.get('t')
  const data = await c.req.json()
  const result = await loginCodeService(c.env, data)
  return c.json(createSuccessResponse(result))
}

// 用户登出
export const logoutHandler = async (c: Context) => {
  const t = c.get('t')
  const token = c.req.header('Authorization')?.replace('Bearer ', '')
  await logoutService(c.env, token)
  return c.json(createSuccessResponse({ message: t('auth.logout_success') }))
}

// 获取用户信息
export const getProfileHandler = async (c: Context) => {
  const t = c.get('t')
  const user = c.get('user')
  if (!user) {
    throwBusinessError(ErrorCode.AUTH_UNAUTHORIZED, { message: t('auth.user_not_found') })
  }
  const profile = await getProfileService(c.env, user.id)
  return c.json(createSuccessResponse({ user: profile }))
}

// 获取会话信息
export const getSessionHandler = async (c: Context) => {
  const t = c.get('t')
  const user = c.get('user')
  if (!user) {
    return c.json(createSuccessResponse({ session: null }))
  }
  const session = await getSessionService(c.env, user.id)
  return c.json(createSuccessResponse(session))
}

// 刷新令牌
export const refreshTokenHandler = async (c: Context) => {
  const t = c.get('t')
  const data = await c.req.json()
  const result = await refreshTokenService(c.env, data.refresh_token)
  return c.json(createSuccessResponse(result))
}

// 管理员登录
export const adminLoginHandler = async (c: Context) => {
  const t = c.get('t')
  const data = await c.req.json()
  const result = await adminLoginService(c.env, data)
  return c.json(createSuccessResponse(result))
}

// 获取管理员信息
export const adminGetProfileHandler = async (c: Context) => {
  const t = c.get('t')
  const user = c.get('user')
  if (!user) {
    throwBusinessError(ErrorCode.AUTH_UNAUTHORIZED, { message: t('auth.user_not_found') })
  }
  const profile = await adminGetProfileService(c.env, user.id)
  return c.json(createSuccessResponse({ user: profile }))
}

// 管理员登出
export const adminLogoutHandler = async (c: Context) => {
  const t = c.get('t')
  const token = c.req.header('Authorization')?.replace('Bearer ', '')
  await adminLogoutService(c.env, token)
  return c.json(createSuccessResponse({ message: t('auth.admin_logout_success') }))
}