import type { Env } from '@/types/env'
import { createSupabaseServiceClient, signUpWithEmail, signInWithEmail, signOut } from '@/lib/supabase'
import { getUserBySupabaseId, createUserProfile, getUser, createUser } from '@/modules/app/user/repositories/user.repository'
import { initializeNewUserPointsAsync } from '@/modules/app/user/services/user.service'
import { generateVerificationCode, sendRegistrationCodeEmail, sendLoginCodeEmail, storeVerificationCode, verifyStoredCode } from '@/lib/email'
import { validateInviteCode, createReferralRelation, incrementInviteCodeUsage } from '@/lib/db/queries/referral'
// 网络错误检查函数
const isNetworkError = (error: any): boolean => {
  return (error.message && error.message.includes('Network Error')) ||
         (error.message && error.message.includes('网络错误')) ||
         error.cause === 'Network Error';
}

// 发送验证码服务
export async function sendCodeService(env: Env, email: string) {
  const existingUser = await getUser(env, email)
  if (existingUser) {
    return { success: false, error: 'EMAIL_ALREADY_REGISTERED' }
  }
  const verificationCode = generateVerificationCode()
  const emailResult = await sendRegistrationCodeEmail(env, email, verificationCode)
  if (!emailResult.success) return { success: false, error: emailResult.error }
  await storeVerificationCode(env, email, verificationCode, 10)
  return { success: true }
}

// 验证验证码并注册服务
export async function verifyCodeService(env: Env, data: any) {
  const { email, code, name, password, inviteCode } = data
  const codeVerification = await verifyStoredCode(env, email, code)
  if (!codeVerification.valid) return { success: false, error: codeVerification.error }

  const supabase = createSupabaseServiceClient(env)
  const userPassword = password || 'temp_' + Math.random().toString(36).substring(2, 15)
  let authData: any, isNewUser = true

  const { data: createData, error: createError } = await supabase.auth.admin.createUser({
    email, password: userPassword, email_confirm: true,
    user_metadata: { name: name || email.split('@')[0], hasPassword: !!password }
  })

  if (createError) {
    if (createError.message.includes('already registered')) {
      const { data: users } = await supabase.auth.admin.listUsers()
      const existingUser = users?.users?.find(u => u.email === email)
      if (!existingUser) return { success: false, error: 'USER_NOT_FOUND' }
      authData = { user: existingUser }
      isNewUser = false
    } else return { success: false, error: createError.message }
  } else authData = createData

  if (!authData.user) return { success: false, error: 'REGISTRATION_FAILED' }

  let dbUser = await getUserBySupabaseId(env, authData.user.id)
  if (!dbUser && isNewUser) {
    const [newDbUser] = await createUser(env, email, authData.user.id)
    dbUser = newDbUser
    if (dbUser) {
      await createUserProfile(env, { userId: dbUser.id, nickname: name || email.split('@')[0], gender: 'other' })
      initializeNewUserPointsAsync(env, dbUser.id)

      if (inviteCode?.trim()) {
        const validationResult = await validateInviteCode(env, inviteCode.trim())
        if (validationResult.valid && validationResult.inviteCode) {
          await createReferralRelation(env, validationResult.inviteCode.userId, dbUser.id, validationResult.inviteCode.id)
          await incrementInviteCodeUsage(env, validationResult.inviteCode.id)
        }
      }
    }
  }

  if (password) {
    const { data: passwordSignIn } = await supabase.auth.signInWithPassword({ email, password: userPassword })
    if (passwordSignIn.session) {
      return {
        message: isNewUser ? 'auth.registration_success' : 'auth.verification_success',
        session: {
          access_token: passwordSignIn.session.access_token,
          refresh_token: passwordSignIn.session.refresh_token,
          expires_at: passwordSignIn.session.expires_at,
          user: { id: authData.user.id, email: authData.user.email, emailConfirmed: true, dbUserId: dbUser?.id }
        }
      }
    }
  }

  return {
    message: isNewUser ? 'auth.registration_success' : 'auth.verification_success',
    requireLogin: !password, hasPassword: !!password
  }
}

// 用户注册服务
export async function registerService(env: Env, data: any) {
  const { email, password, name, inviteCode } = data
  const { data: authData, error } = await signUpWithEmail(env, email, password)
  if (error) return { success: false, error: error.message }
  if (!authData.user) return { success: false, error: 'REGISTRATION_FAILED' }

  const [dbUser] = await createUser(env, email, authData.user.id)
  if (dbUser) {
    await createUserProfile(env, { userId: dbUser.id, nickname: name || email.split('@')[0], gender: 'other' })
    initializeNewUserPointsAsync(env, dbUser.id)

    if (inviteCode?.trim()) {
      const validationResult = await validateInviteCode(env, inviteCode.trim())
      if (validationResult.valid && validationResult.inviteCode) {
        await createReferralRelation(env, validationResult.inviteCode.userId, dbUser.id, validationResult.inviteCode.id)
        await incrementInviteCodeUsage(env, validationResult.inviteCode.id)
      }
    }
  }

  return {
    message: 'auth.registration_success_check_email',
    user: { id: authData.user.id, email: authData.user.email, emailConfirmed: authData.user.email_confirmed_at !== null }
  }
}

// 用户登录服务
export async function loginService(env: Env, data: any) {
  const { email, password } = data
  const { data: authData, error } = await signInWithEmail(env, email, password)
  if (error || !authData.user || !authData.session) return { success: false, error: 'LOGIN_FAILED' }

  let dbUser = await getUserBySupabaseId(env, authData.user.id)
  if (!dbUser) {
    const [newDbUser] = await createUser(env, email, authData.user.id)
    dbUser = newDbUser
    if (dbUser) {
      await createUserProfile(env, { userId: dbUser.id, nickname: email.split('@')[0], gender: 'other' })
    }
  }

  return {
    message: 'user.login_success',
    session: {
      access_token: authData.session.access_token,
      refresh_token: authData.session.refresh_token,
      expires_at: authData.session.expires_at,
      user: { id: authData.user.id, email: authData.user.email, emailConfirmed: authData.user.email_confirmed_at !== null, dbUserId: dbUser?.id }
    }
  }
}

// 发送登录验证码服务
export async function sendLoginCodeService(env: Env, email: string) {
  const existingUser = await getUser(env, email)
  if (!existingUser) return { success: false, error: 'USER_NOT_FOUND' }

  const verificationCode = generateVerificationCode()
  const emailResult = await sendLoginCodeEmail(env, email, verificationCode)
  if (!emailResult.success) return { success: false, error: emailResult.error }

  await storeVerificationCode(env, email, verificationCode, 10)
  return { success: true }
}

// 验证码登录服务
export async function loginCodeService(env: Env, data: any) {
  const { email, code } = data
  const codeVerification = await verifyStoredCode(env, email, code)
  if (!codeVerification.valid) return { success: false, error: codeVerification.error }

  const existingUser = await getUser(env, email)
  if (!existingUser) return { success: false, error: 'USER_NOT_FOUND' }

  const supabase = createSupabaseServiceClient(env)
  const { data: users } = await supabase.auth.admin.listUsers()
  const authUser = users?.users?.find(u => u.id === existingUser.supabaseUserId)
  if (!authUser) return { success: false, error: 'USER_STATUS_ABNORMAL' }

  const tempPassword = 'TempPass_' + Math.random().toString(36).substring(2, 15) + Date.now()
  const { error: updateError } = await supabase.auth.admin.updateUserById(authUser.id, { password: tempPassword })
  if (updateError) return { success: false, error: updateError.message }

  const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({ email, password: tempPassword })
  if (loginError || !loginData.session) return { success: false, error: 'LOGIN_FAILED' }

  return {
    message: 'auth.login_success',
    session: {
      access_token: loginData.session.access_token,
      refresh_token: loginData.session.refresh_token,
      expires_at: loginData.session.expires_at,
      user: { id: authUser.id, email: authUser.email, emailConfirmed: authUser.email_confirmed_at !== null, dbUserId: existingUser.id }
    }
  }
}

// 登出服务
export async function logoutService(env: Env, token: string | undefined) {
  if (token) {
    try { await signOut(env, token) }
    catch (error) { console.error('Logout error:', error) }
  }
}

// 获取用户信息服务
export async function getProfileService(env: Env, userId: string) {
  const dbUser = await getUserBySupabaseId(env, userId)
  const supabase = createSupabaseServiceClient(env);
  const { data: { users } } = await supabase.auth.admin.listUsers();
  const supabaseUser = users?.find(u => u.id === userId);
  if (!supabaseUser) return { success: false, error: 'USER_NOT_FOUND' };
  return {
    id: userId,
    email: dbUser?.email,
    emailConfirmed: supabaseUser.email_confirmed_at !== null,
    dbUserId: dbUser?.id,
    createdAt: dbUser?.createdAt,
    lastSignInAt: supabaseUser.last_sign_in_at
  }
}

// 获取会话信息服务
export async function getSessionService(env: Env, userId: string) {
  const dbUser = await getUserBySupabaseId(env, userId)
  const supabase = createSupabaseServiceClient(env);
  const { data: { users } } = await supabase.auth.admin.listUsers();
  const supabaseUser = users?.find(u => u.id === userId);
  if (!supabaseUser) return { success: false, error: 'USER_NOT_FOUND' };
  const tokenExpiresAt = new Date(Date.now() + 50 * 60 * 1000)
  const sessionExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)

  return {
    user: {
      id: userId,
      email: dbUser?.email,
      emailConfirmed: supabaseUser.email_confirmed_at !== null,
      dbUserId: dbUser?.id
    },
    expires: sessionExpiresAt.toISOString(),
    tokenRefreshAt: tokenExpiresAt.toISOString()
  }
}

// 刷新令牌服务
export async function refreshTokenService(env: Env, refreshToken: string) {
  const supabase = createSupabaseServiceClient(env)
  const { data: refreshData, error } = await supabase.auth.refreshSession({ refresh_token: refreshToken })
  if (error || !refreshData.session || !refreshData.user) return { success: false, error: error?.message }

  let dbUser = await getUserBySupabaseId(env, refreshData.user.id)
  if (!dbUser) {
    const [newDbUser] = await createUser(env, refreshData.user.email || '', refreshData.user.id)
    dbUser = newDbUser
    if (dbUser) {
      await createUserProfile(env, { userId: dbUser.id, nickname: refreshData.user.email?.split('@')[0] || 'User', gender: 'other' })
    }
  }

  const nextRefreshAt = new Date(Date.now() + 50 * 60 * 1000)
  return {
    message: 'auth.token_refresh_success',
    session: {
      access_token: refreshData.session.access_token,
      refresh_token: refreshData.session.refresh_token,
      expires_at: refreshData.session.expires_at,
      user: { id: refreshData.user.id, email: refreshData.user.email, emailConfirmed: refreshData.user.email_confirmed_at !== null, dbUserId: dbUser?.id }
    },
    nextRefreshAt: nextRefreshAt.toISOString(),
    refreshInterval: 3000000
  }
}

// 管理员登录服务
export async function adminLoginService(env: Env, data: { email: string; password: string }) {
  const { email, password } = data
  const { data: authData, error } = await signInWithEmail(env, email, password)
  if (error || !authData.user || !authData.session) return { success: false, error: 'LOGIN_FAILED' }

  const userMetadata = authData.user.user_metadata || {}
  const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true
  const adminEmails = ['<EMAIL>']
  const isAdminByEmail = adminEmails.includes(authData.user.email || '')

  if (!isAdmin && !isAdminByEmail) return { success: false, error: 'ADMIN_PERMISSION_REQUIRED' }

  let dbUser = await getUserBySupabaseId(env, authData.user.id)
  if (!dbUser) {
    const [newDbUser] = await createUser(env, authData.user.email!, authData.user.id)
    dbUser = newDbUser
  }

  return {
    message: 'auth.admin_login_success',
    data: {
      user: { 
        id: authData.user.id, 
        email: authData.user.email, 
        isAdmin: true,
        dbUserId: dbUser?.id,
        createdAt: dbUser?.createdAt
      },
      token: authData.session.access_token
    }
  }
}

// 获取管理员信息服务
export async function adminGetProfileService(env: Env, userId: string) {
  const dbUser = await getUserBySupabaseId(env, userId)
  const supabase = createSupabaseServiceClient(env);
  const { data: { users } } = await supabase.auth.admin.listUsers();
  const supabaseUser = users?.find(u => u.id === userId);
  const adminEmails = ['<EMAIL>']
  const isAdmin = adminEmails.includes(dbUser?.email || '')
  if (!isAdmin || !supabaseUser || !dbUser) return { success: false, error: 'USER_NOT_FOUND' }

  return {
    id: userId,
    email: dbUser.email,
    isAdmin: true,
    createdAt: dbUser.createdAt,
    emailConfirmed: supabaseUser.email_confirmed_at !== null
  }
}

// 管理员登出服务
export async function adminLogoutService(env: Env, token: string | undefined) {
  if (token) {
    try { await signOut(env, token) }
    catch (error) { console.error('Admin logout error:', error) }
  }
}