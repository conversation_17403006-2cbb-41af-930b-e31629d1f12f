  import { z } from 'zod'
import { getUserBySupabaseId, getUserProfile, createUserProfile, repoUpdateUserProfile, repoGetUserActiveSubscription, repoGetUserSubscriptionHistory, repoGetUserPoints, repoGetUserPointsTransactions } from '../repositories/user.repository'
import type { Env } from '@/types/env'
import type { UserProfile } from '@/lib/db/schema'
import { getSystemConfig } from '@/lib/db/queries/system-config'
import { PointsService } from '@/lib/membership/points'

// 验证模式定义
const profileUpdateSchema = z.object({
  nickname: z.string().min(2).max(50).optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
  avatarUrl: z.union([z.string().url(), z.string().length(0)]).optional()
})

// 获取用户资料
export async function getProfile(env: Env, supabaseUserId: string): Promise<UserProfile | null> {
  const dbUser = await getUserBySupabaseId(env, supabaseUserId)
  if (!dbUser) return null

  return getUserProfile(env, dbUser.id)
}

// 更新用户资料
export async function updateProfile(env: Env, supabaseUserId: string, data: any): Promise<UserProfile[] | null> {
  // 数据验证
  const validatedData = profileUpdateSchema.safeParse(data)
  if (!validatedData.success) {
    throw new Error(validatedData.error.errors[0].message)
  }

  const dbUser = await getUserBySupabaseId(env, supabaseUserId)
  if (!dbUser) throw new Error('User not found')

  const userProfile = await getUserProfile(env, dbUser.id)
  if (!userProfile) {
    // 如果用户配置文件不存在，创建新的
    const defaultName = data.nickname || 'User'
    return createUserProfile(env, {
      userId: dbUser.id,
      nickname: defaultName,
      gender: data.gender || 'other',
      avatarUrl: data.avatarUrl
    })
  }

  // 更新用户配置文件
  return repoUpdateUserProfile(env, dbUser.id, validatedData.data)
}

// 获取用户活跃订阅
export async function getActiveSubscription(env: Env, supabaseUserId: string): Promise<any | null> {
  const dbUser = await getUserBySupabaseId(env, supabaseUserId)
  if (!dbUser) return null

  const subscription = await repoGetUserActiveSubscription(env, dbUser.id)
  if (!subscription) return null

  // 计算剩余天数
  const daysRemaining = Math.ceil(
    (new Date(subscription.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
  )

  return {
    ...subscription,
    daysRemaining: Math.max(0, daysRemaining),
    isActive: subscription.status === 'active' && daysRemaining > 0
  }
}

// 获取用户订阅历史
export async function getUserSubscriptionHistory(env: Env, supabaseUserId: string, limit = 20): Promise<any[] | null> {
  const dbUser = await getUserBySupabaseId(env, supabaseUserId)
  if (!dbUser) return []

  const history = await repoGetUserSubscriptionHistory(env, dbUser.id)
  if (!history) return []
  return history.slice(0, limit)
}

// 获取用户积分信息
export async function getUserPoints(env: Env, supabaseUserId: string): Promise<any | null> {
  const dbUser = await getUserBySupabaseId(env, supabaseUserId)
  if (!dbUser) return { totalPoints: 0, usedPoints: 0, availablePoints: 0, lastUpdated: new Date().toISOString() }

  return repoGetUserPoints(env, dbUser.id)
}

// 获取用户积分交易记录
export async function getUserPointsTransactions(env: Env, supabaseUserId: string, limit = 50): Promise<any[] | null> {
  const dbUser = await getUserBySupabaseId(env, supabaseUserId)
  if (!dbUser) return []

  return repoGetUserPointsTransactions(env, dbUser.id, limit)
}

// 获取用户完整状态信息
export async function getUserStatus(env: Env, supabaseUserId: string) {
  const dbUser = await getUserBySupabaseId(env, supabaseUserId)
  if (!dbUser) throw new Error('User not found')

  // 并行获取用户的各种信息
  const [userProfile, activeSubscription, userPointsData] = await Promise.all([
    getUserProfile(env, dbUser.id),
    repoGetUserActiveSubscription(env, dbUser.id),
    repoGetUserPoints(env, dbUser.id)
  ])

  // 计算会员状态
  const isMember = activeSubscription &&
    activeSubscription.status === 'active' &&
    new Date(activeSubscription.endDate) > new Date()

  const daysRemaining = activeSubscription
    ? Math.ceil(
        (new Date(activeSubscription.endDate).getTime() - new Date().getTime()) /
          (1000 * 60 * 60 * 24)
      )
    : 0

  return {
    // 用户基本信息
    user: {
      id: supabaseUserId,
      dbUserId: dbUser.id
    },
    // 用户配置文件
    profile: userProfile
      ? {
          nickname: userProfile?.nickname,
          gender: userProfile?.gender,
          avatarUrl: userProfile?.avatarUrl
        }
      : null,
    // 会员状态
    membership: {
      isMember,
      subscription: activeSubscription
        ? {
            id: activeSubscription.id,
            planId: activeSubscription.planId,
            startDate: activeSubscription.startDate,
            endDate: activeSubscription.endDate,
            status: activeSubscription.status,
            daysRemaining: Math.max(0, daysRemaining)
          }
        : null
    },
    // 积分信息
    points: userPointsData
  }
}


/**
 * 为新用户异步初始化积分
 * 使用Promise.resolve()确保不阻塞主流程
 */
export function initializeNewUserPointsAsync(env: Env, userId: string): void {
  // 使用Promise.resolve()在下一个事件循环中执行
  Promise.resolve()
    .then(async () => {
      try {
        console.log(`开始为新用户 ${userId} 初始化积分...`)

        // 1. 获取新用户初始积分配置
        const newUserPointsConfig = await getSystemConfig(env, 'NEW_USER_POINTS')
        const initialPoints = newUserPointsConfig ? Number.parseInt(newUserPointsConfig, 10) : 50 // 默认50积分

        if (initialPoints <= 0) {
          console.log(`新用户初始积分为 ${initialPoints}，跳过积分初始化`)
          return
        }

        // 2. 使用积分服务添加积分
        const pointsService = new PointsService(env)
        const result = await pointsService.addPoints(
          userId,
          initialPoints,
          'bonus', // 使用bonus作为来源
          undefined, // sourceId
          `新用户注册奖励${initialPoints}积分`
        )

        if (result.success) {
          console.log(`✅ 新用户 ${userId} 成功获得初始积分: ${initialPoints}`)
        } else {
          console.error(`❌ 新用户 ${userId} 积分初始化失败: ${result.error}`)
        }
      } catch (error) {
        console.error(`新用户 ${userId} 积分初始化过程中出错:`, error)
        // 不抛出错误，避免影响其他流程
      }
    })
    .catch(error => {
      // 捕获Promise中的任何错误
      console.error(`新用户 ${userId} 积分初始化Promise失败:`, error)
    })
}
