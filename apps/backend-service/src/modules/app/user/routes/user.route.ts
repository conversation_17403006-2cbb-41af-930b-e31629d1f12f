import { Hono } from 'hono'
import { getUserPro<PERSON>le<PERSON><PERSON><PERSON>, update<PERSON><PERSON><PERSON>le<PERSON><PERSON><PERSON>, getUserSubscriptionHandler, getUserSubscriptionHistoryHandler, getUserPoints<PERSON>and<PERSON>, getUserPointsTransactionsHand<PERSON>, getUserStatusHandler } from '../controllers/user.controller'
import { authMiddleware } from '@/middleware/auth'
import type { Env } from '@/types/env'
import type { SupportedLanguage } from '@/i18n/config'
const route = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
  }
}>()
// Apply auth middleware to all routes
route.use(authMiddleware)

// User profile routes
route.get('/profile', getUserProfileHandler)
route.put('/profile', updateProfileHandler)

// Subscription routes
route.get('/subscription', getUserSubscriptionHandler)
route.get('/subscription/history', getUserSubscriptionHistoryHandler)

// Points routes
route.get('/points', getUserPointsHandler)
route.get('/points/transactions', getUserPointsTransactionsHandler)

// User status route
route.get('/status', getUserStatusHandler)

export default route