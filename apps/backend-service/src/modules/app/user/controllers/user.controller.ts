import { Context } from 'hono'
import { getProfile, updateProfile, getActiveSubscription, getUserSubscriptionHistory, getUserPoints, getUserPointsTransactions, getUserStatus } from '../services/user.service'
import type { Env } from '@/types/env'

// 获取用户资料
export const getUserProfileHandler = async (c: Context) => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t')

    if (!user) {
      return c.json({ success: false, message: t('unauthorized') }, 401)
    }

    const userProfile = await getProfile(env, user.id)
    if (!userProfile) {
      return c.json({ success: false, message: t('user.not_found') }, 404)
    }

    return c.json({
      success: true,
      userProfile
    }, 200)
  } catch (error) {
    console.error('获取用户资料失败:', error)
    const t = c.get('t')
    return c.json({ success: false, message: t('internal_error') }, 500)
  }
}

// 更新用户资料
export const updateProfileHandler = async (c: Context) => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t')
    const data = await c.req.json()

    if (!user) {
      return c.json({ success: false, message: t('unauthorized') }, 401)
    }

    const updatedProfile = await updateProfile(env, user.id, data)
    return c.json({
      success: true,
      userProfile: updatedProfile
    }, 200)
  } catch (error) {
    console.error('更新用户资料失败:', error)
    const t = c.get('t')
    return c.json({ success: false, message: t('internal_error') }, 500)
  }
}

// 获取用户订阅信息
export const getUserSubscriptionHandler = async (c: Context) => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t')

    if (!user) {
      return c.json({ success: false, message: t('unauthorized') }, 401)
    }

    const subscription = await getActiveSubscription(env, user.id)
    return c.json({
      success: true,
      data: subscription
    }, 200)
  } catch (error) {
    console.error('获取用户订阅信息失败:', error)
    const t = c.get('t')
    return c.json({ success: false, message: t('internal_error') }, 500)
  }
}

// 获取用户订阅历史
export const getUserSubscriptionHistoryHandler = async (c: Context) => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t')
    const limit = Number.parseInt(c.req.query('limit') || '20')

    if (!user) {
      return c.json({ success: false, message: t('unauthorized') }, 401)
    }

    const history = await getUserSubscriptionHistory(env, user.id, limit)
    return c.json({
      success: true,
      data: history
    }, 200)
  } catch (error) {
    console.error('获取用户订阅历史失败:', error)
    const t = c.get('t')
    return c.json({ success: false, message: t('internal_error') }, 500)
  }
}

// 获取用户积分信息
export const getUserPointsHandler = async (c: Context) => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t')

    if (!user) {
      return c.json({ success: false, message: t('unauthorized') }, 401)
    }

    const pointsData = await getUserPoints(env, user.id)
    return c.json({
      success: true,
      data: pointsData
    }, 200)
  } catch (error) {
    console.error('获取用户积分信息失败:', error)
    const t = c.get('t')
    return c.json({ success: false, message: t('internal_error') }, 500)
  }
}

// 获取用户积分交易记录
export const getUserPointsTransactionsHandler = async (c: Context) => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t')
    const limit = Number.parseInt(c.req.query('limit') || '50')

    if (!user) {
      return c.json({ success: false, message: t('unauthorized') }, 401)
    }

    const transactions = await getUserPointsTransactions(env, user.id, limit)
    return c.json({
      success: true,
      data: transactions
    }, 200)
  } catch (error) {
    console.error('获取用户积分交易记录失败:', error)
    const t = c.get('t')
    return c.json({ success: false, message: t('user.points_transactions_get_failed') }, 500)
  }
}

// 获取用户完整状态信息
export const getUserStatusHandler = async (c: Context) => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t')

    if (!user) {
      return c.json({ success: false, message: t('unauthorized') }, 401)
    }

    const status = await getUserStatus(env, user.id)
    return c.json({
      success: true,
      data: status
    }, 200)
  } catch (error) {
    console.error('获取用户状态信息失败:', error)
    const t = c.get('t')
    return c.json({ success: false, message: t('user.status_get_failed') }, 500)
  }
}