import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '@/lib/db/supabase-types'
import type { Env } from '@/types/env'
import type { User, UserProfile, UserSubscription, pointsTransaction } from '@/lib/db/schema';

// ==================== 用户基础操作 ====================

export async function createUser(env: Env, email: string, supabaseUserId: string): Promise<[User, boolean]> {
  const supabase = getSupabase(env);

  if (!email || !supabaseUserId) {
    console.error('createUser: 缺少必要参数');
    throw new Error('创建用户失败：缺少必要参数');
  }

  const { data: existingUser, error: fetchError } = await supabase
    .from(TABLE_NAMES.user)
    .select('*')
    .eq('supabase_user_id', supabaseUserId)
    .single();

  if (fetchError && fetchError.code !== 'PGRST116') {
    console.error('createUser: 查询用户失败', fetchError);
    throw new Error('创建用户失败：数据库查询错误');
  }

  if (existingUser) {
    return [existingUser, false];
  }

  const newUser = {
    email,
    supabase_user_id: supabaseUserId,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const { data: insertedUser, error: insertError } = await supabase
    .from(TABLE_NAMES.user)
    .insert([newUser])
    .select()
    .single();

  if (insertError) {
    console.error('createUser: 创建用户失败', insertError);
    throw new Error('创建用户失败：数据库插入错误');
  }

  return [insertedUser, true];
}

export async function getUser(env: Env, email: string): Promise<User | null> {
  const supabase = getSupabase(env);

  if (!email) {
    console.error('getUser: 缺少邮箱参数');
    return null;
  }

  const { data, error } = await supabase
    .from(TABLE_NAMES.user)
    .select('*')
    .eq('email', email)
    .single();

  if (error) {
    if (error.code !== 'PGRST116') {
      console.error('getUser: 查询用户失败', error);
    }
    return null;
  }

  return data;
}

export async function getUserBySupabaseId(env: Env, supabaseUserId: string): Promise<User | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.user)
      .select('*')
      .eq('supabase_user_id', supabaseUserId)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error

    return data && data.length > 0 ? data[0] : null
  } catch (error) {
    console.error('Failed to get user by supabase id from database', error)
    return null
  }
}

export async function getUserById(env: Env, id: string): Promise<User | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase.from(TABLE_NAMES.user).select('*').eq('id', id).single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) throw error
    return data
  } catch (error) {
    console.error('Failed to get user by id from database', error)
    throw error
  }
}

// ==================== 用户配置文件操作 ====================

export async function getUserProfile(env: Env, userId: string): Promise<UserProfile | null> {
  try {
    if (!userId || typeof userId !== 'string') {
      console.log('getUserProfile: Invalid user ID')
      return null
    }

    const supabase = getSupabase(env)
    const result = await supabase.from(TABLE_NAMES.userProfile).select('*').eq('user_id', userId)

    const { data, error } = handleSupabaseResult(result)
    if (error) {
      console.log(`Error querying user ${userId} profile:`, error)
      return null
    }

    return data && data.length > 0 ? data[0] : null
  } catch (error) {
    console.log(`Exception getting user ${userId} profile:`, (error as any)?.message || error)
    return null
  }
}

export async function createUserProfile(
  env: Env,
  data: {
    userId: string
    nickname?: string
    gender?: 'male' | 'female' | 'other'
    avatarUrl?: string
  }
): Promise<UserProfile[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.userProfile)
      .insert({
        user_id: data.userId,
        nickname: data.nickname,
        gender: data.gender,
        avatar_url: data.avatarUrl
      })
      .select()

    const { data: insertedData, error } = handleSupabaseResult(result)
    if (error) throw error
    return insertedData || []
  } catch (error) {
    console.error('Failed to create user profile', error)
    throw error
  }
}

export async function repoUpdateUserProfile(
  env: Env,
  userId: string,
  data: {
    nickname?: string
    gender?: 'male' | 'female' | 'other'
    avatarUrl?: string
  }
): Promise<UserProfile[]> {
  try {
    const supabase = getSupabase(env)

    const updateData: any = {};
    if (data.nickname !== undefined) updateData.nickname = data.nickname;
    if (data.gender !== undefined) updateData.gender = data.gender;
    if (data.avatarUrl !== undefined) updateData.avatar_url = data.avatarUrl;
    updateData.updated_at = new Date().toISOString();

    const result = await supabase
      .from(TABLE_NAMES.userProfile)
      .update(updateData)
      .eq('user_id', userId)
      .select()

    const { data: updatedData, error } = handleSupabaseResult(result)
    if (error) throw error
    return updatedData || []
  } catch (error) {
    console.error('Failed to update user profile', error)
    throw error
  }
}

// ==================== 用户订阅相关操作 ====================

export async function repoGetUserActiveSubscription(env: Env, userId: string): Promise<any | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.userSubscription)
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .order('end_date', { ascending: false })
      .single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) return null
    return data
  } catch (error) {
    console.error('Failed to get active subscription', error)
    return null
  }
}

export async function repoGetUserSubscriptionHistory(env: Env, userId: string): Promise<any[] | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.userSubscription)
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Failed to get subscription history', error)
    return []
  }
}

// ==================== 用户积分相关操作 ====================

export async function repoGetUserPoints(env: Env, userId: string): Promise<any | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.userPoints)
      .select('*')
      .eq('user_id', userId)
      .single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) {
      // 如果用户积分记录不存在，创建初始记录
      if (error.code === 'PGRST116') {
        const initialData = await supabase
          .from(TABLE_NAMES.userPoints)
          .insert({ user_id: userId, total_points: 0, used_points: 0, available_points: 0 })
          .select()
          .single()

        return initialData.data
      }
      return null
    }
    return data
  } catch (error) {
    console.error('Failed to get user points', error)
    return { totalPoints: 0, usedPoints: 0, availablePoints: 0, lastUpdated: new Date().toISOString() }
  }
}

export async function repoGetUserPointsTransactions(env: Env, userId: string, limit = 50): Promise<any[] | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.pointsTransaction)
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Failed to get points transactions', error)
    return []
  }
}