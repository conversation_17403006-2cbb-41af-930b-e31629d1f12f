import { Context } from 'hono'
import { 
  getDeviceByCode, 
  getSystemDevices,
  getUserDevices,
  createDevice,
  updateDevice,
  deleteDevice,
  createDeviceConnection,
  createDeviceUsage
} from '../services/device.service'
import type { Env } from '@/types/env'

export const getDeviceByCodeHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { deviceCode } = c.req.valid('json')
    const t = c.get('t')

    // 验证设备码
    if (!deviceCode || deviceCode.trim().length === 0) {
      return c.json({ error: t('device_code_required') }, 400)
    }
    if (deviceCode.length > 20) {
      return c.json({ error: t('device_code_too_long') }, 400)
    }

    // 查找设备
    const deviceInfo = await getDeviceByCode(c.env, deviceCode)

    if (!deviceInfo) {
      return c.json({ error: t('device_code_invalid') }, 404)
    }

    // 转换为前端需要的格式
    const frontendDevice = {
      pic: deviceInfo.pic || '',
      name: deviceInfo.name,
      func: deviceInfo.functions.map((func: any) => ({
        name: func.name,
        key: func.key,
        commands: func.commands.map((cmd: any) => ({
          intensity: cmd.intensity,
          command: cmd.command,
        })),
      })),
    }

    return c.json({
      success: true,
      device: frontendDevice,
      message: t('device_info_success'),
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取设备信息失败:', error)
    return c.json({ error: t('device_info_failed') }, 500)
  }
}

export const getSystemDevicesHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const t = c.get('t')
    const devices = await getSystemDevices(c.env)

    // 转换为前端格式
    const supportedDevices = devices.map((device: any) => ({
      deviceCode: device.deviceCode,
      name: device.name,
      pic: device.pic || '',
      brand: device.brand,
      model: device.model,
      category: device.category,
      description: device.description,
      func: device.functions.map((func: any) => ({
        name: func.name,
        key: func.key,
        commands: func.commands.map((cmd: any) => ({
          intensity: cmd.intensity,
          command: cmd.command,
        })),
      })),
    }))

    return c.json({
      success: true,
      devices: supportedDevices,
      message: t('supported_devices_success'),
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取支持设备列表失败:', error)
    return c.json({ error: t('supported_devices_failed') }, 500)
  }
}

export const getUserDevicesHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const t = c.get('t')
    const userId = c.req.param('userId')
    
    if (!userId) {
      return c.json({ error: t('user_id_required') }, 400)
    }

    const devices = await getUserDevices(c.env, userId)

    return c.json({
      success: true,
      devices: devices,
      message: t('user_devices_success'),
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取用户设备列表失败:', error)
    return c.json({ error: t('user_devices_failed') }, 500)
  }
}

export const createDeviceHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const t = c.get('t')
    const deviceData = await c.req.json()

    if (!deviceData.userId || !deviceData.deviceCode || !deviceData.name) {
      return c.json({ error: t('device_data_incomplete') }, 400)
    }

    const device = await createDevice(c.env, deviceData)

    if (!device) {
      return c.json({ error: t('device_creation_failed') }, 500)
    }

    return c.json({
      success: true,
      device: device,
      message: t('device_creation_success'),
    })
  } catch (error) {
    const t = c.get('t')
    console.error('创建设备失败:', error)
    return c.json({ error: t('device_creation_failed') }, 500)
  }
}

export const updateDeviceHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const t = c.get('t')
    const deviceId = c.req.param('id')
    const updates = await c.req.json()

    if (!deviceId) {
      return c.json({ error: t('device_id_required') }, 400)
    }

    const device = await updateDevice(c.env, deviceId, updates)

    if (!device) {
      return c.json({ error: t('device_update_failed') }, 500)
    }

    return c.json({
      success: true,
      device: device,
      message: t('device_update_success'),
    })
  } catch (error) {
    const t = c.get('t')
    console.error('更新设备失败:', error)
    return c.json({ error: t('device_update_failed') }, 500)
  }
}

export const deleteDeviceHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const t = c.get('t')
    const deviceId = c.req.param('id')
    const userId = c.req.param('userId')

    if (!deviceId || !userId) {
      return c.json({ error: t('device_id_and_user_id_required') }, 400)
    }

    const device = await deleteDevice(c.env, deviceId, userId)

    if (!device) {
      return c.json({ error: t('device_deletion_failed') }, 500)
    }

    return c.json({
      success: true,
      message: t('device_deletion_success'),
    })
  } catch (error) {
    const t = c.get('t')
    console.error('删除设备失败:', error)
    return c.json({ error: t('device_deletion_failed') }, 500)
  }
}

export const createDeviceConnectionHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const t = c.get('t')
    const connectionData = await c.req.json()

    if (!connectionData.userId || !connectionData.deviceId) {
      return c.json({ error: t('connection_data_incomplete') }, 400)
    }

    const connection = await createDeviceConnection(c.env, connectionData)

    if (!connection) {
      return c.json({ error: t('connection_creation_failed') }, 500)
    }

    return c.json({
      success: true,
      connection: connection,
      message: t('connection_creation_success'),
    })
  } catch (error) {
    const t = c.get('t')
    console.error('创建设备连接失败:', error)
    return c.json({ error: t('connection_creation_failed') }, 500)
  }
}

export const createDeviceUsageHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const t = c.get('t')
    const usageData = await c.req.json()

    if (!usageData.userId || !usageData.deviceId || !usageData.functionKey) {
      return c.json({ error: t('usage_data_incomplete') }, 400)
    }

    const usage = await createDeviceUsage(c.env, usageData)

    if (!usage) {
      return c.json({ error: t('usage_creation_failed') }, 500)
    }

    return c.json({
      success: true,
      usage: usage,
      message: t('usage_creation_success'),
    })
  } catch (error) {
    const t = c.get('t')
    console.error('创建设备使用记录失败:', error)
    return c.json({ error: t('usage_creation_failed') }, 500)
  }
} 