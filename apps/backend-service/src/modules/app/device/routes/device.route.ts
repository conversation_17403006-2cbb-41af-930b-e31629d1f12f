import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { languageMiddleware } from '@/middleware/language'
import { 
  getDeviceByCodeHandler, 
  getSystemDevicesHandler,
  getUserDevicesHandler,
  createDeviceHandler,
  updateDeviceHandler,
  deleteDeviceHandler,
  createDeviceConnectionHandler,
  createDeviceUsageHandler
} from '../controllers/device.controller'

const route = new Hono()

// 根据设备码获取设备信息（用于前端连接设备）
const getDeviceSchema = z.object({
  deviceCode: z.string().min(1).max(20),
})

// 设备信息查询
route.post('/connect', languageMiddleware, zValidator('json', getDeviceSchema), getDeviceByCodeHandler)
route.get('/supported', languageMiddleware, getSystemDevicesHandler)

// 用户设备管理
route.get('/user/:userId', languageMiddleware, getUserDevicesHandler)
route.post('/', languageMiddleware, createDeviceHandler)
route.put('/:id', languageMiddleware, updateDeviceHandler)
route.delete('/:id/:userId', languageMiddleware, deleteDeviceHandler)

// 设备连接和使用记录
route.post('/connection', languageMiddleware, createDeviceConnectionHandler)
route.post('/usage', languageMiddleware, createDeviceUsageHandler)

export default route 