# 设备模块 (Device Module)

## 概述

设备模块按照模板结构进行了重构，采用了分层架构设计，包含以下层级：

```
src/modules/app/device/
├── index.ts                    # 模块入口
├── routes/
│   └── device.route.ts         # 路由定义
├── controllers/
│   └── device.controller.ts    # 控制器层
├── services/
│   └── device.service.ts       # 服务层
├── repositories/
│   └── device.repository.ts    # 数据访问层
└── README.md                   # 模块说明
```

## 功能特性

### 1. 设备信息查询
- `POST /devices/connect` - 根据设备码获取设备信息
- `GET /devices/supported` - 获取系统支持的设备列表

### 2. 用户设备管理
- `GET /devices/user/:userId` - 获取用户的设备列表
- `POST /devices/` - 创建新设备
- `PUT /devices/:id` - 更新设备信息
- `DELETE /devices/:id/:userId` - 删除设备

### 3. 设备连接和使用记录
- `POST /devices/connection` - 记录设备连接
- `POST /devices/usage` - 记录设备使用

### 4. 设备功能管理
- 设备功能支持配置
- 功能状态管理
- 功能指令映射

### 5. 设备使用统计和历史
- 用户设备使用统计
- 设备使用历史查询
- 设备连接记录查询

## 架构设计

### 控制器层 (Controllers)
- 处理HTTP请求和响应
- 参数验证和错误处理
- 调用服务层方法

### 服务层 (Services)
- 业务逻辑处理
- 数据转换和格式化
- 调用数据访问层

### 数据访问层 (Repositories)
- 数据库操作
- SQL查询和事务处理
- 数据模型映射

## 主要功能

### 设备管理
- 设备信息查询和验证
- 设备创建、更新、删除
- 设备功能关联管理

### 设备功能管理
- 设备功能支持配置
- 功能状态管理
- 功能指令映射

### 设备连接管理
- 连接状态记录
- 连接历史查询
- 连接状态更新

### 设备使用记录
- 使用记录创建
- 使用统计查询
- 使用历史管理

## 数据模型

### Device (设备)
- 基本信息：名称、品牌、型号、分类
- 连接信息：最后连接时间、状态
- 功能关联：支持的功能列表

### DeviceFunction (设备功能)
- 功能定义：名称、键值、描述
- 强度配置：最大强度值
- 指令映射：功能对应的指令集

### DeviceConnection (设备连接)
- 连接记录：用户、设备、会话
- 状态管理：连接、断开、错误
- 元数据：连接相关信息

### DeviceUsage (设备使用)
- 使用记录：功能、强度、时长
- 会话关联：脚本、会话ID
- 统计信息：使用频率、偏好

## 迁移说明

### 重构完成的功能

原设备相关功能已从以下位置迁移：
- `src/routes/devices.ts` → `src/modules/app/device/routes/device.route.ts`
- `src/lib/db/queries/devices.ts` → `src/modules/app/device/repositories/device.repository.ts`

### 已迁移的功能列表

#### 设备管理功能
- ✅ `getDeviceByCode` - 根据设备码获取设备信息
- ✅ `getSystemDevices` - 获取系统预设设备列表
- ✅ `getUserDevices` - 获取用户的设备列表
- ✅ `createDevice` - 创建新设备
- ✅ `updateDevice` - 更新设备信息
- ✅ `deleteDevice` - 删除设备

#### 设备功能管理
- ✅ `createDeviceFunction` - 为设备添加功能支持
- ✅ `getDeviceFunctions` - 获取设备功能列表
- ✅ `updateDeviceFunction` - 更新设备功能支持状态

#### 设备指令管理
- ✅ `createDeviceCommand` - 为功能添加指令映射
- ✅ `getFunctionCommands` - 获取功能的指令列表
- ✅ `createDeviceCommands` - 批量创建设备指令映射

#### 设备连接管理
- ✅ `createDeviceConnection` - 记录设备连接
- ✅ `updateDeviceConnection` - 更新连接状态
- ✅ `getUserDeviceConnections` - 获取用户的设备连接记录

#### 设备使用记录
- ✅ `createDeviceUsage` - 记录设备使用
- ✅ `endDeviceUsage` - 结束设备使用记录
- ✅ `getUserDeviceUsageStats` - 获取用户设备使用统计
- ✅ `getDeviceUsageHistory` - 获取设备使用历史

### 向后兼容性

所有功能保持向后兼容，API接口路径和参数格式保持不变。

## 使用示例

### 获取设备信息
```typescript
// 控制器
const deviceInfo = await getDeviceByCode(c.env, deviceCode)

// 服务层
const device = await getDeviceByCodeFromDB(env, deviceCode)

// 数据访问层
const result = await supabase
  .from(TABLE_NAMES.device)
  .select('*')
  .eq('device_code', deviceCode)
  .single()
```

### 创建设备
```typescript
// 控制器
const device = await createDevice(c.env, deviceData)

// 服务层
const newDevice = await createDeviceFromDB(env, deviceData)

// 数据访问层
const result = await supabase
  .from(TABLE_NAMES.device)
  .insert(deviceData)
  .select()
  .single()
```

### 获取使用统计
```typescript
// 服务层
const stats = await getUserDeviceUsageStats(env, userId, deviceId)

// 数据访问层
const stats = await getUserDeviceUsageStatsFromDB(env, userId, deviceId)
```

## 注意事项

1. 所有数据库操作都通过数据访问层进行
2. 业务逻辑集中在服务层处理
3. 控制器层只负责HTTP请求处理
4. 错误处理和日志记录在各层都有相应实现
5. 类型安全通过TypeScript确保
6. 自动功能关联修复机制已保留
7. 完整的设备使用统计和历史查询功能已迁移

## 重构状态

✅ **重构完成** - 所有设备相关功能已成功迁移到新的模块结构中
✅ **功能完整** - 包含设备管理、功能管理、连接管理、使用记录等所有功能
✅ **向后兼容** - API接口保持兼容，无需修改现有前端代码
✅ **架构清晰** - 采用分层架构，职责分离明确
✅ **类型安全** - 完整的TypeScript类型定义 