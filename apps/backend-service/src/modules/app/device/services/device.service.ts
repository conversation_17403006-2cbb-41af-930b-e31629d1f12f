import { 
  getDeviceByCodeFromDB, 
  getSystemDevicesFromDB,
  getUserDevicesFromDB,
  createDeviceFromDB,
  updateDeviceFromDB,
  deleteDeviceFromDB,
  createDeviceConnectionFromDB,
  createDeviceUsageFromDB,
  createDeviceFunctionFromDB,
  getDeviceFunctionsFromDB,
  updateDeviceFunctionFromDB,
  createDeviceCommandFromDB,
  getFunctionCommandsFromDB,
  createDeviceCommandsFromDB,
  updateDeviceConnectionFromDB,
  endDeviceUsageFromDB,
  getUserDeviceConnectionsFromDB,
  getUserDeviceUsageStatsFromDB,
  getDeviceUsageHistoryFromDB
} from '../repositories/device.repository'
import type { Env } from '@/types/env'
import type { Device, DeviceFunctionCommand, DeviceConnection, DeviceUsage } from '@/lib/db/schema'

export async function getDeviceByCode(env: Env, deviceCode: string) {
  return getDeviceByCodeFromDB(env, deviceCode)
}

export async function getSystemDevices(env: Env) {
  return getSystemDevicesFromDB(env)
}

export async function getUserDevices(env: Env, userId: string): Promise<Device[]> {
  return getUserDevicesFromDB(env, userId)
}

export async function createDevice(
  env: Env,
  deviceData: {
    userId: string
    deviceCode: string
    name: string
    pic?: string
    brand?: string
    model?: string
    category?: string
    description?: string
  }
): Promise<Device | null> {
  return createDeviceFromDB(env, deviceData)
}

export async function updateDevice(
  env: Env,
  deviceId: string,
  updates: Partial<Device>
): Promise<Device | null> {
  return updateDeviceFromDB(env, deviceId, updates)
}

export async function deleteDevice(
  env: Env,
  deviceId: string,
  userId: string
): Promise<Device | null> {
  return deleteDeviceFromDB(env, deviceId, userId)
}

export async function createDeviceConnection(
  env: Env,
  connectionData: {
    userId: string
    deviceId: string
    sessionId?: string
    metadata?: any
  }
) {
  return createDeviceConnectionFromDB(env, connectionData)
}

export async function updateDeviceConnection(
  env: Env,
  connectionId: string,
  updates: {
    status?: 'connected' | 'disconnected' | 'error'
    disconnectedAt?: Date
    errorMessage?: string
  }
): Promise<DeviceConnection | null> {
  return updateDeviceConnectionFromDB(env, connectionId, updates)
}

export async function getUserDeviceConnections(env: Env, userId: string, limit = 50) {
  return getUserDeviceConnectionsFromDB(env, userId, limit)
}

export async function createDeviceUsage(
  env: Env,
  usageData: {
    userId: string
    deviceId: string
    scriptId?: string
    sessionId?: string
    functionKey: string
    intensity: number
    metadata?: any
  }
) {
  return createDeviceUsageFromDB(env, usageData)
}

export async function endDeviceUsage(
  env: Env,
  usageId: string,
  duration?: number
): Promise<DeviceUsage | null> {
  return endDeviceUsageFromDB(env, usageId, duration)
}

export async function getUserDeviceUsageStats(env: Env, userId: string, deviceId?: string) {
  return getUserDeviceUsageStatsFromDB(env, userId, deviceId)
}

export async function getDeviceUsageHistory(
  env: Env,
  userId: string,
  deviceId?: string,
  limit = 100
) {
  return getDeviceUsageHistoryFromDB(env, userId, deviceId, limit)
}

// ==================== 设备功能管理 ====================

export async function createDeviceFunction(
  env: Env,
  functionData: {
    deviceId: string
    functionId: string
    isActive?: boolean
  }
): Promise<any | null> {
  return createDeviceFunctionFromDB(env, functionData)
}

export async function getDeviceFunctions(env: Env, deviceId: string): Promise<any[]> {
  return getDeviceFunctionsFromDB(env, deviceId)
}

export async function updateDeviceFunction(
  env: Env,
  deviceId: string,
  functionId: string,
  updates: { isActive?: boolean }
): Promise<any | null> {
  return updateDeviceFunctionFromDB(env, deviceId, functionId, updates)
}

// ==================== 设备指令管理 ====================

export async function createDeviceCommand(
  env: Env,
  commandData: {
    functionId: string
    commandSetId: string
    intensity: number
    description?: string
  }
): Promise<DeviceFunctionCommand | null> {
  return createDeviceCommandFromDB(env, commandData)
}

export async function getFunctionCommands(env: Env, functionId: string): Promise<any[]> {
  return getFunctionCommandsFromDB(env, functionId)
}

export async function createDeviceCommands(
  env: Env,
  commands: Array<{
    functionId: string
    commandSetId: string
    intensity: number
    description?: string
  }>
): Promise<DeviceFunctionCommand[]> {
  return createDeviceCommandsFromDB(env, commands)
} 