import { Hono } from 'hono'
import type { Env } from '@/types/env'
import authRoutes from '@/modules/app/auth/routes/auth.route'
import userRoutes from '@/modules/app/user/routes/user.route'
import chatRoutes from '@/modules/app/chat/routes/chat.route'
import chatv2Routes from '@/routes/chatv2'
import dialogueRoutes from '@/routes/dialogue'
import imageGenerationRoutes from '@/routes/image-generation'
import audioRoutes from '@/modules/app/audio/routes/audio.route'
import historyRoutes from '@/routes/history'
import uploadRoutes from '@/routes/upload'
import membershipRoutes from './membership/routes/membership.route'
import paymentRoutes from '@/modules/app/payment/routes/payment.route'
import templateRoutes from '@/routes/templates'
import photoAlbumGenerationRoutes from '@/routes/photo-album-generation'
import ttsRoutes from '@/routes/tts'
import tts2Routes from '@/routes/tts2'
import tts3Routes from '@/routes/tts3'
import scriptRoutes from './script/routes/script.route'
import deviceRoutes from '@/modules/app/device/routes/device.route'
import voicesRoutes from '@/modules/app/voice/routes/voice.route'
import speechToTextRoutes from '@/routes/speech-to-text'
import pointsCycleRoutes from '@/routes/points-cycle'
import pointsRoutes from '@/routes/points'
import backgroundRoutes from '@/routes/background'
import imageGenerationV2Routes from '@/routes/image-generation-v2'
import imageGenerationRunpodRoutes from '@/routes/image-generation-runpod'
import multimodalVideoGenerationRoutes from '@/routes/multimodal-video-generation'
import referralRoutes from '@/routes/referral'
import activationCodeRoutes from '@/routes/activation-codes'
import appUpdateRoutes from '@/routes/app-update'
import sseRoutes from '@/routes/sse'
import { charactersRouter } from './characters/routes'
import route from './membership/routes/membership.route'

const app = new Hono<{ Bindings: Env }>()

app.route('/auth', authRoutes)
app.route('/users', userRoutes)
app.route('/conversations', chatRoutes)
app.route('/chatv2', chatv2Routes)
app.route('/dialogue', dialogueRoutes)
app.route('/image-generation', imageGenerationRoutes)
app.route('/audio', audioRoutes)
app.route('/history', historyRoutes)
app.route('/upload', uploadRoutes)
app.route('/membership', membershipRoutes)
app.route('/payment', paymentRoutes)
app.route('/templates', templateRoutes)
app.route('/photo-album-generation', photoAlbumGenerationRoutes)
app.route('/tts', ttsRoutes)
app.route('/tts2', tts2Routes)
app.route('/tts3', tts3Routes)
app.route('/scripts', scriptRoutes)
app.route('/devices', deviceRoutes)
app.route('/voices', voicesRoutes)
app.route('/speech-to-text', speechToTextRoutes)
app.route('/points-cycle', pointsCycleRoutes)
app.route('/points', pointsRoutes)
app.route('/background', backgroundRoutes)
app.route('/image-generation-v2', imageGenerationV2Routes)
app.route('/image-generation-runpod', imageGenerationRunpodRoutes)
app.route('/multimodal-video-generation', multimodalVideoGenerationRoutes)
app.route('/referral', referralRoutes)
app.route('/activation-codes', activationCodeRoutes)
app.route('/app-update', appUpdateRoutes)
app.route('/sse', sseRoutes)
app.route('/auth', authRoutes)
// 保持兼容性的图片生成路由
app.route('/characters', charactersRouter)

export default app