import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '@/lib/db/supabase-types'
import type { MembershipPlan, UserSubscription, UserPoints, PointsTransaction } from '@/lib/db/schema'
import type { Env } from '@/types/env'

// ==================== 用户ID解析操作 ====================

export async function getLocalUserIdFromSupabaseId(
  env: Env,
  supabaseUserId: string
): Promise<string | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.user)
      .select('id')
      .eq('supabase_user_id', supabaseUserId)
      .single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) throw error
    return data?.id || null
  } catch (error) {
    console.error('解析用户ID失败', error)
    throw error
  }
}

export async function ensureUserExists(
  env: Env,
  supabaseUserId: string,
  email: string
): Promise<string> {
  try {
    console.log(`检查用户是否存在: supabaseUserId=${supabaseUserId}, email=${email}`)

    // 首先尝试查找现有用户
    const existingUser = await getLocalUserIdFromSupabaseId(env, supabaseUserId)

    if (existingUser) {
      return existingUser
    }

    console.log(`用户不存在，创建新用户`)

    // 如果用户不存在，创建新用户
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.user)
      .insert({
        email,
        supabase_user_id: supabaseUserId
      })
      .select('id')
      .single()

    const { data: newUser, error } = handleSupabaseSingleResult(result)
    if (error) throw error

    console.log(`新用户创建成功，本地ID: ${newUser.id}`)
    return newUser.id
  } catch (error) {
    console.error('确保用户存在失败', error)
    throw error
  }
}

// ==================== 会员计划操作 ====================

export async function getMembershipPlans(env: Env): Promise<MembershipPlan[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.membershipPlan)
      .select('*')
      .eq('is_active', true)
      .order('sort_order')

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('获取会员计划失败', error)
    throw error
  }
}

export async function getMembershipPlanById(env: Env, id: string): Promise<MembershipPlan | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase.from(TABLE_NAMES.membershipPlan).select('*').eq('id', id).single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) throw error
    return data
  } catch (error) {
    console.error('获取会员计划详情失败', error)
    throw error
  }
}

// ==================== 用户订阅操作 ====================

export async function getUserActiveSubscription(
  env: Env,
  userId: string
): Promise<UserSubscription | null> {
  try {
    if (!userId || typeof userId !== 'string') {
      console.log('getUserActiveSubscription: 无效的用户ID')
      return null
    }

    const supabase = getSupabase(env)
    const now = new Date()

    if (!(now instanceof Date) || isNaN(now.getTime())) {
      console.log('getUserActiveSubscription: 无效的时间对象')
      return null
    }

    console.log(`查询用户 ${userId} 的活跃订阅`)

    const result = await supabase
      .from(TABLE_NAMES.userSubscription)
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .gte('end_date', now.toISOString())
      .order('end_date', { ascending: false })
      .limit(1)

    const { data, error } = handleSupabaseResult(result)
    if (error) {
      console.log(`查询用户 ${userId} 订阅时发生错误:`, error)
      return null
    }

    const subscription = data && data.length > 0 ? data[0] : null
    console.log(`用户 ${userId} 的订阅查询结果:`, subscription ? '找到活跃订阅' : '无活跃订阅')
    return subscription
  } catch (error) {
    console.log(`获取用户 ${userId} 活跃订阅时发生异常:`, (error as any)?.message || error)
    return null
  }
}

export async function createUserSubscription(
  env: Env,
  data: {
    userId: string
    planId: string
    startDate: Date
    endDate: Date
    status: 'active' | 'expired' | 'cancelled' | 'pending'
    paymentId?: string
    autoRenew?: boolean
  }
): Promise<UserSubscription[]> {
  try {
    const supabase = getSupabase(env)

    const subscriptionResult = await supabase
      .from(TABLE_NAMES.userSubscription)
      .insert({
        user_id: data.userId,
        plan_id: data.planId,
        start_date: data.startDate.toISOString(),
        end_date: data.endDate.toISOString(),
        status: data.status,
        payment_id: data.paymentId,
        auto_renew: data.autoRenew || false
      })
      .select()

    const { data: subscription, error: subscriptionError } = handleSupabaseResult(subscriptionResult)
    if (subscriptionError) throw subscriptionError

    const planResult = await supabase
      .from(TABLE_NAMES.membershipPlan)
      .select('points_included, price')
      .eq('id', data.planId)
      .single()

    const { data: plan, error: planError } = handleSupabaseSingleResult(planResult)
    if (planError) {
      console.warn('获取会员计划信息失败，继续处理', planError)
    }

    try {
      await supabase.from('SubscriptionHistory').insert({
        user_id: data.userId,
        plan_id: data.planId,
        subscription_id: subscription[0]?.id,
        action: 'subscribe',
        amount: plan?.price || '0',
        points_granted: plan?.pointsIncluded || 0,
        payment_id: data.paymentId,
        metadata: {
          autoRenew: data.autoRenew,
          startDate: data.startDate.toISOString(),
          endDate: data.endDate.toISOString()
        }
      })
    } catch (historyError) {
      console.warn('创建订阅历史记录失败，但订阅已创建', historyError)
    }

    return subscription || []
  } catch (error) {
    console.error('创建用户订阅失败', error)
    throw error
  }
}

export async function updateSubscriptionStatus(
  env: Env,
  id: string,
  status: 'active' | 'expired' | 'cancelled'
): Promise<UserSubscription[]> {
  try {
    const supabase = getSupabase(env)

    const result = await supabase
      .from(TABLE_NAMES.userSubscription)
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()

    const { data: updatedSubscription, error } = handleSupabaseResult(result)
    if (error) throw error

    if (updatedSubscription && updatedSubscription.length > 0) {
      const subscription = updatedSubscription[0]
      const action = status === 'cancelled' ? 'cancel' : status === 'expired' ? 'expire' : 'renew'

      try {
        await supabase.from('SubscriptionHistory').insert({
          user_id: subscription.userId,
          plan_id: subscription.planId,
          subscription_id: subscription.id,
          action,
          amount: '0',
          points_granted: 0,
          payment_id: null,
          metadata: {
            previousStatus: 'active',
            newStatus: status,
            updatedAt: new Date().toISOString()
          }
        })
      } catch (historyError) {
        console.warn('创建订阅历史记录失败，但状态已更新', historyError)
      }
    }

    return updatedSubscription || []
  } catch (error) {
    console.error('更新订阅状态失败', error)
    throw error
  }
}

export async function getUserSubscriptionHistory(
  env: Env,
  userId: string
): Promise<UserSubscription[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.userSubscription)
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('获取用户订阅历史失败', error)
    throw error
  }
}

// ==================== 积分系统操作 ====================

export async function getUserPoints(env: Env, userId: string): Promise<UserPoints> {
  try {
    if (!userId || typeof userId !== 'string') {
      console.log('getUserPoints: 无效的用户ID')
      return getDefaultUserPoints(userId)
    }

    console.log(`查询用户 ${userId} 的积分信息`)

    const supabase = getSupabase(env)
    const result = await supabase.from(TABLE_NAMES.userPoints).select('*').eq('user_id', userId)

    const { data, error } = handleSupabaseResult(result)
    if (error) {
      console.log(`查询用户 ${userId} 积分时发生错误:`, error)
      return getDefaultUserPoints(userId)
    }

    const userPointsRecord = data && data.length > 0 ? data[0] : null

    if (!userPointsRecord) {
      console.log(`用户 ${userId} 积分记录不存在，返回默认值`)
      return getDefaultUserPoints(userId)
    }

    console.log(
      `用户 ${userId} 的积分查询结果: 总积分=${userPointsRecord.totalPoints}, 可用=${userPointsRecord.availablePoints}`
    )
    return userPointsRecord
  } catch (error) {
    console.log(`获取用户 ${userId} 积分时发生异常:`, (error as any)?.message || error)
    return getDefaultUserPoints(userId)
  }
}

function getDefaultUserPoints(userId: string): UserPoints {
  return {
    id: '',
    userId: userId,
    totalPoints: 0,
    usedPoints: 0,
    availablePoints: 0,
    cycleStartDate: null,
    cycleEndDate: null,
    membershipLevel: null,
    monthlyAllocation: 0,
    cycleConsumed: 0,
    cycleGifted: 0,
    cycleReceived: 0,
    lastCycleCheck: null,
    lastUpdated: new Date()
  }
}

export async function updateUserPoints(
  env: Env,
  userId: string,
  points: number,
  type: 'earn' | 'spend',
  source: 'subscription' | 'purchase' | 'generation' | 'refund' | 'bonus' | 'admin',
  sourceId?: string,
  description?: string
): Promise<UserPoints> {
  try {
    const supabase = getSupabase(env)

    const currentResult = await supabase
      .from(TABLE_NAMES.userPoints)
      .select('*')
      .eq('user_id', userId)
      .single()

    const { data: userPointsRecord, error: fetchError } = handleSupabaseSingleResult(currentResult)
    if (fetchError || !userPointsRecord) {
      throw new Error('用户积分记录不存在')
    }

    let newTotalPoints = userPointsRecord.totalPoints
    let newUsedPoints = userPointsRecord.usedPoints
    let newAvailablePoints = userPointsRecord.availablePoints

    if (type === 'earn') {
      newTotalPoints += points
      newAvailablePoints += points
    } else if (type === 'spend') {
      if (newAvailablePoints < points) {
        throw new Error(`积分不足，需要${points}积分，当前可用${newAvailablePoints}积分`)
      }
      newUsedPoints += points
      newAvailablePoints -= points
    }

    const updateResult = await supabase
      .from(TABLE_NAMES.userPoints)
      .update({
        total_points: newTotalPoints,
        used_points: newUsedPoints,
        available_points: newAvailablePoints,
        last_updated: new Date().toISOString()
      })
      .eq('user_id', userId)
      .select()
      .single()

    const { data: updatedRecord, error: updateError } = handleSupabaseSingleResult(updateResult)
    if (updateError) throw updateError

    try {
      await supabase.from(TABLE_NAMES.pointsTransaction).insert({
        user_id: userId,
        transaction_type: type,
        amount: points,
        source: source,
        source_id: sourceId,
        description: description || `${type === 'earn' ? '获得' : '消费'}${points}积分`,
        balance_after: newAvailablePoints,
        created_at: new Date().toISOString()
      })
    } catch (transactionError) {
      console.warn('创建积分交易记录失败，但积分已更新', transactionError)
    }

    return updatedRecord
  } catch (error) {
    console.error('更新用户积分失败', error)
    throw error
  }
}

export async function getUserPointsTransactions(
  env: Env,
  userId: string,
  limit = 50
): Promise<PointsTransaction[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.pointsTransaction)
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('获取用户积分交易记录失败', error)
    throw error
  }
}

// ==================== 会员状态检查 ====================

export async function checkUserMembership(
  env: Env,
  userId: string
): Promise<{
  isMember: boolean
  subscription: UserSubscription | null
  plan: MembershipPlan | null
}> {
  try {
    console.log(`🔍 [会员检查] 开始检查用户 ${userId} 的会员状态`)

    const subscription = await getUserActiveSubscription(env, userId)
    console.log(
      `🔍 [会员检查] 订阅查询结果:`,
      subscription
        ? {
            id: subscription.id,
            status: subscription.status,
            endDate: subscription.endDate,
            planId: subscription.planId
          }
        : '无订阅记录'
    )

    const now = new Date()
    const isMember =
      subscription && subscription.status === 'active' && new Date(subscription.endDate) > now

    console.log(`🔍 [会员检查] 会员状态判断:`, {
      hasSubscription: !!subscription,
      status: subscription?.status,
      endDate: subscription?.endDate,
      currentTime: now.toISOString(),
      isEndDateValid: subscription ? new Date(subscription.endDate) > now : false,
      finalResult: !!isMember
    })

    return {
      isMember: !!isMember,
      subscription,
      plan: null
    }
  } catch (error) {
    console.error('检查用户会员状态失败', error)
    throw error
  }
}

// ==================== 积分套餐操作 ====================

export async function getPointsPackages(env: Env): Promise<any[]> {
    try {
        const supabase = getSupabase(env);
        const result = await supabase
            .from(TABLE_NAMES.pointsPackage)
            .select('*')
            .eq('is_active', true)
            .order('sort_order');

        const { data, error } = handleSupabaseResult(result);
        if (error) throw error;
        return data || [];
    } catch (error) {
        console.error('获取积分套餐失败', error);
        throw error;
    }
}

export async function getPointsPackageById(env: Env, id: string): Promise<any | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase.from(TABLE_NAMES.pointsPackage).select('*').eq('id', id).single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) throw error
    return data
  } catch (error) {
    console.error('获取积分套餐详情失败', error)
    throw error
  }
}

// ==================== 积分管理 ====================

export async function addUserPoints(
  env: Env,
  params: {
    userId: string
    amount: number
    source: string
    sourceId?: string
    description?: string
  }
): Promise<PointsTransaction> {
  try {
    const supabase = getSupabase(env)

    const userPoints = await supabase
      .from(TABLE_NAMES.userPoints)
      .select('*')
      .eq('user_id', params.userId)
      .single()

    if (!userPoints.data) {
      const newUserPoints = await supabase
        .from(TABLE_NAMES.userPoints)
        .insert({
          user_id: params.userId,
          total_points: params.amount,
          available_points: params.amount,
          used_points: 0
        })
        .select()
        .single()

      const { data: newData, error: newError } = handleSupabaseSingleResult(newUserPoints)
      if (newError) throw newError
      userPoints.data = newData
    } else {
      const updatedUserPoints = await supabase
        .from(TABLE_NAMES.userPoints)
        .update({
          total_points: userPoints.data.total_points + params.amount,
          available_points: userPoints.data.available_points + params.amount,
          last_updated: new Date().toISOString()
        })
        .eq('user_id', params.userId)
        .select()
        .single()

      const { data: updatedData, error: updateError } = handleSupabaseSingleResult(updatedUserPoints)
      if (updateError) throw updateError
      userPoints.data = updatedData
    }

    const transaction = await supabase
      .from(TABLE_NAMES.pointsTransaction)
      .insert({
        user_id: params.userId,
        transaction_type: 'earn',
        amount: params.amount,
        source: params.source,
        source_id: params.sourceId,
        description: params.description,
        balance_after: userPoints.data.available_points
      })
      .select()
      .single()

    const { data, error } = handleSupabaseSingleResult(transaction)
    if (error) throw error

    return data
  } catch (error) {
    console.error('添加用户积分失败', error)
    throw error
  }
}