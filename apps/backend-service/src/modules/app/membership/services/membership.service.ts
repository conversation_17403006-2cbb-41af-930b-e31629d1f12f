import * as membershipRepository from '../repositories/membership.repository'
import type { Env } from '@/types/env'
import type { MembershipPlan, UserSubscription, UserPoints, PointsTransaction } from '@/lib/db/schema'

// ==================== 用户ID解析操作 ====================

export async function getLocalUserIdFromSupabaseId(env: Env, supabaseUserId: string) {
  return membershipRepository.getLocalUserIdFromSupabaseId(env, supabaseUserId)
}

export async function ensureUserExists(env: Env, supabaseUserId: string, email: string) {
  return membershipRepository.ensureUserExists(env, supabaseUserId, email)
}

// ==================== 会员计划操作 ====================

export async function getMembershipPlans(env: Env): Promise<MembershipPlan[]> {
  return membershipRepository.getMembershipPlans(env)
}

export async function getMembershipPlanById(env: Env, id: string): Promise<MembershipPlan | null> {
  return membershipRepository.getMembershipPlanById(env, id)
}

// ==================== 用户订阅操作 ====================

export async function getUserActiveSubscription(env: Env, userId: string): Promise<UserSubscription | null> {
  return membershipRepository.getUserActiveSubscription(env, userId)
}

export async function createUserSubscription(
  env: Env,
  data: {
    userId: string
    planId: string
    startDate: Date
    endDate: Date
    status: 'active' | 'expired' | 'cancelled' | 'pending'
    paymentId?: string
    autoRenew?: boolean
  }
) {
  return membershipRepository.createUserSubscription(env, data)
}

export async function updateSubscriptionStatus(
  env: Env,
  id: string,
  status: 'active' | 'expired' | 'cancelled'
) {
  return membershipRepository.updateSubscriptionStatus(env, id, status)
}

export async function getUserSubscriptionHistory(env: Env, userId: string) {
  return membershipRepository.getUserSubscriptionHistory(env, userId)
}

// ==================== 积分系统操作 ====================

export async function getUserPoints(env: Env, userId: string): Promise<UserPoints> {
  return membershipRepository.getUserPoints(env, userId)
}

export async function updateUserPoints(
  env: Env,
  userId: string,
  points: number,
  type: 'earn' | 'spend',
  source: 'subscription' | 'purchase' | 'generation' | 'refund' | 'bonus' | 'admin',
  sourceId?: string,
  description?: string
) {
  return membershipRepository.updateUserPoints(env, userId, points, type, source, sourceId, description)
}

export async function getUserPointsTransactions(
  env: Env,
  userId: string,
  limit = 50
): Promise<PointsTransaction[]> {
  return membershipRepository.getUserPointsTransactions(env, userId, limit)
}

// ==================== 积分套餐操作 ====================

export async function getPointsPackages(env: Env) {
  return membershipRepository.getPointsPackages(env)
}

export async function getPointsPackageById(env: Env, id: string) {
  return membershipRepository.getPointsPackageById(env, id)
}

// ==================== 会员状态检查 ====================

export async function checkUserMembership(env: Env, userId: string) {
  return membershipRepository.checkUserMembership(env, userId)
}

// ==================== 积分管理 ====================

export async function addUserPoints(
  env: Env,
  params: {
    userId: string
    amount: number
    source: string
    sourceId?: string
    description?: string
  }
) {
  return membershipRepository.addUserPoints(env, params)
}