import type { Context } from 'hono'
import * as membershipService from '../services/membership.service'
import type { Env } from '@/types/env'

// ==================== 会员计划操作 ====================

export const getMembershipPlansHandler = async (c: Context) => {
  try {
    const plans = await membershipService.getMembershipPlans(c.env as Env)
    return c.json(plans)
  } catch (error) {
    console.error('获取会员计划失败', error)
    return c.json({ error: '获取会员计划失败' }, 500)
  }
}

export const getMembershipPlanByIdHandler = async (c: Context) => {
  try {
    const id = c.req.param('id')
    const plan = await membershipService.getMembershipPlanById(c.env as Env, id)
    return plan ? c.json(plan) : c.notFound()
  } catch (error) {
    console.error('获取会员计划详情失败', error)
    return c.json({ error: '获取会员计划详情失败' }, 500)
  }
}

// ==================== 用户订阅操作 ====================

export const getUserActiveSubscriptionHandler = async (c: Context) => {
  try {
    const userId = c.req.param('userId')
    const subscription = await membershipService.getUserActiveSubscription(c.env as Env, userId)
    return subscription ? c.json(subscription) : c.notFound()
  } catch (error) {
    console.error('获取用户活跃订阅失败', error)
    return c.json({ error: '获取用户活跃订阅失败' }, 500)
  }
}

export const createUserSubscriptionHandler = async (c: Context) => {
  try {
    const data = await c.req.json()
    const subscription = await membershipService.createUserSubscription(c.env as Env, data)
    return c.json(subscription, 201)
  } catch (error) {
    console.error('创建用户订阅失败', error)
    return c.json({ error: '创建用户订阅失败' }, 500)
  }
}

export const updateSubscriptionStatusHandler = async (c: Context) => {
  try {
    const id = c.req.param('id')
    const { status } = await c.req.json()
    const updatedSubscription = await membershipService.updateSubscriptionStatus(c.env as Env, id, status)
    return c.json(updatedSubscription)
  } catch (error) {
    console.error('更新订阅状态失败', error)
    return c.json({ error: '更新订阅状态失败' }, 500)
  }
}

export const getUserSubscriptionHistoryHandler = async (c: Context) => {
  try {
    const userId = c.req.param('userId')
    const history = await membershipService.getUserSubscriptionHistory(c.env as Env, userId)
    return c.json(history)
  } catch (error) {
    console.error('获取用户订阅历史失败', error)
    return c.json({ error: '获取用户订阅历史失败' }, 500)
  }
}

// ==================== 积分系统操作 ====================

export const getUserPointsHandler = async (c: Context) => {
  try {
    const userId = c.req.param('userId')
    const points = await membershipService.getUserPoints(c.env as Env, userId)
    return c.json(points)
  } catch (error) {
    console.error('获取用户积分失败', error)
    return c.json({ error: '获取用户积分失败' }, 500)
  }
}

export const updateUserPointsHandler = async (c: Context) => {
  try {
    const userId = c.req.param('userId')
    const { points, type, source, sourceId, description } = await c.req.json()
    const updatedPoints = await membershipService.updateUserPoints(
      c.env as Env,
      userId,
      points,
      type,
      source,
      sourceId,
      description
    )
    return c.json(updatedPoints)
  } catch (error) {
    console.error('更新用户积分失败', error)
    return c.json({ error: (error as Error).message }, 400)
  }
}

export const getUserPointsTransactionsHandler = async (c: Context) => {
  try {
    const userId = c.req.param('userId')
    const limit = c.req.query('limit') ? Number.parseInt(c.req.query('limit')!) : 50
    const transactions = await membershipService.getUserPointsTransactions(c.env as Env, userId, limit)
    return c.json(transactions)
  } catch (error) {
    console.error('获取用户积分交易记录失败', error)
    return c.json({ error: '获取用户积分交易记录失败' }, 500)
  }
}

// ==================== 积分套餐操作 ====================

export const getPointsPackagesHandler = async (c: Context) => {
  try {
    const packages = await membershipService.getPointsPackages(c.env as Env)
    return c.json(packages)
  } catch (error) {
    console.error('获取积分套餐失败', error)
    return c.json({ error: '获取积分套餐失败' }, 500)
  }
}

export const getPointsPackageByIdHandler = async (c: Context) => {
  try {
    const id = c.req.param('id')
    const pkg = await membershipService.getPointsPackageById(c.env as Env, id)
    return pkg ? c.json(pkg) : c.notFound()
  } catch (error) {
    console.error('获取积分套餐详情失败', error)
    return c.json({ error: '获取积分套餐详情失败' }, 500)
  }
}

// ==================== 会员状态检查 ====================

export const checkUserMembershipHandler = async (c: Context) => {
  try {
    const userId = c.req.param('userId')
    const membershipStatus = await membershipService.checkUserMembership(c.env as Env, userId)
    return c.json(membershipStatus)
  } catch (error) {
    console.error('检查用户会员状态失败', error)
    return c.json({ error: '检查用户会员状态失败' }, 500)
  }
}