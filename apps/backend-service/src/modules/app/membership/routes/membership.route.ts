import { Hono } from 'hono'
import {
  getMembershipPlansHandler,
  getMembershipPlanByIdHandler,
  getUserActiveSubscriptionHandler,
  createUserSubscriptionHandler,
  updateSubscriptionStatusHandler,
  getUserSubscriptionHistoryHandler,
  getUserPointsHandler,
  updateUserPointsHandler,
  getUserPointsTransactionsHandler,
  getPointsPackagesHandler,
  getPointsPackageByIdHandler,
  checkUserMembershipHandler
} from '../controllers/membership.controller'

const route = new Hono()

// 会员计划路由
route.get('/plans', getMembershipPlansHandler)
route.get('/plans/:id', getMembershipPlanByIdHandler)

// 用户订阅路由
route.get('/subscription', getUserActiveSubscriptionHandler)
route.post('/subscription', createUserSubscriptionHandler)
route.patch('/subscriptions/:id/status', updateSubscriptionStatusHandler)
route.get('/subscription/history', getUserSubscriptionHistoryHandler)

// 积分系统路由
route.get('/points', getUserPointsHandler)
route.post('/points/spend', updateUserPointsHandler)
route.get('/points/transactions', getUserPointsTransactionsHandler)

// 会员状态检查路由
route.get('/status', checkUserMembershipHandler)
route.get('/points-packages', getPointsPackagesHandler)
route.get('/points-packages/:id', getPointsPackageByIdHandler)

// 会员状态检查路由
route.get('/check/:userId', checkUserMembershipHandler)

export default route