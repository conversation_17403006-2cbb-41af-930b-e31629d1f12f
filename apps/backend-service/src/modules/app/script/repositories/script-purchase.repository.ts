import type { Env } from '@/types/env'
import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '@/lib/db/supabase-types'
import type { ScriptPurchase } from '@/lib/db/schema'

/**
 * 检查用户是否已购买剧本
 */
export async function checkScriptPurchaseRepo(
  env: Env,
  userId: string,
  scriptId: string
): Promise<ScriptPurchase | null> {
  try {
    const supabase = getSupabase(env)

    const result = await supabase
      .from(TABLE_NAMES.scriptPurchase)
      .select('*')
      .eq('user_id', userId)
      .eq('script_id', scriptId)
      .eq('status', 'completed')
      .gt('expires_at', new Date().toISOString())
      .limit(1)
      .single()

    const { data: purchase, error } = handleSupabaseSingleResult(result)
    if (error) return null
    return purchase
  } catch (error) {
    console.error('检查购买状态失败:', error)
    return null
  }
}

/**
 * 创建剧本购买记录
 */
export async function createScriptPurchaseRepo(
  env: Env,
  data: {
    userId: string
    scriptId: string
    pointsCost: number
    transactionId?: string
  }
): Promise<ScriptPurchase[]> {
  try {
    const supabase = getSupabase(env)
    const expiresAt = new Date()
    expiresAt.setFullYear(expiresAt.getFullYear() + 1)

    const result = await supabase
      .from(TABLE_NAMES.scriptPurchase)
      .insert({
        user_id: data.userId,
        script_id: data.scriptId,
        points_cost: data.pointsCost,
        transaction_id: data.transactionId,
        expires_at: expiresAt.toISOString(),
        status: 'completed'
      })
      .select()

    const { data: purchases, error } = handleSupabaseResult(result)
    if (error) throw error
    return purchases || []
  } catch (error) {
    console.error('创建购买记录失败:', error)
    throw error
  }
}

/**
 * 获取用户已购买的剧本ID列表
 */
export async function getUserPurchasedScriptIdsRepo(env: Env, userId: string): Promise<string[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.scriptPurchase)
      .select('script_id')
      .eq('user_id', userId)
      .eq('status', 'completed')
      .gt('expires_at', new Date().toISOString())

    const { data: purchases, error } = handleSupabaseResult(result)
    if (error) throw error

    return (purchases || []).map((p: any) => p.script_id as string).filter(Boolean)
  } catch (error) {
    console.error('获取购买剧本ID列表失败:', error)
    return []
  }
}

/**
 * 标记剧本内容已下载
 */
export async function markScriptDownloadedRepo(
  env: Env,
  userId: string,
  scriptId: string
): Promise<boolean> {
  try {
    const supabase = getSupabase(env)
    const now = new Date()

    const result = await supabase
      .from(TABLE_NAMES.scriptPurchase)
      .update({
        is_downloaded: true,
        downloaded_at: now.toISOString(),
        updated_at: now.toISOString()
      })
      .eq('user_id', userId)
      .eq('script_id', scriptId)
      .eq('status', 'completed')
      .select()

    const { data: updates, error } = handleSupabaseResult(result)
    if (error) throw error
    return (updates || []).length > 0
  } catch (error) {
    console.error('标记下载失败:', error)
    return false
  }
}

/**
 * 购买剧本（包含积分交易）
 */
export async function purchaseScriptWithPointsRepo(
  env: Env,
  data: {
    userId: string
    scriptId: string
    pointsCost: number
    scriptTitle: string
  }
): Promise<{
  success: boolean
  purchase?: ScriptPurchase
  remainingPoints?: number
  error?: string
  errorCode?: string
  errorData?: Record<string, any>
}> {
  try {
    const supabase = getSupabase(env)

    // 检查用户积分
    const userPointsResult = await supabase
      .from(TABLE_NAMES.userPoints)
      .select('*')
      .eq('user_id', data.userId)
      .single()

    const { data: userPointsRecord, error: pointsError } = handleSupabaseSingleResult(userPointsResult)
    if (pointsError || !userPointsRecord) {
      return { success: false, errorCode: 'USER_POINTS_NOT_FOUND', error: '用户积分记录不存在' }
    }

    if (userPointsRecord.available_points < data.pointsCost) {
      return {
        success: false,
        errorCode: 'INSUFFICIENT_POINTS',
        errorData: { required: data.pointsCost, available: userPointsRecord.available_points },
        remainingPoints: userPointsRecord.available_points
      }
    }

    // 更新积分
    const newAvailablePoints = userPointsRecord.available_points - data.pointsCost
    const newUsedPoints = userPointsRecord.used_points + data.pointsCost

    const pointsUpdateResult = await supabase
      .from(TABLE_NAMES.userPoints)
      .update({
        available_points: newAvailablePoints,
        used_points: newUsedPoints,
        last_updated: new Date().toISOString()
      })
      .eq('user_id', data.userId)
      .eq('available_points', userPointsRecord.available_points)
      .select()

    const { data: updatedPoints, error: updateError } = handleSupabaseResult(pointsUpdateResult)
    if (updateError || !updatedPoints?.length) {
      return { success: false, error: '积分更新失败，可能有并发操作' }
    }

    // 创建积分交易记录
    const transactionResult = await supabase
      .from(TABLE_NAMES.pointsTransaction)
      .insert({
        user_id: data.userId,
        transaction_type: 'spend',
        amount: data.pointsCost,
        source: 'purchase',
        source_id: data.scriptId,
        description: `购买剧本《${data.scriptTitle}》`,
        balance_after: newAvailablePoints
      })
      .select()
      .single()

    const { data: transaction, error: transactionError } = handleSupabaseSingleResult(transactionResult)
    if (transactionError || !transaction) {
      throw new Error('创建积分交易记录失败')
    }

    // 创建购买记录
    const expiresAt = new Date()
    expiresAt.setFullYear(expiresAt.getFullYear() + 1)

    const purchaseResult = await supabase
      .from(TABLE_NAMES.scriptPurchase)
      .insert({
        user_id: data.userId,
        script_id: data.scriptId,
        points_cost: data.pointsCost,
        transaction_id: transaction.id,
        expires_at: expiresAt.toISOString(),
        status: 'completed'
      })
      .select()
      .single()

    const { data: purchase, error: purchaseError } = handleSupabaseSingleResult(purchaseResult)
    if (purchaseError || !purchase) {
      throw new Error('创建购买记录失败')
    }

    return {
      success: true,
      purchase,
      remainingPoints: newAvailablePoints
    }
  } catch (error) {
    console.error('剧本购买异常:', error)
    return { success: false, error: error instanceof Error ? error.message : '剧本购买失败' }
  }
}