import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '@/lib/db/supabase-types'
import type { Env } from '@/types/env'
import type { <PERSON>rip<PERSON>, ScriptUsage } from '@/lib/db/schema'

/**
 * 获取公开剧本列表（分页）
 */
export async function getPublicScriptsRepo(
  env: Env,
  options: {
    limit?: number
    offset?: number
    category?: string
    search?: string
    sortBy?: 'latest' | 'popular' | 'rating'
  } = {}
): Promise<Script[]> {
  try {
    const supabase = getSupabase(env)
    const { limit = 20, offset = 0, category, search, sortBy = 'latest' } = options

    let query = supabase
      .from(TABLE_NAMES.script)
      .select('*')
      .eq('is_public', true)
      .eq('is_active', true)

    if (category) {
      query = query.eq('category', category)
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`)
    }

    let sortColumn: string
    switch (sortBy) {
      case 'popular':
        sortColumn = 'usage_count'
        break
      case 'rating':
        sortColumn = 'rating'
        break
      case 'latest':
      default:
        sortColumn = 'created_at'
        break
    }

    const result = await query
      .order(sortColumn, { ascending: false })
      .range(offset, offset + limit - 1)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('获取公开剧本列表失败', error)
    throw error
  }
}

/**
 * 根据ID获取剧本详情
 */
export async function getScriptByIdRepo(env: Env, id: string): Promise<Script | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.script)
      .select('*')
      .eq('id', id)
      .eq('is_active', true)
      .single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) return null
    return data
  } catch (error) {
    console.error('获取剧本详情失败', error)
    throw error
  }
}

/**
 * 创建新剧本
 */
export async function createScriptRepo(
  env: Env,
  data: {
    title: string
    description: string
    coverImage: string
    duration: string
    tags?: string[]
    category?: string
    content?: any
    audioUrl?: string
    totalDuration?: number
    stageCount?: number
    isPublic?: boolean
    isActive?: boolean
    isPremium?: boolean
    pointsCost?: number
    createdBy?: string
  }
): Promise<Script[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.script)
      .insert({
        title: data.title,
        description: data.description,
        cover_image: data.coverImage,
        duration: data.duration,
        tags: data.tags || [],
        category: data.category,
        content: data.content || {},
        audio_url: data.audioUrl,
        total_duration: data.totalDuration,
        stage_count: data.stageCount,
        is_public: data.isPublic ?? true,
        is_active: data.isActive ?? true,
        is_premium: data.isPremium ?? false,
        points_cost: data.pointsCost ?? 0,
        created_by: data.createdBy
      })
      .select()

    const { data: scripts, error } = handleSupabaseResult(result)
    if (error) throw error
    return scripts || []
  } catch (error) {
    console.error('创建剧本失败', error)
    throw error
  }
}

/**
 * 更新剧本
 */
export async function updateScriptByIdRepo(
  env: Env,
  id: string,
  data: Partial<{
    title: string
    description: string
    coverImage: string
    duration: string
    tags: string[]
    category: string
    content: any
    audioUrl: string
    totalDuration: number
    stageCount: number
    isPublic: boolean
    isActive: boolean
    isPremium: boolean
    pointsCost: number
  }>
): Promise<Script[]> {
  try {
    const supabase = getSupabase(env)

    const updateData: any = {}
    if (data.title !== undefined) updateData.title = data.title
    if (data.description !== undefined) updateData.description = data.description
    if (data.coverImage !== undefined) updateData.cover_image = data.coverImage
    if (data.duration !== undefined) updateData.duration = data.duration
    if (data.tags !== undefined) updateData.tags = data.tags
    if (data.category !== undefined) updateData.category = data.category
    if (data.content !== undefined) updateData.content = data.content
    if (data.audioUrl !== undefined) updateData.audio_url = data.audioUrl
    if (data.totalDuration !== undefined) updateData.total_duration = data.totalDuration
    if (data.stageCount !== undefined) updateData.stage_count = data.stageCount
    if (data.isPublic !== undefined) updateData.is_public = data.isPublic
    if (data.isActive !== undefined) updateData.is_active = data.isActive
    if (data.isPremium !== undefined) updateData.is_premium = data.isPremium
    if (data.pointsCost !== undefined) updateData.points_cost = data.pointsCost

    updateData.updated_at = new Date().toISOString()

    const result = await supabase.from(TABLE_NAMES.script).update(updateData).eq('id', id).select()

    const { data: scripts, error } = handleSupabaseResult(result)
    if (error) throw error
    return scripts || []
  } catch (error) {
    console.error('更新剧本失败', error)
    throw error
  }
}

/**
 * 删除剧本（软删除）
 */
export async function deleteScriptByIdRepo(env: Env, id: string): Promise<Script[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.script)
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('删除剧本失败', error)
    throw error
  }
}

/**
 * 增加剧本使用次数
 */
export async function incrementScriptUsageRepo(env: Env, scriptId: string): Promise<Script[]> {
  try {
    const supabase = getSupabase(env)

    const currentResult = await supabase
      .from(TABLE_NAMES.script)
      .select('usage_count')
      .eq('id', scriptId)
      .single()

    const { data: current } = handleSupabaseSingleResult(currentResult)
    if (!current) throw new Error('剧本不存在')

    const result = await supabase
      .from(TABLE_NAMES.script)
      .update({
        usage_count: (current.usage_count || 0) + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', scriptId)
      .select()

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('增加剧本使用次数失败', error)
    throw error
  }
}

/**
 * 获取剧本分类列表
 */
export async function getScriptCategoriesRepo(
  env: Env
): Promise<Array<{ category: string; count: number }>> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.script)
      .select('category')
      .eq('is_public', true)
      .eq('is_active', true)
      .not('category', 'is', null)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error

    const categoryStats = (data || []).reduce((acc: any, item: any) => {
      const category = item.category
      if (category) {
        acc[category] = (acc[category] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>)

    return Object.entries(categoryStats)
      .map(([category, count]) => ({ category, count }))
      .sort((a, b) => b.count - a.count)
  } catch (error) {
    console.error('获取剧本分类失败', error)
    throw error
  }
}

/**
 * 创建剧本使用记录
 */
export async function createScriptUsageRepo(
  env: Env,
  data: {
    userId: string
    scriptId: string
    chatId?: string
    duration?: number
    rating?: number
    feedback?: string
  }
): Promise<ScriptUsage[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.scriptUsage)
      .insert({
        user_id: data.userId,
        script_id: data.scriptId,
        chat_id: data.chatId,
        duration: data.duration?.toString(),
        rating: data.rating,
        feedback: data.feedback
      })
      .select()

    const { data: usageRecords, error } = handleSupabaseResult(result)
    if (error) throw error
    return usageRecords || []
  } catch (error) {
    console.error('创建剧本使用记录失败', error)
    throw error
  }
}

/**
 * 获取用户的剧本使用历史
 */
export async function getUserScriptUsageHistoryRepo(
  env: Env,
  userId: string,
  options: {
    limit?: number
    offset?: number
  } = {}
): Promise<Array<{ usage: ScriptUsage; script: Script | null }>> {
  try {
    const supabase = getSupabase(env)
    const { limit = 20, offset = 0 } = options

    const usageResult = await supabase
      .from(TABLE_NAMES.scriptUsage)
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    const { data: usageRecords, error: usageError } = handleSupabaseResult(usageResult)
    if (usageError) throw usageError

    const results = await Promise.all(
      (usageRecords || []).map(async (usage: any) => {
        const scriptResult = await supabase
          .from(TABLE_NAMES.script)
          .select('*')
          .eq('id', usage.script_id)
          .single()

        const { data: script } = handleSupabaseSingleResult(scriptResult)

        return {
          usage,
          script
        }
      })
    )

    return results
  } catch (error) {
    console.error('获取用户剧本使用历史失败', error)
    throw error
  }
}

/**
 * 更新剧本评分
 */
export async function updateScriptRatingRepo(env: Env, scriptId: string): Promise<Script[] | null> {
  try {
    const supabase = getSupabase(env)

    const ratingsResult = await supabase
      .from(TABLE_NAMES.scriptUsage)
      .select('rating')
      .eq('script_id', scriptId)
      .not('rating', 'is', null)

    const { data: ratings, error: ratingsError } = handleSupabaseResult(ratingsResult)
    if (ratingsError) throw ratingsError

    if (ratings && ratings.length > 0 && ratings[0].rating !== null) {
      const avgRating = ratings.reduce((sum: number, item: any) => sum + (item.rating || 0), 0) / ratings.length
      const ratingCount = ratings.length

      const result = await supabase
        .from(TABLE_NAMES.script)
        .update({
          rating: Number.parseFloat(avgRating.toFixed(2)),
          rating_count: ratingCount,
          updated_at: new Date().toISOString()
        })
        .eq('id', scriptId)
        .select()

      const { data, error } = handleSupabaseResult(result)
      if (error) throw error
      return data || []
    }

    return null
  } catch (error) {
    console.error('更新剧本评分失败', error)
    throw error
  }
}