import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import {
  getPublicScriptsHandler,
  getScriptCategoriesHandler,
  getUserPurchasedScriptIdsHandler,
  createScriptUsageHandler,
  getUserScriptUsageHistoryHandler,
  createScriptHandler,
  getScriptByIdHandler,
  getScriptContentHandler,
  getScriptStagesHandler,
  updateScriptByIdHandler,
  deleteScriptByIdHandler,
  useScriptHandler,
  rateScriptHandler,
  purchaseScriptHandler,
  checkScriptPurchaseHandler,
  downloadScriptHandler
} from '../controllers/script.controller'

// 验证模式
const getScriptsQuerySchema = z.object({
  limit: z.coerce.number().optional().default(20),
  offset: z.coerce.number().optional().default(0),
  category: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['latest', 'popular', 'rating']).optional().default('latest')
})

const createScriptSchema = z.object({
  title: z.string().min(1).max(100),
  description: z.string().min(1),
  coverImage: z.string().url(),
  duration: z.string().min(1),
  tags: z.array(z.string()).optional().default([]),
  category: z.string().optional(),
  content: z.any().optional(),
  isPublic: z.boolean().optional().default(true),
  isPremium: z.boolean().optional().default(false)
})

const updateScriptSchema = z.object({
  title: z.string().min(1).max(100).optional(),
  description: z.string().min(1).optional(),
  coverImage: z.string().url().optional(),
  duration: z.string().min(1).optional(),
  tags: z.array(z.string()).optional(),
  category: z.string().optional(),
  content: z.any().optional(),
  isPublic: z.boolean().optional(),
  isPremium: z.boolean().optional()
})

const ratingSchema = z.object({
  rating: z.number().min(1).max(5)
})

// 创建路由实例
const route = new Hono()

// 公开路由
route.get('/', zValidator('query', getScriptsQuerySchema), getPublicScriptsHandler)
route.get('/categories', getScriptCategoriesHandler)

// 需要认证的路由
route.get('/purchased', authMiddleware, getUserPurchasedScriptIdsHandler)
route.post('/usage', authMiddleware, createScriptUsageHandler)
route.get('/usage/history', authMiddleware, getUserScriptUsageHistoryHandler)
route.post('/', authMiddleware, zValidator('json', createScriptSchema), createScriptHandler)
route.get('/:id', getScriptByIdHandler)
route.get('/:id/content', getScriptContentHandler)
route.get('/:id/stages', getScriptStagesHandler)
route.put('/:id', authMiddleware, zValidator('json', updateScriptSchema), updateScriptByIdHandler)
route.delete('/:id', authMiddleware, deleteScriptByIdHandler)
route.post('/:id/use', authMiddleware, useScriptHandler)
route.post('/:id/rating', authMiddleware, zValidator('json', ratingSchema), rateScriptHandler)
route.post('/:id/purchase', authMiddleware, purchaseScriptHandler)
route.get('/:id/purchase-status', authMiddleware, checkScriptPurchaseHandler)
route.post('/:id/download', authMiddleware, downloadScriptHandler)

export default route