import { Context } from 'hono'
import type { Env } from '@/types/env'
import type { SupportedLanguage } from '@/i18n/config'
import { BusinessError, ErrorCode } from '@/types/errors'
import { createSuccessResponse } from '@/types/responses'
import {
  getPublicScripts,
  getScriptById,
  createScript,
  updateScriptById,
  deleteScriptById,
  incrementScriptUsage,
  getScriptCategories,
  createScriptUsage,
  getUserScriptUsageHistory,
  updateScriptRating
} from '../services/script.service'
import {
  checkScriptPurchase,
  createScriptPurchase,
  getUserPurchasedScriptIds,
  markScriptDownloaded,
  purchaseScriptWithPoints
} from '../services/script-purchase.service'
import { throwBusinessError } from '@/middleware/global-error-handler'
import type { ScriptContent } from '@/types/script'
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository'

// 获取国际化函数的辅助函数
function getI18n(
  c: Context<{
    Variables: {
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>
): (key: string, params?: Record<string, string | number>) => string {
  return c.get('t')
}

// 辅助函数：从Supabase用户上下文解析本地用户ID
async function resolveLocalUserId(c: Context<{ Bindings: Env }>): Promise<string> {
  const supabaseUser = c.get('user')
  if (!supabaseUser) {
    throwBusinessError(ErrorCode.AUTH_UNAUTHORIZED)
  }

  const dbUser = await getUserBySupabaseId(c.env, supabaseUser.id)
  if (!dbUser) {
    throwBusinessError(ErrorCode.USER_NOT_FOUND)
  }

  return dbUser.id
}

/**
 * 获取公开剧本列表
 */
export const getPublicScriptsHandler = async (c: Context<{ Bindings: Env }>) => {
  const query = c.req.valid('query')

  const scripts = await getPublicScripts(c.env, {
    limit: query.limit,
    offset: query.offset,
    category: query.category,
    search: query.search,
    sortBy: query.sortBy
  })

 // 移除敏感内容，列表页面不返回content和audioUrl
    const filteredScripts = scripts.map(script => ({
      id: script.id,
      title: script.title,
      description: script.description,
      coverImage: script.coverImage,
      duration: script.duration,
      tags: script.tags,
      category: script.category,
      pointsCost: script.pointsCost,
      isPublic: script.isPublic,
      isPremium: script.isPremium,
      usageCount: script.usageCount,
      rating: script.rating,
      stageCount: script.stageCount,
      totalDuration: script.totalDuration,
      createdAt: script.createdAt,
      updatedAt: script.updatedAt
      // 不返回 content 和 audioUrl
    }))

  return c.json(createSuccessResponse(filteredScripts))
}

/**
 * 获取剧本分类
 */
export const getScriptCategoriesHandler = async (c: Context<{ Bindings: Env }>) => {
  const categories = await getScriptCategories(c.env)
  return c.json(createSuccessResponse(categories))
}

/**
 * 获取用户已购买的剧本ID列表
 */
export const getUserPurchasedScriptIdsHandler = async (c: Context<{ Bindings: Env }>) => {
  const userId = await resolveLocalUserId(c)
  const scriptIds = await getUserPurchasedScriptIds(c.env, userId)
  return c.json(createSuccessResponse({ scriptIds }))
}

/**
 * 创建剧本使用记录
 */
export const createScriptUsageHandler = async (c: Context<{ Bindings: Env }>) => {
  const data = await c.req.json()
  const userId = await resolveLocalUserId(c)

  if (!data.scriptId) {
    throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'scriptId' })
  }

  const [usage] = await createScriptUsage(c.env, {
    ...data,
    userId
  })

  if (data.rating) {
    await updateScriptRating(c.env, data.scriptId)
  }

  const t = getI18n(c)
  return c.json(createSuccessResponse(usage, t('script_usage_created')), 201)
}

/**
 * 获取用户的剧本使用历史
 */
export const getUserScriptUsageHistoryHandler = async (c: Context<{ Bindings: Env }>) => {
  const userId = await resolveLocalUserId(c)
  const limit = Number.parseInt(c.req.query('limit') || '20', 10)
  const offset = Number.parseInt(c.req.query('offset') || '0', 10)

  const history = await getUserScriptUsageHistory(c.env, userId, {
    limit,
    offset
  })

  return c.json(createSuccessResponse({
    data: history,
    pagination: {
      limit,
      offset,
      total: history.length
    }
  }))
}

/**
 * 创建新剧本
 */
export const createScriptHandler = async (c: Context<{ Bindings: Env }>) => {
  const scriptData = c.req.valid('json')
  const userId = await resolveLocalUserId(c)

  const [script] = await createScript(c.env, {
    ...scriptData,
    createdBy: userId
  })

  const t = getI18n(c)
  return c.json(createSuccessResponse(script, t('script_created')), 201)
}

/**
 * 根据ID获取剧本详情
 */
export const getScriptByIdHandler = async (c: Context<{ Bindings: Env }>) => {
  const id = c.req.param('id')

  if (!id) {
    throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
  }

  const script = await getScriptById(c.env, id)

  if (!script) {
    throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId: id })
  }

  return c.json(createSuccessResponse(script))
}

/**
 * 获取剧本详细内容
 */
export const getScriptContentHandler = async (c: Context<{ Bindings: Env }>) => {
  const id = c.req.param('id')

  if (!id) {
    throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
  }

  const script = await getScriptById(c.env, id)

  if (!script) {
    throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId: id })
  }

  return c.json(createSuccessResponse({
 id: script.id,
      title: script.title,
      content: script.content,
      audioUrl: script.audioUrl,
      totalDuration: script.totalDuration,
      stageCount: script.stageCount
  }))
}

/**
 * 获取剧本阶段列表
 */
export const getScriptStagesHandler = async (c: Context<{ Bindings: Env }>) => {
  const id = c.req.param('id')

  if (!id) {
    throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
  }

  const script = await getScriptById(c.env, id)

  if (!script) {
    throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId: id })
  }

  const content = script.content as ScriptContent
  if (!content || !content.stages) {
    throwBusinessError(ErrorCode.SCRIPT_INVALID_STAGE, { scriptId: id })
  }

  const stages = content.stages.map(stage => ({
    stage: stage.stage,
    stageTitle: stage.stageTitle,
    duration: stage.duration,
    pics: stage.pics,
    dialogueCount: stage.dialogues?.length || 0
  }))

  return c.json(createSuccessResponse(stages))
}

/**
 * 更新剧本信息
 */
export const updateScriptByIdHandler = async (c: Context<{ Bindings: Env }>) => {
  const id = c.req.param('id')
  const updateData = c.req.valid('json')

  if (!id) {
    throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
  }

  const userId = await resolveLocalUserId(c)
  const existingScript = await getScriptById(c.env, id)

  if (!existingScript) {
    throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId: id })
  }

  if (existingScript.createdBy !== userId) {
    throwBusinessError(ErrorCode.SCRIPT_ACCESS_DENIED, { scriptId: id })
  }

  const [updatedScript] = await updateScriptById(c.env, id, updateData)
  const t = getI18n(c)
  return c.json(createSuccessResponse(updatedScript, t('script_updated')))
}

/**
 * 删除剧本
 */
export const deleteScriptByIdHandler = async (c: Context<{ Bindings: Env }>) => {
  const id = c.req.param('id')

  if (!id) {
    throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
  }

  const userId = await resolveLocalUserId(c)
  const existingScript = await getScriptById(c.env, id)

  if (!existingScript) {
    throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId: id })
  }

  if (existingScript.created_by !== userId) {
    throwBusinessError(ErrorCode.SCRIPT_ACCESS_DENIED, { scriptId: id })
  }

  await deleteScriptById(c.env, id)
  const t = getI18n(c)
  return c.json(createSuccessResponse(null, t('script_deleted')))
}

/**
 * 使用剧本（增加使用次数）
 */
export const useScriptHandler = async (c: Context<{ Bindings: Env }>) => {
  const scriptId = c.req.param('id')

  if (!scriptId) {
    throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
  }

  const userId = await resolveLocalUserId(c)
  const script = await getScriptById(c.env, scriptId)

  if (!script) {
    throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId })
  }

  await incrementScriptUsage(c.env, scriptId)
  await createScriptUsage(c.env, {
    userId,
    scriptId
  })

  const t = getI18n(c)
  return c.json(createSuccessResponse(null, t('script_usage_updated')))
}

/**
 * 为剧本评分
 */
export const rateScriptHandler = async (c: Context<{ Bindings: Env }>) => {
  const scriptId = c.req.param('id')
  const { rating } = c.req.valid('json')

  if (!scriptId) {
    throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
  }

  const userId = await resolveLocalUserId(c)
  const script = await getScriptById(c.env, scriptId)

  if (!script) {
    throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId })
  }

  await updateScriptRating(c.env, scriptId)
  const t = getI18n(c)
  return c.json(createSuccessResponse(null, t('script_rating_success')))
}

/**
 * 购买剧本
 */
export const purchaseScriptHandler = async (c: Context<{ Bindings: Env }>) => {
  const scriptId = c.req.param('id')

  if (!scriptId) {
    throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'scriptId' })
  }

  const userId = await resolveLocalUserId(c)
  const [script, existingPurchase] = await Promise.all([
    getScriptById(c.env, scriptId),
    checkScriptPurchase(c.env, userId, scriptId)
  ])

  if (!script) {
    throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId })
  }

  if (existingPurchase) {
    throwBusinessError(ErrorCode.SCRIPT_ALREADY_PURCHASED, { scriptId })
  }

  if (script.pointsCost <= 0) {
    const [purchase] = await createScriptPurchase(c.env, {
      userId,
      scriptId,
      pointsCost: 0
    })

    const t = getI18n(c)
    return c.json(createSuccessResponse(
      {
        purchase,
        script: {
          id: script.id,
          title: script.title,
          description: script.description,
          coverImage: script.coverImage
        }
      },
      t('script_free_success')
    ))
  }

  const purchaseResult = await purchaseScriptWithPoints(c.env, {
    userId,
    scriptId,
    pointsCost: script.pointsCost,
    scriptTitle: script.title
  })

  if (!purchaseResult.success) {
    if (purchaseResult.errorCode === 'INSUFFICIENT_POINTS' && purchaseResult.errorData) {
      return c.json(
        {
          success: false,
          error: getErrorMessage(ErrorCode.INSUFFICIENT_POINTS, c.get('language'), {
            required: purchaseResult.errorData.required,
            available: purchaseResult.errorData.available
          }),
          code: ErrorCode.INSUFFICIENT_POINTS,
          data: {
            requiredPoints: purchaseResult.errorData.required,
            availablePoints: purchaseResult.errorData.available,
            remainingPoints: purchaseResult.remainingPoints
          }
        },
        402
      )
    } else if (purchaseResult.errorCode === 'USER_POINTS_NOT_FOUND') {
      throwBusinessError(ErrorCode.USER_NOT_FOUND)
    } else {
      throwBusinessError(ErrorCode.SCRIPT_PURCHASE_FAILED, {
        scriptId,
        reason: purchaseResult.error
      })
    }
  }

  const t = getI18n(c)
  return c.json(createSuccessResponse(
    {
      purchase: purchaseResult.purchase,
      script: {
        id: script.id,
        title: script.title,
        description: script.description,
        coverImage: script.coverImage
      },
      remainingPoints: purchaseResult.remainingPoints
    },
    t('script_purchase_success')
  ))
}

/**
 * 检查剧本购买状态
 */
export const checkScriptPurchaseHandler = async (c: Context<{ Bindings: Env }>) => {
  const scriptId = c.req.param('id')

  if (!scriptId) {
    throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'scriptId' })
  }

  const userId = await resolveLocalUserId(c)
  const purchase = await checkScriptPurchase(c.env, userId, scriptId)

  return c.json(createSuccessResponse({
    isPurchased: !!purchase,
    purchase: purchase || null
  }))
}

/**
 * 下载剧本内容
 */
export const downloadScriptHandler = async (c: Context<{ Bindings: Env }>) => {
  const scriptId = c.req.param('id')

  if (!scriptId) {
    throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'scriptId' })
  }

  const userId = await resolveLocalUserId(c)
  const purchase = await checkScriptPurchase(c.env, userId, scriptId)

  if (!purchase) {
    throwBusinessError(ErrorCode.SCRIPT_ACCESS_DENIED, {
      scriptId,
      reason: 'Script not purchased'
    })
  }

  const script = await getScriptById(c.env, scriptId)
  if (!script) {
    throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId })
  }

  await markScriptDownloaded(c.env, userId, scriptId)
  const t = getI18n(c)
  return c.json(createSuccessResponse(
    {
      id: script.id,
      title: script.title,
      content: script.content,
      audioUrl: script.audioUrl
    },
    t('script_download_success')
  ))
}