import type { Env } from '@/types/env'
import type { <PERSON><PERSON><PERSON>, ScriptUsage } from '@/lib/db/schema'
import {
  getPublicScriptsRepo,
  getScriptByIdRepo,
  createScriptRepo,
  updateScriptByIdRepo,
  deleteScriptByIdRepo,
  incrementScriptUsageRepo,
  getScriptCategoriesRepo,
  createScriptUsageRepo,
  getUserScriptUsageHistoryRepo,
  updateScriptRatingRepo
} from '../repositories/script.repository'

/**
 * 获取公开剧本列表（分页）
 */
export async function getPublicScripts(
  env: Env,
  options: {
    limit?: number
    offset?: number
    category?: string
    search?: string
    sortBy?: 'latest' | 'popular' | 'rating'
  } = {}
): Promise<Script[]> {
  return getPublicScriptsRepo(env, options)
}

/**
 * 根据ID获取剧本详情
 */
export async function getScriptById(env: Env, id: string): Promise<Script | null> {
  return getScriptByIdRepo(env, id)
}

/**
 * 创建新剧本
 */
export async function createScript(
  env: Env,
  data: {
    title: string
    description: string
    coverImage: string
    duration: string
    tags?: string[]
    category?: string
    content?: any
    audioUrl?: string
    totalDuration?: number
    stageCount?: number
    isPublic?: boolean
    isActive?: boolean
    isPremium?: boolean
    pointsCost?: number
    createdBy?: string
  }
): Promise<Script[]> {
  return createScriptRepo(env, data)
}

/**
 * 更新剧本
 */
export async function updateScriptById(
  env: Env,
  id: string,
  data: Partial<{
    title: string
    description: string
    coverImage: string
    duration: string
    tags: string[]
    category: string
    content: any
    audioUrl: string
    totalDuration: number
    stageCount: number
    isPublic: boolean
    isActive: boolean
    isPremium: boolean
    pointsCost: number
  }>
): Promise<Script[]> {
  return updateScriptByIdRepo(env, id, data)
}

/**
 * 删除剧本（软删除）
 */
export async function deleteScriptById(env: Env, id: string): Promise<Script[]> {
  return deleteScriptByIdRepo(env, id)
}

/**
 * 增加剧本使用次数
 */
export async function incrementScriptUsage(env: Env, scriptId: string): Promise<Script[]> {
  return incrementScriptUsageRepo(env, scriptId)
}

/**
 * 获取剧本分类列表
 */
export async function getScriptCategories(
  env: Env
): Promise<Array<{ category: string; count: number }>> {
  return getScriptCategoriesRepo(env)
}

/**
 * 创建剧本使用记录
 */
export async function createScriptUsage(
  env: Env,
  data: {
    userId: string
    scriptId: string
    chatId?: string
    duration?: number
    rating?: number
    feedback?: string
  }
): Promise<ScriptUsage[]> {
  return createScriptUsageRepo(env, data)
}

/**
 * 获取用户的剧本使用历史
 */
export async function getUserScriptUsageHistory(
  env: Env,
  userId: string,
  options: {
    limit?: number
    offset?: number
  } = {}
): Promise<Array<{ usage: ScriptUsage; script: Script | null }>> {
  return getUserScriptUsageHistoryRepo(env, userId, options)
}

/**
 * 更新剧本评分
 */
export async function updateScriptRating(env: Env, scriptId: string): Promise<Script[] | null> {
  return updateScriptRatingRepo(env, scriptId)
}