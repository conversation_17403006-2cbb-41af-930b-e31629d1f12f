import type { Env } from '@/types/env'
import type { ScriptPurchase } from '@/lib/db/schema'
import {
  checkScriptPurchaseRepo,
  createScriptPurchaseRepo,
  getUserPurchasedScriptIdsRepo,
  markScriptDownloadedRepo,
  purchaseScriptWithPointsRepo
} from '../repositories/script-purchase.repository'

/**
 * 检查用户是否已购买剧本
 */
export async function checkScriptPurchase(
  env: Env,
  userId: string,
  scriptId: string
): Promise<ScriptPurchase | null> {
  return checkScriptPurchaseRepo(env, userId, scriptId)
}

/**
 * 创建剧本购买记录
 */
export async function createScriptPurchase(
  env: Env,
  data: {
    userId: string
    scriptId: string
    pointsCost: number
    transactionId?: string
  }
): Promise<ScriptPurchase[]> {
  return createScriptPurchaseRepo(env, data)
}

/**
 * 获取用户已购买的剧本ID列表
 */
export async function getUserPurchasedScriptIds(
  env: Env,
  userId: string
): Promise<string[]> {
  return getUserPurchasedScriptIdsRepo(env, userId)
}

/**
 * 标记剧本内容已下载
 */
export async function markScriptDownloaded(
  env: Env,
  userId: string,
  scriptId: string
): Promise<boolean> {
  return markScriptDownloadedRepo(env, userId, scriptId)
}

/**
 * 购买剧本（包含积分交易）
 */
export async function purchaseScriptWithPoints(
  env: Env,
  data: {
    userId: string
    scriptId: string
    pointsCost: number
    scriptTitle: string
  }
): Promise<{
  success: boolean
  purchase?: ScriptPurchase
  remainingPoints?: number
  error?: string
  errorCode?: string
  errorData?: Record<string, any>
}> {
  return purchaseScriptWithPointsRepo(env, data)
}