import type { Env } from '@/types/env'
import { CacheManager } from './cache-manager'
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository';
import { getVoiceModelByModelId, getVoiceModelById } from '@/modules/app/voice/services/voice.service'
import { checkUserMembership } from '@/modules/app/membership/services/membership.service'
import type { CachedMembership, CachedVoiceModelsList, CachedUserPoints } from './types'

/**
 * 带缓存的数据库查询工具函数
 */

/**
 * 获取数据库用户ID（带缓存）
 * 先查缓存，缓存未命中时查询数据库并设置缓存
 */
export async function getCachedDbUserId(env: Env, supabaseUserId: string): Promise<string | null> {
  const cacheManager = new CacheManager(env.CACHE)

  try {
    // 1. 先查缓存
    const cacheResult = await cacheManager.getCachedDbUserId(supabaseUserId)

    if (cacheResult.success && cacheResult.cached && cacheResult.data) {
      console.log(`✅ 用户ID映射缓存命中: ${supabaseUserId} -> ${cacheResult.data}`)
      return cacheResult.data
    }

    // 2. 缓存未命中，查询数据库
    console.log(`🔍 用户ID映射缓存未命中，查询数据库: ${supabaseUserId}`)
    const dbUser = await getUserBySupabaseId(env, supabaseUserId)

    if (!dbUser?.id) {
      console.log(`❌ 用户不存在: ${supabaseUserId}`)
      return null
    }

    // 3. 设置缓存
    await cacheManager.setCachedDbUserId(supabaseUserId, dbUser.id)
    console.log(`💾 用户ID映射已缓存: ${supabaseUserId} -> ${dbUser.id}`)

    return dbUser.id
  } catch (error) {
    console.error('获取用户ID失败:', error)

    // 降级：直接查询数据库
    try {
      const dbUser = await getUserBySupabaseId(env, supabaseUserId)
      return dbUser?.id || null
    } catch (fallbackError) {
      console.error('降级查询也失败:', fallbackError)
      return null
    }
  }
}

/**
 * 获取真正的声音ID（带缓存）
 * 先查缓存，缓存未命中时查询数据库并设置缓存
 */
export async function getCachedRealVoiceId(env: Env, voiceModelId?: string): Promise<string> {
  // 默认声音ID
  const defaultVoiceId = 'TX3LPaxmHKxFdv7VOQHJ'

  if (!voiceModelId) {
    return defaultVoiceId
  }

  const cacheManager = new CacheManager(env.CACHE)

  try {
    // 1. 先查缓存
    const cacheResult = await cacheManager.getCachedRealVoiceId(voiceModelId)

    if (cacheResult.success && cacheResult.cached && cacheResult.data) {
      console.log(`✅ 声音模型缓存命中: ${voiceModelId} -> ${cacheResult.data}`)
      return cacheResult.data
    }

    // 2. 缓存未命中，查询数据库
    console.log(`🔍 声音模型缓存未命中，查询数据库: ${voiceModelId}`)

    let voiceModel = null
    let realVoiceId = defaultVoiceId

    // 检查是否为UUID格式（数据库ID）
    const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i

    if (uuidPattern.test(voiceModelId)) {
      // 是UUID，从数据库中查找声音模型
      voiceModel = await getVoiceModelById(env, voiceModelId)
    } else {
      // 不是UUID，可能是旧的modelId格式，尝试按modelId查找
      voiceModel = await getVoiceModelByModelId(env, voiceModelId)
    }

    if (voiceModel && voiceModel.modelId) {
      realVoiceId = voiceModel.modelId
      console.log(`🎵 找到声音模型: ${voiceModel.displayName} (${voiceModel.modelId})`)
    } else {
      // 如果没找到，直接使用传入的值作为voice_id（兼容性）
      realVoiceId = voiceModelId
      console.log(`⚠️ 未找到声音模型，直接使用voice_id: ${voiceModelId}`)
    }

    // 3. 设置缓存
    await cacheManager.setCachedRealVoiceId(voiceModelId, realVoiceId, voiceModel?.displayName)
    console.log(`💾 声音模型已缓存: ${voiceModelId} -> ${realVoiceId}`)

    return realVoiceId
  } catch (error) {
    console.error('获取声音模型失败:', error)

    // 降级：使用默认声音或原始输入
    const fallbackVoiceId = voiceModelId || defaultVoiceId
    console.log(`🔄 降级使用声音ID: ${fallbackVoiceId}`)
    return fallbackVoiceId
  }
}

/**
 * 获取会员状态（带缓存）
 * 先查缓存，缓存未命中时查询数据库并设置缓存
 */
export async function getCachedMembership(
  env: Env,
  dbUserId: string
): Promise<{
  isMember: boolean
  subscription: any | null
  plan: any | null
}> {
  const cacheManager = new CacheManager(env.CACHE)

  try {
    // 1. 先查缓存
    const cacheResult = await cacheManager.getCachedMembership(dbUserId)

    if (cacheResult.success && cacheResult.cached && cacheResult.data) {
      console.log(`✅ 会员状态缓存命中: ${dbUserId} -> isMember=${cacheResult.data.isMember}`)

      // 转换缓存数据格式，返回与checkUserMembership一致的结构
      return {
        isMember: cacheResult.data.isMember,
        subscription: cacheResult.data.subscriptionId
          ? {
              id: cacheResult.data.subscriptionId,
              planId: cacheResult.data.planId,
              endDate: cacheResult.data.endDate,
              status: cacheResult.data.status
            }
          : null,
        plan: null
      }
    }

    // 2. 缓存未命中，查询数据库
    console.log(`🔍 会员状态缓存未命中，查询数据库: ${dbUserId}`)
    const membershipInfo = await checkUserMembership(env, dbUserId)

    // 3. 设置缓存
    const cacheData: Omit<CachedMembership, 'timestamp'> = {
      isMember: membershipInfo.isMember,
      subscriptionId: membershipInfo.subscription?.id,
      planId: membershipInfo.subscription?.planId,
      endDate: membershipInfo.subscription?.endDate?.toISOString(),
      status: membershipInfo.subscription?.status
    }

    await cacheManager.setCachedMembership(dbUserId, cacheData)
    console.log(`💾 会员状态已缓存: ${dbUserId} -> isMember=${membershipInfo.isMember}`)

    return membershipInfo
  } catch (error) {
    console.error('获取会员状态失败:', error)

    // 降级：直接查询数据库
    try {
      const membershipInfo = await checkUserMembership(env, dbUserId)
      return membershipInfo
    } catch (fallbackError) {
      console.error('降级查询也失败:', fallbackError)
      return { isMember: false, subscription: null, plan: null }
    }
  }
}

/**
 * 清除用户相关的所有缓存
 * 当用户信息发生变化时调用
 */
export async function clearUserCaches(
  env: Env,
  supabaseUserId: string,
  dbUserId?: string
): Promise<void> {
  const cacheManager = new CacheManager(env.CACHE)

  try {
    // 清除用户ID映射缓存
    await cacheManager.clearUserMappingCache(supabaseUserId)

    // 如果有数据库用户ID，也清除积分缓存
    if (dbUserId) {
      await cacheManager.clearUserPointsCache(dbUserId)
    }

    console.log(`🗑️ 已清除用户缓存: ${supabaseUserId}`)
  } catch (error) {
    console.error('清除用户缓存失败:', error)
  }
}

/**
 * 清除声音模型缓存
 * 当声音模型更新时调用
 */
export async function clearVoiceModelCache(env: Env, voiceModelId: string): Promise<void> {
  const cacheManager = new CacheManager(env.CACHE)

  try {
    await cacheManager.clearVoiceModelCache(voiceModelId)
    console.log(`🗑️ 已清除声音模型缓存: ${voiceModelId}`)
  } catch (error) {
    console.error('清除声音模型缓存失败:', error)
  }
}

/**
 * 更新用户积分缓存
 * 当积分发生变化时调用（重要：积分消费后必须调用）
 * 性能优于清除缓存，直接更新为正确的数据
 */
export async function updateUserPointsCache(
  env: Env,
  dbUserId: string,
  newRemainingPoints: number,
  pointsConsumed: number
): Promise<void> {
  const cacheManager = new CacheManager(env.CACHE)

  try {
    // 1. 尝试获取当前缓存
    const cacheResult = await cacheManager.getCachedUserPoints(dbUserId)

    if (cacheResult.success && cacheResult.cached && cacheResult.data) {
      // 2. 缓存存在，智能更新
      const currentCache = cacheResult.data
      const updatedCache: Omit<CachedUserPoints, 'timestamp'> = {
        totalPoints: currentCache.totalPoints, // 总积分不变
        usedPoints: currentCache.usedPoints + pointsConsumed, // 增加已使用积分
        remainingPoints: newRemainingPoints // 更新剩余积分
      }

      await cacheManager.setCachedUserPoints(dbUserId, updatedCache)
      console.log(
        `💾 积分缓存已更新: ${dbUserId}, 剩余: ${currentCache.remainingPoints} -> ${newRemainingPoints}`
      )
    } else {
      // 3. 缓存不存在，清除确保下次重新缓存
      await cacheManager.clearUserPointsCache(dbUserId)
      console.log(`🗑️ 积分缓存不存在，清除确保下次重新缓存: ${dbUserId}`)
    }
  } catch (error) {
    console.error('更新用户积分缓存失败，降级为清除缓存:', error)
    // 降级：清除缓存
    try {
      await cacheManager.clearUserPointsCache(dbUserId)
    } catch (clearError) {
      console.error('降级清除缓存也失败:', clearError)
    }
  }
}

/**
 * 清除用户积分缓存
 * 当积分发生变化时调用（建议使用updateUserPointsCache代替）
 */
export async function clearUserPointsCache(env: Env, dbUserId: string): Promise<void> {
  const cacheManager = new CacheManager(env.CACHE)

  try {
    await cacheManager.clearUserPointsCache(dbUserId)
    console.log(`🗑️ 已清除用户积分缓存: ${dbUserId}`)
  } catch (error) {
    console.error('清除用户积分缓存失败:', error)
  }
}

/**
 * 清除会员相关的所有缓存
 * 当订阅状态发生变化时调用
 */
export async function clearMembershipCaches(env: Env, dbUserId: string): Promise<void> {
  const cacheManager = new CacheManager(env.CACHE)

  try {
    // 清除会员状态缓存
    await cacheManager.clearMembershipCache(dbUserId)

    // 清除积分缓存（会员状态变化可能影响积分）
    await cacheManager.clearUserPointsCache(dbUserId)

    console.log(`🗑️ 已清除会员相关缓存: ${dbUserId}`)
  } catch (error) {
    console.error('清除会员缓存失败:', error)
  }
}

/**
 * 获取声音模型列表（带缓存）
 * 通用函数，支持不同的查询方式
 */
export async function getCachedVoiceModelsList(
  env: Env,
  queryFn: () => Promise<any[]>,
  cacheType: 'all' | 'gender' | 'withSamples',
  filter?: string
): Promise<any[]> {
  const cacheManager = new CacheManager(env.CACHE)
  const cacheKey = CacheManager.generateVoiceModelsListCacheKey(cacheType, filter)

  try {
    // 1. 先查缓存
    const cacheResult = await cacheManager.getCachedVoiceModelsList(cacheKey)

    if (cacheResult.success && cacheResult.cached && cacheResult.data) {
      console.log(
        `✅ 声音模型列表缓存命中: ${cacheType}${filter ? `:${filter}` : ''}, 数量: ${
          cacheResult.data.models.length
        }`
      )
      return cacheResult.data.models
    }

    // 2. 缓存未命中，执行数据库查询
    console.log(`🔍 声音模型列表缓存未命中，查询数据库: ${cacheType}${filter ? `:${filter}` : ''}`)
    const models = await queryFn()

    // 3. 设置缓存
    const cacheData: Omit<CachedVoiceModelsList, 'timestamp'> = {
      models,
      type: cacheType,
      filter
    }

    await cacheManager.setCachedVoiceModelsList(cacheKey, cacheData)
    console.log(
      `💾 声音模型列表已缓存: ${cacheType}${filter ? `:${filter}` : ''}, 数量: ${models.length}`
    )

    return models
  } catch (error) {
    console.error('获取声音模型列表失败:', error)
    // 降级：直接执行查询函数
    try {
      return await queryFn()
    } catch (fallbackError) {
      console.error('降级查询声音模型列表也失败:', fallbackError)
      return []
    }
  }
}

/**
 * 清除所有声音模型列表缓存
 * 当声音模型数据发生变化时调用（如：创建、更新、删除声音模型）
 */
export async function clearAllVoiceModelsListCache(env: Env): Promise<void> {
  const cacheManager = new CacheManager(env.CACHE)

  try {
    await cacheManager.clearAllVoiceModelsListCache()
    console.log('✅ 声音模型列表缓存清除请求已发送')
  } catch (error) {
    console.error('清除声音模型列表缓存失败:', error)
  }
}

/**
 * 获取缓存统计信息
 */
export async function getCacheStats(env: Env) {
  const cacheManager = new CacheManager(env.CACHE)
  return cacheManager.getStats()
}

/**
 * 获取模板数据（带缓存）
 * 先查缓存，缓存未命中时查询数据库并设置缓存
 */
export async function getCachedTemplateById(env: Env, templateId: string): Promise<any | null> {
  const cacheManager = new CacheManager(env.CACHE)

  try {
    // 1. 先查缓存
    const cacheResult = await cacheManager.getCachedTemplate(templateId)

    if (cacheResult.success && cacheResult.cached && cacheResult.data) {
      console.log(`✅ 模板缓存命中: ${templateId} -> ${cacheResult.data.name}`)

      // 转换缓存数据为数据库格式
      return {
        id: cacheResult.data.id,
        name: cacheResult.data.name,
        prompt: cacheResult.data.prompt,
        pointsCost: cacheResult.data.pointsCost,
        isActive: cacheResult.data.isActive,
        category: cacheResult.data.category,
        tags: cacheResult.data.tags,
        previewImage: cacheResult.data.imageUrl // 恢复为previewImage字段名
      }
    }

    // 2. 缓存未命中，查询数据库
    console.log(`🔍 模板缓存未命中，查询数据库: ${templateId}`)
    const { getTemplateById } = await import('@/lib/db/queries/template')
    const template = await getTemplateById(env, templateId)

    if (!template) {
      console.log(`❌ 模板不存在: ${templateId}`)
      return null
    }

    // 3. 设置缓存
    const cacheData = {
      id: template.id,
      name: template.name,
      prompt: template.prompt,
      pointsCost: template.pointsCost,
      isActive: template.isActive,
      category: template.category || undefined,
      tags: Array.isArray(template.tags) ? template.tags : undefined,
      imageUrl: template.previewImage || undefined // 使用previewImage字段
    }

    await cacheManager.setCachedTemplate(templateId, cacheData)
    console.log(`💾 模板已缓存: ${templateId} -> ${template.name}`)

    return template
  } catch (error) {
    console.error('获取模板失败:', error)

    // 降级：直接查询数据库
    try {
      const { getTemplateById } = await import('@/lib/db/queries/template')
      const template = await getTemplateById(env, templateId)
      return template
    } catch (fallbackError) {
      console.error('降级查询也失败:', fallbackError)
      return null
    }
  }
}

/**
 * 清除模板缓存
 * 当模板更新时调用
 */
export async function clearTemplateCaches(env: Env, templateId: string): Promise<void> {
  const cacheManager = new CacheManager(env.CACHE)

  try {
    await cacheManager.clearTemplateCache(templateId)
    console.log(`🗑️ 已清除模板缓存: ${templateId}`)
  } catch (error) {
    console.error('清除模板缓存失败:', error)
  }
}
