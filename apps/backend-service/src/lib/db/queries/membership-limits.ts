import { getSupabase } from './base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types'
import type { Env } from '@/types/env'
import { getSystemConfig } from './system-config'

/**
 * 会员功能权限接口
 */
export interface MembershipFeatures {
  maxCharacters?: number // 最大角色数目
  canCreatePublicCharacters?: boolean
  canUseCustomVoices?: boolean
  canAccessPremiumTemplates?: boolean
}

/**
 * 默认的免费用户权限
 */
export const DEFAULT_FREE_FEATURES: MembershipFeatures = {
  maxCharacters: 1, // 免费用户最多1个角色（作为后备值）
  canCreatePublicCharacters: false,
  canUseCustomVoices: false,
  canAccessPremiumTemplates: false
}

/**
 * 获取免费用户的角色创建限制
 * 从SystemConfig表的FREE_CHARACTER_LIMIT字段获取
 */
async function getFreeUserCharacterLimit(env: Env): Promise<number> {
  try {
    const limitValue = await getSystemConfig(env, 'FREE_CHARACTER_LIMIT')
    if (limitValue) {
      const limit = typeof limitValue === 'number' ? limitValue : Number.parseInt(limitValue, 10)
      if (!isNaN(limit) && limit > 0) {
        console.log('📋 [FREE-LIMIT] 从SystemConfig获取免费用户角色限制:', limit)
        return limit
      }
    }

    console.log(
      '📋 [FREE-LIMIT] SystemConfig中无有效配置，使用默认值:',
      DEFAULT_FREE_FEATURES.maxCharacters
    )
    return DEFAULT_FREE_FEATURES.maxCharacters!
  } catch (error) {
    console.error('❌ [FREE-LIMIT] 获取免费用户角色限制失败:', error)
    return DEFAULT_FREE_FEATURES.maxCharacters!
  }
}

/**
 * 获取用户的会员功能权限
 */
export async function getUserMembershipFeatures(
  env: Env,
  userId: string
): Promise<MembershipFeatures> {
  const supabase = getSupabase(env)

  console.log('🔍 [MEMBERSHIP] 获取用户会员权限，用户ID:', userId)

  try {
    // 查询用户当前有效的订阅
    const subscriptionResult = await supabase
      .from('UserSubscription')
      .select(
        `
        id,
        status,
        end_date,
        plan:MembershipPlan(
          id,
          name,
          features
        )
      `
      )
      .eq('user_id', userId)
      .eq('status', 'active')
      .gt('end_date', new Date().toISOString())
      .order('end_date', { ascending: false })
      .limit(1)

    const { data: subscriptions, error } = handleSupabaseResult(subscriptionResult)

    if (error) {
      console.error('❌ [MEMBERSHIP] 查询订阅失败:', error)
      throw error
    }

    if (!subscriptions || subscriptions.length === 0) {
      console.log('📋 [MEMBERSHIP] 用户无有效订阅，使用免费权限')
      // 免费用户：从SystemConfig获取角色限制
      const freeCharacterLimit = await getFreeUserCharacterLimit(env)
      return {
        ...DEFAULT_FREE_FEATURES,
        maxCharacters: freeCharacterLimit
      }
    }

    const subscription = subscriptions[0]
    const plan = subscription.plan

    if (!plan || !plan.features) {
      console.log('📋 [MEMBERSHIP] 订阅计划无功能配置，使用免费权限')
      const freeCharacterLimit = await getFreeUserCharacterLimit(env)
      return {
        ...DEFAULT_FREE_FEATURES,
        maxCharacters: freeCharacterLimit
      }
    }

    console.log('✅ [MEMBERSHIP] 用户会员计划:', plan.name, '功能配置:', plan.features)

    // 会员用户：合并默认权限和计划权限
    const features: MembershipFeatures = {
      ...DEFAULT_FREE_FEATURES,
      ...plan.features
    }

    return features
  } catch (error) {
    console.error('❌ [MEMBERSHIP] 获取用户会员权限失败:', error)
    // 出错时返回免费权限，保证系统正常运行
    const freeCharacterLimit = await getFreeUserCharacterLimit(env)
    return {
      ...DEFAULT_FREE_FEATURES,
      maxCharacters: freeCharacterLimit
    }
  }
}

/**
 * 检查用户是否可以创建更多角色
 */
export async function checkUserCanCreateCharacter(
  env: Env,
  userId: string
): Promise<{
  canCreate: boolean
  reason?: string
  currentCount?: number
  maxAllowed?: number
}> {
  const supabase = getSupabase(env)

  console.log('🔍 [CHARACTER-LIMIT] 检查用户角色创建权限，用户ID:', userId)

  try {
    // 1. 获取用户会员权限
    const features = await getUserMembershipFeatures(env, userId)
    const maxCharacters = features.maxCharacters || DEFAULT_FREE_FEATURES.maxCharacters!

    // 2. 查询用户当前角色数量
    const characterCountResult = await supabase
      .from('Character')
      .select('id', { count: 'exact' })
      .eq('user_id', userId)
      .eq('is_active', true)

    const { count: currentCount, error } = characterCountResult

    if (error) {
      console.error('❌ [CHARACTER-LIMIT] 查询角色数量失败:', error)
      throw error
    }

    const characterCount = currentCount || 0

    // 3. 检查用户是否有有效订阅（判断真实会员状态）
    const subscriptionResult = await supabase
      .from('UserSubscription')
      .select('id, status, end_date')
      .eq('user_id', userId)
      .eq('status', 'active')
      .gt('end_date', new Date().toISOString())
      .limit(1)

    const { data: subscriptions } = handleSupabaseResult(subscriptionResult)
    const isMember = subscriptions && subscriptions.length > 0

    console.log('📊 [CHARACTER-LIMIT] 角色数量检查:', {
      userId,
      currentCount: characterCount,
      maxAllowed: maxCharacters,
      canCreate: characterCount < maxCharacters,
      isMember: isMember
    })

    if (characterCount >= maxCharacters) {
      const reason = isMember
        ? `您已达到当前会员套餐的角色创建上限（${maxCharacters}个）。请联系管理员升级套餐。`
        : `免费用户最多只能创建${maxCharacters}个角色。升级会员可创建更多角色。`

      return {
        canCreate: false,
        reason,
        currentCount: characterCount,
        maxAllowed: maxCharacters
      }
    }

    return {
      canCreate: true,
      currentCount: characterCount,
      maxAllowed: maxCharacters
    }
  } catch (error) {
    console.error('❌ [CHARACTER-LIMIT] 检查角色创建权限失败:', error)
    // 出错时允许创建，避免影响用户体验
    return {
      canCreate: true,
      reason: '权限检查失败，已允许创建'
    }
  }
}

/**
 * 获取用户角色使用统计
 */
export async function getUserCharacterStats(
  env: Env,
  userId: string
): Promise<{
  currentCount: number
  maxAllowed: number
  canCreateMore: boolean
  membershipPlan?: string
}> {
  console.log('📊 [CHARACTER-STATS] 获取用户角色统计，用户ID:', userId)

  try {
    const [features, checkResult] = await Promise.all([
      getUserMembershipFeatures(env, userId),
      checkUserCanCreateCharacter(env, userId)
    ])

    return {
      currentCount: checkResult.currentCount || 0,
      maxAllowed:
        checkResult.maxAllowed || features.maxCharacters || DEFAULT_FREE_FEATURES.maxCharacters!,
      canCreateMore: checkResult.canCreate,
      membershipPlan:
        features.maxCharacters === DEFAULT_FREE_FEATURES.maxCharacters ? '免费用户' : '会员用户'
    }
  } catch (error) {
    console.error('❌ [CHARACTER-STATS] 获取角色统计失败:', error)
    return {
      currentCount: 0,
      maxAllowed: DEFAULT_FREE_FEATURES.maxCharacters!,
      canCreateMore: true,
      membershipPlan: '免费用户'
    }
  }
}
