import type { Env } from '@/types/env'
import { uploadToR2, getR2ConfigFromEnv, IMAGE_UPLOAD_OPTIONS } from '@/lib/utils/r2-upload'

// RunPod API 请求接口
interface RunPodTaskRequest {
  input: {
    workflow_api: {
      [key: string]: any
    }
    images: Array<{
      name: string
      image: string // base64 without header
    }>
  }
}

// RunPod API 响应接口
interface RunPodTaskResponse {
  id: string
  status: string
}

// RunPod 任务状态接口
interface RunPodTaskStatus {
  id: string
  status: 'IN_QUEUE' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED'
  output?: {
    message?: string // base64 without header
  }
  error?: string
}

// 图片生成参数接口
interface ImageGenerationParams {
  prompt: string
  characterAvatar?: string
  width?: number
  height?: number
  seed?: number
}

// 进度回调接口
type ProgressCallback = (progress: number, message: string) => Promise<void>

// 环境配置接口
interface EnvironmentConfig {
  negativePrompt: string
}

/**
 * RunPod 图片服务
 * 提供统一的 RunPod 图片生成功能
 */
export class RunPodImageService {
  private config: EnvironmentConfig

  constructor(private env: Env) {
    this.config = this.getEnvironmentConfig()
  }

  /**
   * 根据环境变量获取配置
   */
  private getEnvironmentConfig(): EnvironmentConfig {
    const workerEnv = this.env.WORKER_ENV || 'dev'

    console.log('🔍 [RunPod服务] 环境检查:')
    console.log('🔍 [RunPod服务] WORKER_ENV:', workerEnv)
    console.log('🔍 [RunPod服务] NODE_ENV:', this.env.NODE_ENV)
    console.log('🔍 [RunPod服务] RUNPOD_API_TOKEN 存在:', !!this.env.RUNPOD_API_TOKEN)
    console.log('🔍 [RunPod服务] RUNPOD_API_ENDPOINT 存在:', !!this.env.RUNPOD_API_ENDPOINT)

    if (this.env.RUNPOD_API_TOKEN) {
      console.log('🔍 [RunPod服务] RUNPOD_API_TOKEN 长度:', this.env.RUNPOD_API_TOKEN.length)
    }
    if (this.env.RUNPOD_API_ENDPOINT) {
      console.log('🔍 [RunPod服务] RUNPOD_API_ENDPOINT:', this.env.RUNPOD_API_ENDPOINT)
    }

    switch (workerEnv) {
      case 'global':
        console.log('🌍 [RunPod服务] 使用全局环境配置')
        return this.getGlobalConfig()
      case 'dev':
      default:
        console.log('🛠️ [RunPod服务] 使用开发环境配置')
        return this.getDevConfig()
    }
  }

  /**
   * 开发环境配置
   */
  private getDevConfig(): EnvironmentConfig {
    return {
      negativePrompt:
        'embedding:Stable_Yogis_Illustrious_Negatives-neg, nipple, genitals, penis, reproductive organs'
    }
  }

  /**
   * 全局环境配置 - 生产环境优化
   */
  private getGlobalConfig(): EnvironmentConfig {
    return {
      negativePrompt:
        'embedding:Stable_Yogis_Illustrious_Negatives-neg, low quality, blurry, distorted, watermark, signature, text'
    }
  }

  /**
   * 生成图片（包含换脸功能）
   */
  async generateImage(
    params: ImageGenerationParams,
    progressCallback?: ProgressCallback
  ): Promise<string> {
    console.log('🎨 [RunPod服务] 开始生成图片')

    // 构建工作流
    const workflowApi = this.buildWorkflowApi(params)

    const request: RunPodTaskRequest = {
      input: {
        workflow_api: workflowApi,
        images: []
      }
    }

    // 如果有角色头像，添加到请求中
    if (params.characterAvatar) {
      const avatarBase64 = await this.convertImageToBase64(params.characterAvatar)
      request.input.images.push({
        name: 'image.png',
        image: avatarBase64
      })
    }

    console.log('📋 [RunPod服务] 发送任务请求')

    // 检查必要的环境变量
    if (!this.env.RUNPOD_API_TOKEN) {
      throw new Error('RUNPOD_API_TOKEN 环境变量未设置')
    }
    if (!this.env.RUNPOD_API_ENDPOINT) {
      throw new Error('RUNPOD_API_ENDPOINT 环境变量未设置')
    }

    // 提交任务
    const apiUrl = `https://api.runpod.ai/v2/${this.env.RUNPOD_API_ENDPOINT}/run`
    console.log('📋 [RunPod服务] 请求 URL:', apiUrl)
    console.log('📋 [RunPod服务] 请求体大小:', JSON.stringify(request).length, '字符')

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.env.RUNPOD_API_TOKEN}`
      },
      body: JSON.stringify(request),
      signal: AbortSignal.timeout(60000)
    })

    console.log('📋 [RunPod服务] API 响应状态:', response.status)
    console.log('📋 [RunPod服务] API 响应头:', Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ [RunPod服务] API 错误响应:', errorText)
      throw new Error(`RunPod API请求失败: ${response.status} ${errorText}`)
    }

    const result: RunPodTaskResponse = await response.json()
    console.log('✅ [RunPod服务] 任务启动:', result.id)

    // 等待任务完成
    return await this.waitForTaskCompletion(result.id, progressCallback)
  }

  /**
   * 构建 RunPod 工作流 API
   */
  private buildWorkflowApi(params: ImageGenerationParams): Record<string, any> {
    return {
      '3': {
        inputs: {
          seed: params.seed || this.generateRandomSeed(),
          steps: 27,
          cfg: 4.21,
          sampler_name: 'dpmpp_2m_sde',
          scheduler: 'karras',
          denoise: 1,
          model: ['221', 0],
          positive: ['6', 0],
          negative: ['7', 0],
          latent_image: ['5', 0]
        },
        class_type: 'KSampler',
        _meta: {
          title: 'K采样器'
        }
      },
      '4': {
        inputs: {
          ckpt_name: 'realismByStableYogi_v50FP16.safetensors'
        },
        class_type: 'CheckpointLoaderSimple',
        _meta: {
          title: 'Checkpoint加载器（简易）'
        }
      },
      '5': {
        inputs: {
          width: params.width || 1024,
          height: params.height || 1440,
          batch_size: 1
        },
        class_type: 'EmptyLatentImage',
        _meta: {
          title: '空Latent图像'
        }
      },
      '6': {
        inputs: {
          text: `embedding:Stable_Yogis_PDXL_Positives, Stable_Yogis_PDXL_Positives, solo. natural skin texture, ${params.prompt}`,
          clip: ['4', 1]
        },
        class_type: 'CLIPTextEncode',
        _meta: {
          title: 'CLIP文本编码'
        }
      },
      '7': {
        inputs: {
          text: this.config.negativePrompt,
          clip: ['4', 1]
        },
        class_type: 'CLIPTextEncode',
        _meta: {
          title: 'CLIP文本编码'
        }
      },
      '8': {
        inputs: {
          samples: ['3', 0],
          vae: ['4', 2]
        },
        class_type: 'VAEDecode',
        _meta: {
          title: 'VAE解码'
        }
      },
      '220': {
        inputs: {
          lora_name: 'Realism_Lora_By_Stable_yogi_SDXL8.1.safetensors',
          strength_model: 0.7500000000000001,
          model: ['4', 0]
        },
        class_type: 'LoraLoaderModelOnly',
        _meta: {
          title: 'LoRA加载器（仅模型）'
        }
      },
      '221': {
        inputs: {
          lora_name: 'Super_Skin_Detailer_By_Stable_Yogi_PD0_V1.safetensors',
          strength_model: 0.7000000000000002,
          model: ['220', 0]
        },
        class_type: 'LoraLoaderModelOnly',
        _meta: {
          title: 'LoRA加载器（仅模型）'
        }
      },
      '225': {
        inputs: {
          enabled: true,
          swap_model: 'inswapper_128.onnx',
          facedetection: 'retinaface_resnet50',
          face_restore_model: 'codeformer-v0.1.0.pth',
          face_restore_visibility: 1,
          codeformer_weight: 0.9000000000000001,
          detect_gender_input: 'no',
          detect_gender_source: 'no',
          input_faces_index: '0',
          source_faces_index: '0',
          console_log_level: 1,
          input_image: ['8', 0],
          source_image: ['226', 0]
        },
        class_type: 'ReActorFaceSwap',
        _meta: {
          title: 'ReActor 🌌 Fast Face Swap'
        }
      },
      '226': {
        inputs: {
          image: 'image.png'
        },
        class_type: 'LoadImage',
        _meta: {
          title: '加载图像'
        }
      },
      '230': {
        inputs: {
          filename_prefix: 'ComfyUI',
          images: ['225', 0]
        },
        class_type: 'SaveImage',
        _meta: {
          title: '保存图像'
        }
      }
    }
  }

  /**
   * 等待 RunPod 任务完成
   */
  private async waitForTaskCompletion(
    taskId: string,
    progressCallback?: ProgressCallback
  ): Promise<string> {
    const maxWaitTime = 600000 // 10分钟
    const pollInterval = 3000 // 3秒
    const startTime = Date.now()

    console.log('⏳ [RunPod服务] 轮询状态:', taskId)

    let progressStep = 10 // 从10%开始
    let consecutiveErrors = 0
    const maxConsecutiveErrors = 5

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const statusUrl = `https://api.runpod.ai/v2/${this.env.RUNPOD_API_ENDPOINT}/status/${taskId}`
        const response = await fetch(statusUrl, {
          headers: {
            Authorization: `Bearer ${this.env.RUNPOD_API_TOKEN}`
          }
        })

        if (!response.ok) {
          consecutiveErrors++
          const errorText = await response.text()
          console.warn(
            `⚠️ [RunPod服务] 查询失败 ${consecutiveErrors}/${maxConsecutiveErrors}:`,
            response.status,
            errorText
          )

          if (consecutiveErrors >= maxConsecutiveErrors) {
            throw new Error('RunPod查询失败次数过多')
          }

          await new Promise(resolve => setTimeout(resolve, pollInterval))
          continue
        }

        consecutiveErrors = 0
        const task: RunPodTaskStatus = await response.json()

        console.log('📋 [RunPod服务] 任务状态:', task.status)

        // 更新进度
        if (task.status === 'IN_PROGRESS') {
          progressStep = Math.min(progressStep + 8, 75)
          if (progressCallback) {
            await progressCallback(progressStep, '正在处理...')
          }
        }

        // 检查完成状态
        if (task.status === 'COMPLETED') {
          const base64Message = task.output?.message
          if (!base64Message) {
            throw new Error('RunPod未返回图片数据')
          }

          // 将 base64 转换为可访问的 URL
          const imageUrl = await this.convertBase64ToUrl(base64Message)
          console.log('✅ [RunPod服务] 图片生成完成')
          return imageUrl
        }

        if (task.status === 'FAILED') {
          throw new Error(task.error || 'RunPod图片处理失败')
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      } catch (error) {
        consecutiveErrors++
        console.error(
          `❌ [RunPod服务] 轮询异常 ${consecutiveErrors}/${maxConsecutiveErrors}:`,
          error
        )

        if (consecutiveErrors >= maxConsecutiveErrors) {
          throw new Error('RunPod轮询异常过多')
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      }
    }

    // 超时
    throw new Error('RunPod等待超时')
  }

  /**
   * 将图片 URL 转换为 base64（不包含 data: 头）
   */
  private async convertImageToBase64(imageUrl: string): Promise<string> {
    try {
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`下载图片失败: ${response.status}`)
      }

      const buffer = await response.arrayBuffer()

      // 使用分块处理避免栈溢出
      const uint8Array = new Uint8Array(buffer)
      const chunkSize = 8192 // 8KB 分块
      let binaryString = ''

      for (let i = 0; i < uint8Array.length; i += chunkSize) {
        const chunk = uint8Array.subarray(i, i + chunkSize)
        binaryString += String.fromCharCode(...chunk)
      }

      const base64 = btoa(binaryString)
      console.log('✅ [RunPod服务] 图片转base64成功，大小:', buffer.byteLength, '字节')
      return base64
    } catch (error) {
      console.error('❌ [RunPod服务] 图片转base64失败:', error)
      throw new Error('图片转换失败')
    }
  }

  /**
   * 将 base64 转换为可访问的 URL（通过 R2 临时存储）
   */
  private async convertBase64ToUrl(base64: string): Promise<string> {
    try {
      console.log('🔄 [RunPod服务] 开始base64转URL，base64长度:', base64.length)

      // 将 base64 转换为 buffer
      const binaryString = atob(base64)
      const buffer = new Uint8Array(binaryString.length)
      for (let i = 0; i < binaryString.length; i++) {
        buffer[i] = binaryString.charCodeAt(i)
      }

      console.log('✅ [RunPod服务] base64解码完成，buffer大小:', buffer.length, '字节')

      // 获取 R2 配置
      const r2Config = getR2ConfigFromEnv(this.env)
      if (!r2Config) {
        throw new Error('R2 配置缺失')
      }

      // 生成临时文件名
      const tempFileName = `temp-runpod-${Date.now()}.png`

      // 上传到 R2
      const uploadResult = await uploadToR2(buffer.buffer, r2Config, {
        ...IMAGE_UPLOAD_OPTIONS,
        fileName: tempFileName,
        folder: 'temp-generated-images'
      })

      if (!uploadResult.success) {
        throw new Error(`R2上传失败: ${uploadResult.error}`)
      }

      console.log('✅ [RunPod服务] base64转URL成功:', uploadResult.url)
      return uploadResult.url!
    } catch (error) {
      console.error('❌ [RunPod服务] base64转URL失败:', error)
      throw new Error('图片处理失败')
    }
  }

  /**
   * 生成随机 seed
   */
  private generateRandomSeed(): number {
    return Math.floor(Math.random() * 9000000000000) + 1000000000000
  }

  /**
   * 下载图片并上传到 R2（用于获取永久链接）
   */
  async downloadAndUploadToR2(
    imageUrl: string,
    taskId: string,
    folderPrefix = 'generated-images'
  ): Promise<string> {
    try {
      console.log('📸 [RunPod服务-R2上传] 开始下载图片:', imageUrl)

      // 1. 下载图片
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`下载图片失败: ${response.status} ${response.statusText}`)
      }

      const imageBuffer = await response.arrayBuffer()
      console.log('📸 [RunPod服务-R2上传] 图片下载完成，大小:', imageBuffer.byteLength, '字节')

      // 2. 获取 R2 配置
      const r2Config = getR2ConfigFromEnv(this.env)
      if (!r2Config) {
        console.error('❌ [RunPod服务-R2上传] R2 配置缺失，使用原始 URL')
        return imageUrl
      }

      console.log('📸 [RunPod服务-R2上传] R2 配置获取成功')

      // 3. 生成文件名
      const timestamp = new Date().toISOString().split('T')[0]
      const uniqueId = taskId.split('-').pop() || 'unknown'
      const fileName = `runpod-image-${uniqueId}.png`

      // 4. 上传到 R2
      const uploadResult = await uploadToR2(imageBuffer, r2Config, {
        ...IMAGE_UPLOAD_OPTIONS,
        fileName,
        folder: `${folderPrefix}/${timestamp}`
      })

      if (!uploadResult.success) {
        console.error('❌ [RunPod服务-R2上传] 失败，使用原始 URL:', uploadResult.error)
        return imageUrl
      }

      console.log('✅ [RunPod服务-R2上传] 上传成功:', {
        originalUrl: imageUrl,
        r2Url: uploadResult.url,
        key: uploadResult.key,
        size: uploadResult.size
      })

      return uploadResult.url!
    } catch (error) {
      console.error('❌ [RunPod服务-R2上传] 异常，使用原始 URL:', error)
      return imageUrl
    }
  }
}

// 导出接口和类型供其他模块使用
export type { ImageGenerationParams, ProgressCallback }
