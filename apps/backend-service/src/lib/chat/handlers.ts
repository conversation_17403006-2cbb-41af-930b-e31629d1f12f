import { z } from 'zod'
import {
  getUserBySupabaseId,
  getUserProfile
} from '@/modules/app/user/repositories/user.repository';
import {
  getChatFromDB as getChatById,
  createChatInDB as saveChat,
  updateChatInDB as updateChatTitle,
  deleteChatFromDB as deleteChatById,
  getChatsByUserId
} from '@/modules/app/chat/repositories/chat.repository'
import { getMessagesByChatId } from '@/modules/app/chat/repositories/message.repository'
import { CharacterService } from '@/modules/app/characters'
import type { Env } from '@/types/env'
import { generateUUID } from '@/lib/utils'

// ==================== 验证模式 ====================

export const textPartSchema = z.object({
  text: z.string().min(1).max(2000),
  type: z.enum(['text'])
})

export const createChatSchema = z.object({
  title: z.string().min(1).max(100).optional(),
  characterId: z.string().uuid().optional()
})

export const sendMessageSchema = z.object({
  content: z.string().min(1).max(2000),
  parts: z.array(textPartSchema),
  attachments: z
    .array(
      z.object({
        url: z.string().url(),
        name: z.string().min(1).max(200),
        contentType: z.enum(['image/png', 'image/jpg', 'image/jpeg'])
      })
    )
    .optional()
})

export const updateChatSchema = z.object({
  title: z.string().min(1).max(100).optional()
})

// ==================== 聊天处理器 ====================

export class ChatHandlers {
  constructor(private env: Env) {}

  async getConversations(
    userId: string,
    options: {
      limit?: number
      startingAfter?: string | null
      endingBefore?: string | null
    }
  ) {
    const dbUser = await getUserBySupabaseId(this.env, userId)
    if (!dbUser) {
      throw new Error('用户数据不存在')
    }

    const { limit = 20, startingAfter, endingBefore } = options

    const result = await getChatsByUserId(this.env, {
      id: dbUser.id,
      limit,
      startingAfter: startingAfter ?? null,
      endingBefore: endingBefore ?? null
    })

    return {
      chats: result.chats,
      hasMore: result.hasMore,
      pagination: {
        limit,
        startingAfter: startingAfter || null,
        endingBefore: endingBefore || null
      }
    }
  }

  async createConversation(userId: string, data: z.infer<typeof createChatSchema>) {
    const dbUser = await getUserBySupabaseId(this.env, userId)
    if (!dbUser) {
      throw new Error('用户数据不存在')
    }

    // 验证角色权限
    if (data.characterId) {
      const characterService = new CharacterService()
      try {
        const character = await characterService.getCharacterById(this.env, data.characterId, userId)
        if (!character.isPublic && character.userId !== dbUser.id) {
          throw new Error('无权限使用该角色')
        }
      } catch (error) {
        throw new Error('指定的角色不存在或无权限访问')
      }
    }

    const chatId = generateUUID()
    await saveChat(this.env, {
      userId: dbUser.id,
      title: data.title || '新对话',
      characterId: data.characterId
    })

    const newChat = await getChatById(this.env, chatId)
    if (!newChat) {
      throw new Error('Chat not found')
    }
    return {
      id: newChat.id,
      title: newChat.title,
      characterId: newChat.characterId,
      createdAt: newChat.createdAt,
      updatedAt: newChat.updatedAt
    }
  }

  async getConversation(userId: string, chatId: string) {
    const dbUser = await getUserBySupabaseId(this.env, userId)
    if (!dbUser) {
      throw new Error('用户数据不存在')
    }

    const chatData = await getChatById(this.env, chatId)
    if (!chatData) {
      throw new Error('聊天不存在')
    }

    if (chatData.userId !== dbUser.id) {
      throw new Error('无权限访问该聊天')
    }

    const messages = await getMessagesByChatId(this.env, chatId)

    // 确保消息按正确顺序排列：先按时间，再按ID
    const sortedMessages = messages.sort((a, b) => {
      const timeCompare = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      if (timeCompare !== 0) return timeCompare
      return a.id.localeCompare(b.id) // ID作为第二排序条件
    })

    return {
      chat: {
        id: chatData.id,
        title: chatData.title,
        characterId: chatData.characterId,
        createdAt: chatData.createdAt,
        updatedAt: chatData.updatedAt
      },
      messages: sortedMessages.map(msg => {
        // 从 parts 中提取文本内容作为 content
        const parts = typeof msg.parts === 'string' ? JSON.parse(msg.parts) : msg.parts
        const content = parts
          .filter((part: any) => part.type === 'text')
          .map((part: any) => part.text)
          .join('')

        return {
          id: msg.id,
          role: msg.role,
          content, // 添加 content 字段
          parts: parts,
          attachments:
            typeof msg.attachments === 'string' ? JSON.parse(msg.attachments) : msg.attachments,
          createdAt: msg.createdAt
        }
      })
    }
  }

  async updateConversation(userId: string, chatId: string, data: z.infer<typeof updateChatSchema>) {
    const dbUser = await getUserBySupabaseId(this.env, userId)
    if (!dbUser) {
      throw new Error('用户数据不存在')
    }

    const chatData = await getChatById(this.env, chatId)
    if (!chatData) {
      throw new Error('聊天不存在')
    }

    if (chatData.userId !== dbUser.id) {
      throw new Error('无权限修改该聊天')
    }

    if (data.title) {
      await updateChatTitle(this.env, { userId: dbUser.id, chatId, title: data.title })
    }

    const updatedChat = await getChatById(this.env, chatId)
    if (!updatedChat) {
      throw new Error('Chat not found')
    }
    return {
      id: updatedChat.id,
      title: updatedChat.title,
      characterId: updatedChat.characterId,
      createdAt: updatedChat.createdAt,
      updatedAt: updatedChat.updatedAt
    }
  }

  async deleteConversation(userId: string, chatId: string) {
    const dbUser = await getUserBySupabaseId(this.env, userId)
    if (!dbUser) {
      throw new Error('用户数据不存在')
    }

    const chatData = await getChatById(this.env, chatId)
    if (!chatData) {
      throw new Error('聊天不存在')
    }

    if (chatData.userId !== dbUser.id) {
      throw new Error('无权限删除该聊天')
    }

    await deleteChatById(this.env,dbUser.id, chatId)
    return { success: true }
  }

  async validateChatAccess(
    userId: string,
    chatId: string,
    createIfNotExists = false,
    characterId?: string,
    userMessage?: { content: string; parts?: any[] }
  ) {
    const dbUser = await getUserBySupabaseId(this.env, userId)
    if (!dbUser) {
      throw new Error('用户数据不存在')
    }

    let chatData = await getChatById(this.env, chatId)

    if (!chatData) {
      if (createIfNotExists) {
        // 如果聊天不存在且允许创建，则创建新聊天
        const title = `新对话 ${new Date().toLocaleDateString()}`

        // // 如果有用户消息，尝试生成智能标题
        // if (userMessage) {
        //   try {
        //     title = await generateTitleFromUserMessage(this.env, { message: userMessage })
        //   } catch (error) {
        //     console.error('生成标题失败，使用默认标题:', error)
        //   }
        // }

        await saveChat(this.env, {
          userId: dbUser.id,
          title,
          characterId: characterId || undefined // 使用传入的角色ID
        })

        // 重新获取创建的聊天数据
        chatData = await getChatById(this.env, chatId)
        if (!chatData) {
          throw new Error('创建聊天失败')
        }
      } else {
        throw new Error('聊天不存在')
      }
    }

    if (chatData.userId !== dbUser.id) {
      throw new Error('无权限访问该聊天')
    }

    const userProfile = await getUserProfile(this.env, dbUser.id)

    return { dbUser, chatData, userProfile }
  }
}
