import type { Env } from '@/types/env';
import { generateUUID } from '@/lib/utils';
import { uploadToR2, IMAGE_UPLOAD_OPTIONS, getR2ConfigFromEnv } from '@/lib/utils/r2-upload';
import { updateMessageAttachments, getMessageById } from '@/modules/app/chat/repositories/message.repository';
import { getSupabase } from '@/lib/db/queries/base';
import {
  handleSupabaseResult,
  handleSupabaseSingleResult,
  TABLE_NAMES,
} from '@/lib/db/supabase-types';
import { getVoiceModelById, getVoiceModelByModelId } from '@/modules/app/voice/services/voice.service';

// TTS任务状态
export type TTSTaskStatus = 'pending' | 'processing' | 'completed' | 'failed';

// TTS任务接口 - 与数据库schema保持一致
export interface TTSTask {
  id: string;
  userId: string;
  messageId: string | null;
  chatId: string | null;
  text: string;
  voice: string | null;
  status: TTSTaskStatus;
  audioUrl: string | null;
  errorMessage: string | null;
  progress: number | null;
  createdAt: Date;
  updatedAt: Date;
}

// 创建TTS任务的参数
export interface CreateTTSTaskParams {
  text: string;
  messageId?: string;
  chatId?: string;
  voiceModelId?: string; // 声音模型ID
  userId: string;
}

// 查询TTS任务的参数
export interface GetTTSTasksParams {
  userId: string;
  chatId?: string;
  limit: number;
}

// Fish Audio API响应接口
interface FishAudioResponse {
  audio?: ArrayBuffer;
  error?: string;
}

export class TTSService {
  private env: Env;

  constructor(env: Env) {
    this.env = env;
  }

  /**
   * 同步生成音频（新的简化方法）
   */
  async generateAudioSync(params: CreateTTSTaskParams): Promise<{ audioUrl: string }> {
    console.log('开始同步生成TTS音频:', params.text.substring(0, 100) + '...');

    try {
      // 获取实际的modelId（Fish Audio reference_id）
      const referenceId = await this.getVoiceId(params.voiceModelId);

      // 调用Fish Audio API
      const audioBuffer = await this.callFishAudioAPI(params.text, referenceId);

      // 生成任务ID用于文件命名
      const taskId = generateUUID();

      // 上传到R2
      const audioUrl = await this.uploadAudioToR2(audioBuffer, taskId);

      // 更新消息附件（如果有messageId）
      if (params.messageId && params.chatId) {
        await this.updateMessageWithAudio(params.messageId, audioUrl);
      }

      console.log('TTS音频生成完成:', audioUrl);

      return { audioUrl };
    } catch (error) {
      console.error('同步生成TTS音频失败:', error);
      throw error;
    }
  }

  /**
   * 创建TTS任务
   */
  async createTTSTask(params: CreateTTSTaskParams): Promise<TTSTask> {
    const supabase = getSupabase(this.env);

    const result = await supabase
      .from(TABLE_NAMES.ttsTask)
      .insert({
        user_id: params.userId,
        message_id: params.messageId,
        chat_id: params.chatId,
        text: params.text,
        voice: params.voiceModelId, // 将voiceModelId存储到voice字段（兼容性）
        status: 'pending',
        progress: 0,
      })
      .select()
      .single();

    const { data: task, error } = handleSupabaseSingleResult(result);
    if (error) throw error;

    console.log('创建TTS任务:', task.id, '文本长度:', params.text.length);

    // 异步处理TTS任务
    this.processTTSTask(task.id).catch((error) => {
      console.error(`TTS任务 ${task.id} 处理失败:`, error);
      this.updateTaskStatus(task.id, 'failed', undefined, error.message);
    });

    return task;
  }

  /**
   * 获取TTS任务状态
   */
  async getTTSTaskStatus(taskId: string, userId: string): Promise<TTSTask | null> {
    const supabase = getSupabase(this.env);

    const result = await supabase
      .from(TABLE_NAMES.ttsTask)
      .select('*')
      .eq('id', taskId)
      .eq('user_id', userId)
      .limit(1)
      .single();

    const { data: task } = handleSupabaseSingleResult(result);
    return task || null;
  }

  /**
   * 获取用户的TTS任务列表
   */
  async getTTSTasks(params: GetTTSTasksParams): Promise<TTSTask[]> {
    const supabase = getSupabase(this.env);

    let queryBuilder = supabase
      .from(TABLE_NAMES.ttsTask)
      .select('*')
      .eq('user_id', params.userId)
      .order('created_at')
      .limit(params.limit);

    if (params.chatId) {
      queryBuilder = queryBuilder.eq('chat_id', params.chatId);
    }

    const result = await queryBuilder;
    const { data: tasks, error } = handleSupabaseResult(result);
    if (error) throw error;

    return tasks;
  }

  /**
   * 处理TTS任务
   */
  private async processTTSTask(taskId: string): Promise<void> {
    const supabase = getSupabase(this.env);

    // 获取任务
    const result = await supabase
      .from(TABLE_NAMES.ttsTask)
      .select('*')
      .eq('id', taskId)
      .limit(1)
      .single();

    const { data: task } = handleSupabaseSingleResult(result);

    if (!task) {
      throw new Error('任务不存在');
    }

    try {
      // 直接开始处理，不需要中间状态
      await this.updateTaskStatus(taskId, 'processing');

      // 获取实际的modelId（Fish Audio reference_id）
      const referenceId = await this.getVoiceId(task.voice || undefined);

      // 调用Fish Audio API（同步但可能耗时）
      const audioBuffer = await this.callFishAudioAPI(task.text, referenceId);

      // 上传到R2
      const audioUrl = await this.uploadAudioToR2(audioBuffer, taskId);

      // 更新消息附件（如果有messageId）
      if (task.messageId && task.chatId) {
        await this.updateMessageWithAudio(task.messageId, audioUrl);
      }

      // 完成任务
      await this.updateTaskStatus(taskId, 'completed', undefined, undefined, audioUrl);
    } catch (error) {
      console.error(`处理TTS任务 ${taskId} 失败:`, error);
      await this.updateTaskStatus(
        taskId,
        'failed',
        undefined,
        error instanceof Error ? error.message : '处理失败'
      );
    }
  }

  /**
   * 获取Fish Audio reference_id（从数据库中的声音模型获取modelId）
   */
  private async getVoiceId(voiceModelId?: string): Promise<string | undefined> {
    if (!voiceModelId) {
      // 没有指定声音模型，返回undefined，让Fish Audio使用默认声音
      return undefined;
    }

    try {
      // 检查是否为UUID格式（数据库ID）
      const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

      if (uuidPattern.test(voiceModelId)) {
        // 是UUID，从数据库中查找声音模型
        const voiceModel = await getVoiceModelById(this.env, voiceModelId);

        if (voiceModel && voiceModel.modelId) {
          console.log(`使用声音模型: ${voiceModel.displayName} (${voiceModel.modelId})`);
          return voiceModel.modelId;
        }
      } else {
        // 不是UUID，可能是旧的modelId格式，尝试按modelId查找验证
        const voiceModel = await getVoiceModelByModelId(this.env, voiceModelId);

        if (voiceModel && voiceModel.modelId) {
          console.log(`使用声音模型: ${voiceModel.displayName} (${voiceModel.modelId})`);
          return voiceModel.modelId;
        }

        // 如果没找到对应的声音模型，直接使用传入的值作为reference_id（兼容性）
        console.log(`未找到声音模型，直接使用reference_id: ${voiceModelId}`);
        return voiceModelId;
      }

      // 如果没找到，返回undefined，让Fish Audio使用默认声音
      console.log(`未找到声音模型，使用默认声音`);
      return undefined;
    } catch (error) {
      console.error('获取声音模型失败，使用默认声音:', error);
      return undefined;
    }
  }

  /**
   * 调用Fish Audio API
   */
  private async callFishAudioAPI(text: string, referenceId?: string): Promise<ArrayBuffer> {
    const apiKey = this.env.FISH_AUDIO_API_KEY;
    const modelId = '8d620a17e1d943c7b1a90165a3a6fed9';

    if (!apiKey) {
      throw new Error('Fish Audio API配置不完整');
    }

    // 构建请求体，只有在有有效的referenceId时才添加reference_id参数
    const requestBody: any = {
      text,
      reference_id: referenceId || modelId,
      format: 'mp3',
      normalize: true,
      latency: 'normal',
      temperature: 0.95,
      top_p: 0.95,
      prosody: {
        speed: 0.9,
      },
    };

    // 只有在有有效的referenceId时才添加reference_id参数
    if (referenceId) {
      console.log('调用Fish Audio API (带reference_id):', {
        text: text.substring(0, 100) + '...',
        referenceId,
      });
    }

    const response = await fetch('https://api.fish.audio/v1/tts', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        model: 'speech-1.6',
      },
      body: JSON.stringify(requestBody),
      // 增加超时时间，因为TTS可能需要较长时间
      signal: AbortSignal.timeout(60000), // 60秒超时
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Fish Audio API错误:', response.status, errorText);

      // 如果是reference_id相关的错误，尝试不使用reference_id重新请求
      if (referenceId && (response.status === 500 || errorText.includes('reference audio'))) {
        console.log('reference_id可能无效，尝试使用默认声音重新请求...');
        return this.callFishAudioAPI(text, undefined);
      }

      throw new Error(`Fish Audio API错误: ${response.status} ${errorText}`);
    }

    // Fish Audio API直接返回音频流
    const audioBuffer = await response.arrayBuffer();
    console.log('Fish Audio API返回音频大小:', audioBuffer.byteLength, 'bytes');

    if (audioBuffer.byteLength === 0) {
      throw new Error('Fish Audio API返回空音频');
    }

    return audioBuffer;
  }

  /**
   * 上传音频到R2
   */
  private async uploadAudioToR2(audioBuffer: ArrayBuffer, taskId: string): Promise<string> {
    // 在 Cloudflare Workers 环境中，从 env 对象获取 R2 配置
    const r2Config = getR2ConfigFromEnv(this.env);
    if (!r2Config) {
      throw new Error('R2配置不完整');
    }

    const fileName = `tts_${taskId}.mp3`;
    const uploadOptions = {
      ...IMAGE_UPLOAD_OPTIONS,
      fileName,
      folder: 'audio',
      allowedTypes: [
        'audio/mpeg',
        'audio/mp3',
        'audio/wav',
        'audio/ogg',
        'application/octet-stream', // 添加这个类型以支持 Fish Audio API 返回的数据
      ],
      maxSize: 50 * 1024 * 1024, // 50MB
    };

    const result = await uploadToR2(audioBuffer, r2Config, uploadOptions);

    if (!result.success || !result.url) {
      throw new Error(result.error || '上传音频文件失败');
    }

    return result.url;
  }

  /**
   * 更新消息附件（追加音频附件，保留现有附件）
   */
  private async updateMessageWithAudio(messageId: string, audioUrl: string): Promise<void> {
    try {
      // 获取现有消息
      const messages = await getMessageById(this.env,  messageId );
      const existingMessage = messages

      if (!existingMessage) {
        console.error('消息不存在:', messageId);
        return;
      }

      // 解析现有附件
      let existingAttachments: any[] = [];
      try {
        if (existingMessage.attachments) {
          existingAttachments =
            typeof existingMessage.attachments === 'string'
              ? JSON.parse(existingMessage.attachments)
              : existingMessage.attachments;
        }
      } catch (parseError) {
        console.error('解析现有附件失败:', parseError);
        existingAttachments = [];
      }

      // 检查是否已经有音频附件，避免重复添加
      const hasAudioAttachment = existingAttachments.some((attachment) =>
        attachment.contentType?.startsWith('audio/')
      );

      if (hasAudioAttachment) {
        console.log('消息已有音频附件，跳过添加');
        return;
      }

      // 创建新的音频附件
      const audioAttachment = {
        url: audioUrl,
        name: '语音',
        contentType: 'audio/mpeg',
      };

      // 合并现有附件和新的音频附件
      const updatedAttachments = [...existingAttachments, audioAttachment];

      // 更新消息附件
      await updateMessageAttachments(this.env, 
        messageId, updatedAttachments
      );

      console.log('成功追加音频附件到消息:', messageId);
    } catch (error) {
      console.error('更新消息附件失败:', error);
      // 不抛出错误，因为音频已经生成成功
    }
  }

  /**
   * 更新任务状态
   */
  private async updateTaskStatus(
    taskId: string,
    status: TTSTaskStatus,
    progress?: number,
    errorMessage?: string,
    audioUrl?: string
  ): Promise<void> {
    const supabase = getSupabase(this.env);

    const updateData: any = {
      status,
      updated_at: new Date().toISOString(),
    };

    if (progress !== undefined) {
      updateData.progress = progress;
    }

    if (errorMessage) {
      updateData.error_message = errorMessage;
    }

    if (audioUrl) {
      updateData.audio_url = audioUrl;
    }

    await supabase.from(TABLE_NAMES.ttsTask).update(updateData).eq('id', taskId);
  }
}
