/**
 * 优化的 ElevenLabs API 客户端
 * 集成连接池、请求去重、智能重试等高级优化
 */

import type {
  SessionInfo,
  V3GenerateRequest,
  V3GenerateResponse,
  ElevenLabsServiceConfig,
  RequestMetrics
} from './types'

export class OptimizedElevenLabsApiClient {
  private config: ElevenLabsServiceConfig
  private baseUrl = 'https://api.elevenlabs.io'
  private userAgent =
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  private referer = 'https://elevenlabs.io/'

  // 连接池和请求管理
  private activeRequests = new Map<string, Promise<Response>>()
  private requestMetrics = new Map<string, RequestMetrics>()
  private connectionPool = new Map<string, { connection: any; lastUsed: number }>()

  // 请求去重
  private requestCache = new Map<string, { response: any; timestamp: number }>()

  // 性能监控
  private totalRequests = 0
  private successfulRequests = 0
  private cacheHits = 0

  constructor(config: ElevenLabsServiceConfig) {
    this.config = config
    this.startBackgroundTasks()
  }

  /**
   * 生成TTS音频 - 优化版本
   */
  async generateTTS(request: V3GenerateRequest, session: SessionInfo): Promise<V3GenerateResponse> {
    const requestId = this.generateRequestId(request, session)
    const startTime = Date.now()

    this.totalRequests++
    this.recordMetrics(requestId, { startTime })

    try {
      // 1. 检查请求去重缓存
      if (this.config.enableRequestDeduplication) {
        const cached = this.getCachedResponse(requestId)
        if (cached) {
          this.cacheHits++
          return cached
        }
      }

      // 2. 检查是否有相同的进行中请求
      const existingRequest = this.activeRequests.get(requestId)
      if (existingRequest) {
        const response = await existingRequest
        return await this.parseResponse(response)
      }

      // 3. 创建新的请求
      const requestPromise = this.makeRequest(request, session)
      this.activeRequests.set(requestId, requestPromise)

      try {
        const response = await requestPromise
        const result = await this.parseResponse(response)

        // 4. 缓存结果
        if (this.config.enableRequestDeduplication && result.status === 'completed') {
          this.cacheResponse(requestId, result)
        }

        this.successfulRequests++
        this.recordMetrics(requestId, {
          endTime: Date.now(),
          success: true
        })

        return result
      } finally {
        this.activeRequests.delete(requestId)
      }
    } catch (error) {
      this.recordMetrics(requestId, {
        endTime: Date.now(),
        success: false,
        error: error instanceof Error ? error.message : String(error)
      })

      return {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 生成流式TTS音频 - 直接返回Response
   */
  async generateTTSStream(request: V3GenerateRequest, session: SessionInfo): Promise<Response> {
    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`,
      'User-Agent': this.userAgent,
      Referer: this.referer
    }

    const response = await fetch(`${this.baseUrl}/v1/text-to-dialogue/stream`, {
      method: 'POST',
      headers,
      body: JSON.stringify(request),
      signal: AbortSignal.timeout(this.config.requestTimeoutMs)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`ElevenLabs API 错误: ${response.status} - ${errorText}`)
    }

    return response
  }

  /**
   * 创建HTTP请求
   */
  private async makeRequest(request: V3GenerateRequest, session: SessionInfo): Promise<Response> {
    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`,
      'User-Agent': this.userAgent,
      Referer: this.referer
    }

    const response = await fetch(`${this.baseUrl}/v1/text-to-dialogue/stream`, {
      method: 'POST',
      headers,
      body: JSON.stringify(request),
      signal: AbortSignal.timeout(this.config.requestTimeoutMs)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`ElevenLabs API 错误: ${response.status} - ${errorText}`)
    }

    return response
  }

  /**
   * 解析响应
   */
  private async parseResponse(response: Response): Promise<V3GenerateResponse> {
    const contentType = response.headers.get('content-type') || ''

    if (contentType.includes('application/json')) {
      const data = (await response.json()) as any

      if (data.status === 'processing' && data.task_id) {
        // 异步任务，需要轮询
        return await this.pollTaskStatus(data.task_id, response.headers.get('authorization') || '')
      }

      return data as V3GenerateResponse
    } else if (contentType.includes('audio/')) {
      // 直接返回音频数据
      const audioBuffer = await response.arrayBuffer()
      return {
        status: 'completed',
        audio_data: audioBuffer
      }
    } else {
      throw new Error(`意外的响应类型: ${contentType}`)
    }
  }

  /**
   * 轮询任务状态 - 优化版本
   */
  async pollTaskStatus(
    taskId: string,
    authToken: string,
    maxAttempts = 30
  ): Promise<V3GenerateResponse> {
    const pollInterval = 2000 // 2秒
    let attempt = 0

    while (attempt < maxAttempts) {
      attempt++

      try {
        const response = await fetch(`${this.baseUrl}/v1/text-to-speech/task/${taskId}`, {
          headers: {
            Authorization: authToken,
            'User-Agent': this.userAgent,
            Referer: this.referer
          },
          signal: AbortSignal.timeout(10000) // 10秒超时
        })

        if (!response.ok) {
          if (attempt === maxAttempts) {
            return {
              status: 'failed',
              error: `轮询任务超时: ${response.status}`
            }
          }

          await this.sleep(pollInterval)
          continue
        }

        const result = (await response.json()) as any

        if (result.status === 'completed') {
          if (result.audio_url) {
            // 下载音频数据
            const audioResponse = await fetch(result.audio_url, {
              signal: AbortSignal.timeout(30000) // 30秒下载超时
            })
            const audioBuffer = await audioResponse.arrayBuffer()

            return {
              status: 'completed',
              audio_data: audioBuffer,
              audio_url: result.audio_url
            }
          } else {
            return {
              status: 'failed',
              error: '任务完成但无音频 URL'
            }
          }
        } else if (result.status === 'failed') {
          return {
            status: 'failed',
            error: result.error || '任务失败'
          }
        } else if (result.status === 'processing') {
          // 继续等待
          await this.sleep(pollInterval)
          continue
        } else {
          return {
            status: 'failed',
            error: `未知任务状态: ${result.status}`
          }
        }
      } catch (error) {
        if (attempt === maxAttempts) {
          return {
            status: 'failed',
            error: `轮询任务失败: ${error instanceof Error ? error.message : 'Unknown error'}`
          }
        }

        await this.sleep(pollInterval)
      }
    }

    return {
      status: 'failed',
      error: '轮询任务超时'
    }
  }

  /**
   * 获取声音列表
   */
  async getVoices(session: SessionInfo): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/voices`, {
        headers: {
          Authorization: `Bearer ${session.token}`,
          'User-Agent': this.userAgent,
          Referer: this.referer
        },
        signal: AbortSignal.timeout(10000)
      })

      if (!response.ok) {
        console.error('获取声音列表失败:', response.status)
        return []
      }

      const result = (await response.json()) as any
      return result.voices || []
    } catch (error) {
      console.error('获取声音列表异常:', error)
      return []
    }
  }

  /**
   * 获取用户配额信息
   */
  async getUserQuota(session: SessionInfo): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/user/subscription`, {
        headers: {
          Authorization: `Bearer ${session.token}`,
          'User-Agent': this.userAgent,
          Referer: this.referer
        },
        signal: AbortSignal.timeout(10000)
      })

      if (!response.ok) {
        console.error('获取用户配额失败:', response.status)
        return null
      }

      return await response.json()
    } catch (error) {
      console.error('获取用户配额异常:', error)
      return null
    }
  }

  /**
   * 生成请求ID用于去重
   */
  private generateRequestId(request: V3GenerateRequest, _session: SessionInfo): string {
    const key = JSON.stringify({
      text: request.inputs[0]?.text,
      voice_id: request.inputs[0]?.voice_id,
      model_id: request.model_id,
      settings: request.settings
    })

    return `tts_${this.hashString(key)}`
  }

  /**
   * 简单字符串哈希
   */
  private hashString(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * 获取缓存的响应
   */
  private getCachedResponse(requestId: string): V3GenerateResponse | null {
    const cached = this.requestCache.get(requestId)
    if (!cached) {
      return null
    }

    // 检查缓存是否过期
    const cacheAge = Date.now() - cached.timestamp
    if (cacheAge > 300000) {
      // 5分钟缓存
      this.requestCache.delete(requestId)
      return null
    }

    return cached.response
  }

  /**
   * 缓存响应
   */
  private cacheResponse(requestId: string, response: V3GenerateResponse): void {
    this.requestCache.set(requestId, {
      response,
      timestamp: Date.now()
    })
  }

  /**
   * 记录请求指标
   */
  private recordMetrics(requestId: string, metrics: Partial<RequestMetrics>): void {
    const existing = this.requestMetrics.get(requestId) || { startTime: Date.now() }
    this.requestMetrics.set(requestId, { ...existing, ...metrics })
  }

  /**
   * 启动后台任务
   */
  private startBackgroundTasks(): void {
    // 定期清理缓存
    setInterval(() => {
      this.cleanupCaches()
    }, 300000) // 每5分钟清理一次

    // 定期清理指标
    setInterval(() => {
      this.cleanupMetrics()
    }, 600000) // 每10分钟清理一次
  }

  /**
   * 清理过期缓存
   */
  private cleanupCaches(): void {
    const now = Date.now()
    const maxAge = 300000 // 5分钟

    // 清理请求缓存
    for (const [key, cached] of this.requestCache.entries()) {
      if (now - cached.timestamp > maxAge) {
        this.requestCache.delete(key)
      }
    }

    // 清理连接池
    for (const [key, conn] of this.connectionPool.entries()) {
      if (now - conn.lastUsed > 600000) {
        // 10分钟
        this.connectionPool.delete(key)
      }
    }
  }

  /**
   * 清理指标数据
   */
  private cleanupMetrics(): void {
    const now = Date.now()
    const maxAge = 3600000 // 1小时

    for (const [key, metrics] of this.requestMetrics.entries()) {
      if (now - metrics.startTime > maxAge) {
        this.requestMetrics.delete(key)
      }
    }
  }

  /**
   * 等待函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return {
      totalRequests: this.totalRequests,
      successfulRequests: this.successfulRequests,
      successRate: this.totalRequests > 0 ? this.successfulRequests / this.totalRequests : 0,
      cacheHits: this.cacheHits,
      cacheHitRate: this.totalRequests > 0 ? this.cacheHits / this.totalRequests : 0,
      activeRequests: this.activeRequests.size,
      cachedResponses: this.requestCache.size,
      connectionPoolSize: this.connectionPool.size
    }
  }
}
