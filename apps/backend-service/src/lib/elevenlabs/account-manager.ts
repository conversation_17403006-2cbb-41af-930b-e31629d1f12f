/**
 * 优化的账号管理器 - 高性能内存缓存版本
 * 专为 backend-service 设计，极致性能优化
 */

import type { Env } from '../../types/env'
import type {
  ElevenLabsAccount,
  AccountHealth,
  CachedAccountSelection,
  BatchAccountUpdate,
  ElevenLabsServiceConfig,
  PerformanceEvent
} from './types'

// 硬编码账号配置 - 从环境变量或配置文件加载
const HARDCODED_ACCOUNTS = [
  {
    email: '<EMAIL>',
    password: 'fkj2vrb-YZA*fvf9rua'
    // email: '<EMAIL>',
    // password: 'jyg5gmf8gte6YNU!jfg'
  }
  // 可以添加更多账号
]

export class OptimizedAccountManager {
  private env: Env
  private config: ElevenLabsServiceConfig

  // 内存缓存
  private accountsCache: Map<string, ElevenLabsAccount> = new Map()
  private accountSelectionCache: CachedAccountSelection | null = null
  private healthCache: Map<string, AccountHealth> = new Map()

  // 性能监控
  private performanceEvents: PerformanceEvent[] = []
  private lastCacheRefresh = 0
  private requestCount = 0
  private cacheHits = 0

  // 异步更新队列
  private updateQueue: Array<() => Promise<void>> = []
  private isProcessingQueue = false

  constructor(env: Env, config: ElevenLabsServiceConfig) {
    this.env = env
    this.config = config

    // 启动后台任务
    this.startBackgroundTasks()
  }

  /**
   * 获取下一个可用账号
   */
  async getNextAvailableAccount(): Promise<ElevenLabsAccount | null> {
    const startTime = Date.now()
    this.requestCount++

    try {
      // 1. 检查缓存的账号选择
      if (this.accountSelectionCache && this.isSelectionValid(this.accountSelectionCache)) {
        const cachedAccount = this.accountsCache.get(this.accountSelectionCache.accountId)
        if (cachedAccount && this.isAccountUsable(cachedAccount)) {
          this.cacheHits++
          this.recordEvent('account_selected', startTime, cachedAccount.id, true, true)

          // 异步更新使用时间，不阻塞响应
          this.queueAsyncUpdate(() => this.updateAccountUsageAsync(cachedAccount.id))

          return cachedAccount
        }
      }

      // 2. 从内存缓存获取账号列表
      await this.ensureAccountsLoaded()
      const accounts = Array.from(this.accountsCache.values())

      if (accounts.length === 0) {
        await this.initializeAccounts()
        return await this.getNextAvailableAccount()
      }

      // 3. 智能选择最优账号
      const bestAccount = this.selectBestAccount(accounts)
      if (!bestAccount) {
        // 重置失败计数并重试
        await this.resetFailureCountsAsync()
        return accounts.find(acc => acc.isActive) || null
      }

      // 4. 缓存选择结果
      this.cacheAccountSelection(bestAccount)

      // 5. 异步更新使用时间
      this.queueAsyncUpdate(() => this.updateAccountUsageAsync(bestAccount.id))

      this.recordEvent('account_selected', startTime, bestAccount.id, true, false)
      return bestAccount
    } catch (error) {
      this.recordEvent('account_selected', startTime, undefined, false, false)
      console.error('获取可用账号失败:', error)
      return null
    }
  }

  /**
   * 智能选择最优账号
   */
  private selectBestAccount(accounts: ElevenLabsAccount[]): ElevenLabsAccount | null {
    // 过滤可用账号
    const usableAccounts = accounts.filter(acc => this.isAccountUsable(acc))

    if (usableAccounts.length === 0) {
      return null
    }

    // 按优先级排序：成功率 > 响应时间 > 最后使用时间
    usableAccounts.sort((a, b) => {
      // 1. 优先选择成功率高的账号
      const aSuccessRate = this.calculateSuccessRate(a)
      const bSuccessRate = this.calculateSuccessRate(b)
      if (Math.abs(aSuccessRate - bSuccessRate) > 0.1) {
        return bSuccessRate - aSuccessRate
      }

      // 2. 响应时间优先
      const aResponseTime = a.avgResponseTime || 1000
      const bResponseTime = b.avgResponseTime || 1000
      if (Math.abs(aResponseTime - bResponseTime) > 100) {
        return aResponseTime - bResponseTime
      }

      // 3. 最后使用时间（负载均衡）
      const aTime = a.lastUsed?.getTime() || 0
      const bTime = b.lastUsed?.getTime() || 0
      return aTime - bTime
    })

    return usableAccounts[0]
  }

  /**
   * 检查账号是否可用
   */
  private isAccountUsable(account: ElevenLabsAccount): boolean {
    return account.isActive && account.failureCount < 3 && this.calculateSuccessRate(account) > 0.5
  }

  /**
   * 计算账号成功率
   */
  private calculateSuccessRate(account: ElevenLabsAccount): number {
    const successes = account.consecutiveSuccesses || 0
    const failures = account.failureCount || 0
    const total = successes + failures

    if (total === 0) return 1.0 // 新账号默认成功率100%
    return successes / total
  }

  /**
   * 缓存账号选择结果
   */
  private cacheAccountSelection(account: ElevenLabsAccount): void {
    this.accountSelectionCache = {
      accountId: account.id,
      selectedAt: new Date(),
      validUntil: new Date(Date.now() + this.config.accountCacheTimeMs)
    }
  }

  /**
   * 检查选择缓存是否有效
   */
  private isSelectionValid(selection: CachedAccountSelection): boolean {
    return selection.validUntil.getTime() > Date.now()
  }

  /**
   * 确保账号已加载到内存
   */
  private async ensureAccountsLoaded(): Promise<void> {
    const now = Date.now()

    // 如果缓存还有效，直接返回
    if (
      now - this.lastCacheRefresh < this.config.accountCacheTimeMs &&
      this.accountsCache.size > 0
    ) {
      return
    }

    try {
      const accounts = await this.loadAccountsFromStorage()

      // 更新内存缓存
      this.accountsCache.clear()
      accounts.forEach(account => {
        this.accountsCache.set(account.id, account)
      })

      this.lastCacheRefresh = now
    } catch (error) {
      console.error('加载账号失败:', error)
    }
  }

  /**
   * 从存储加载账号
   */
  private async loadAccountsFromStorage(): Promise<ElevenLabsAccount[]> {
    try {
      const accountsJson = await this.env.CACHE?.get('elevenlabs:accounts')
      if (!accountsJson) {
        return []
      }

      const accounts = JSON.parse(accountsJson) as ElevenLabsAccount[]
      return accounts.map(account => ({
        ...account,
        lastUsed: account.lastUsed ? new Date(account.lastUsed) : undefined,
        tokenExpiry: account.tokenExpiry ? new Date(account.tokenExpiry) : undefined,
        lastHealthCheck: account.lastHealthCheck ? new Date(account.lastHealthCheck) : undefined
      }))
    } catch (error) {
      console.error('解析账号数据失败:', error)
      return []
    }
  }

  /**
   * 初始化账号
   */
  private async initializeAccounts(): Promise<void> {
    console.log('初始化 ElevenLabs 账号...')

    const accounts: ElevenLabsAccount[] = HARDCODED_ACCOUNTS.map(accountData => ({
      id: this.generateAccountId(),
      email: accountData.email,
      password: accountData.password,
      isActive: true,
      failureCount: 0,
      consecutiveSuccesses: 0,
      avgResponseTime: 1000
    }))

    // 批量保存到内存和存储
    accounts.forEach(account => {
      this.accountsCache.set(account.id, account)
    })

    await this.saveAccountsToStorage(accounts)
    console.log(`已初始化 ${accounts.length} 个账号`)
  }

  /**
   * 保存账号到存储
   */
  private async saveAccountsToStorage(accounts: ElevenLabsAccount[]): Promise<void> {
    try {
      await this.env.CACHE?.put('elevenlabs:accounts', JSON.stringify(accounts), {
        expirationTtl: 86400 // 24小时
      })
    } catch (error) {
      console.error('保存账号失败:', error)
    }
  }

  /**
   * 异步更新账号使用时间
   */
  private async updateAccountUsageAsync(accountId: string): Promise<void> {
    const account = this.accountsCache.get(accountId)
    if (account) {
      account.lastUsed = new Date()
      // 不立即保存到存储，等待批量更新
    }
  }

  /**
   * 添加异步更新任务到队列
   */
  private queueAsyncUpdate(updateFn: () => Promise<void>): void {
    this.updateQueue.push(updateFn)

    if (!this.isProcessingQueue) {
      // 延迟处理，批量执行
      setTimeout(() => this.processUpdateQueue(), 100)
    }
  }

  /**
   * 处理更新队列
   */
  private async processUpdateQueue(): Promise<void> {
    if (this.isProcessingQueue || this.updateQueue.length === 0) {
      return
    }

    this.isProcessingQueue = true

    try {
      // 批量执行更新
      const updates = this.updateQueue.splice(0)
      await Promise.all(updates.map(update => update().catch(console.error)))

      // 批量保存到存储
      const accounts = Array.from(this.accountsCache.values())
      await this.saveAccountsToStorage(accounts)
    } catch (error) {
      console.error('处理更新队列失败:', error)
    } finally {
      this.isProcessingQueue = false

      // 如果还有待处理的更新，继续处理
      if (this.updateQueue.length > 0) {
        setTimeout(() => this.processUpdateQueue(), 100)
      }
    }
  }

  /**
   * 启动后台任务
   */
  private startBackgroundTasks(): void {
    // 定期清理性能事件
    setInterval(() => {
      const cutoff = Date.now() - 3600000 // 保留1小时的事件
      this.performanceEvents = this.performanceEvents.filter(
        event => event.timestamp.getTime() > cutoff
      )
    }, 300000) // 每5分钟清理一次

    // 定期健康检查
    setInterval(() => {
      this.performHealthCheck().catch(console.error)
    }, this.config.healthCheckIntervalMs)
  }

  /**
   * 记录性能事件
   */
  private recordEvent(
    type: PerformanceEvent['type'],
    startTime: number,
    accountId?: string,
    success?: boolean,
    fromCache?: boolean
  ): void {
    this.performanceEvents.push({
      type,
      timestamp: new Date(),
      accountId,
      duration: Date.now() - startTime,
      success,
      metadata: { fromCache }
    })
  }

  /**
   * 生成账号ID
   */
  private generateAccountId(): string {
    return `acc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 异步重置失败计数
   */
  private async resetFailureCountsAsync(): Promise<void> {
    this.accountsCache.forEach(account => {
      account.failureCount = 0
      account.isActive = true
    })
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    // 实现健康检查逻辑
    console.log('执行账号健康检查...')
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return {
      totalRequests: this.requestCount,
      cacheHitRate: this.requestCount > 0 ? this.cacheHits / this.requestCount : 0,
      accountsLoaded: this.accountsCache.size,
      queueSize: this.updateQueue.length,
      recentEvents: this.performanceEvents.slice(-10)
    }
  }
}
