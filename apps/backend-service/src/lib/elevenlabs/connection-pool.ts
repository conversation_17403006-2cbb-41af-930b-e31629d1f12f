/**
 * HTTP 连接池管理器
 * 复用连接，减少连接建立开销
 */

import type { ConnectionPoolConfig } from './types'

interface PooledConnection {
  id: string
  url: string
  lastUsed: number
  inUse: boolean
  requestCount: number
  createdAt: number
}

export class ConnectionPool {
  private config: ConnectionPoolConfig
  private connections = new Map<string, PooledConnection>()
  private connectionCounter = 0

  constructor(config: ConnectionPoolConfig) {
    this.config = config
    this.startCleanupTask()
  }

  /**
   * 获取或创建连接
   */
  async getConnection(url: string): Promise<string> {
    const baseUrl = this.getBaseUrl(url)

    // 查找可用的连接
    const availableConnection = this.findAvailableConnection(baseUrl)
    if (availableConnection) {
      availableConnection.inUse = true
      availableConnection.lastUsed = Date.now()
      availableConnection.requestCount++
      return availableConnection.id
    }

    // 检查是否达到最大连接数
    if (this.connections.size >= this.config.maxConnections) {
      // 清理最旧的空闲连接
      this.cleanupOldestIdleConnection()
    }

    // 创建新连接
    const connection: PooledConnection = {
      id: `conn_${++this.connectionCounter}`,
      url: baseUrl,
      lastUsed: Date.now(),
      inUse: true,
      requestCount: 1,
      createdAt: Date.now()
    }

    this.connections.set(connection.id, connection)
    return connection.id
  }

  /**
   * 释放连接
   */
  releaseConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId)
    if (connection) {
      connection.inUse = false
      connection.lastUsed = Date.now()
    }
  }

  /**
   * 查找可用连接
   */
  private findAvailableConnection(url: string): PooledConnection | null {
    for (const connection of this.connections.values()) {
      if (!connection.inUse && connection.url === url) {
        // 检查连接是否过期
        const age = Date.now() - connection.lastUsed
        if (age < this.config.maxIdleTime) {
          return connection
        }
      }
    }
    return null
  }

  /**
   * 清理最旧的空闲连接
   */
  private cleanupOldestIdleConnection(): void {
    let oldestConnection: PooledConnection | null = null
    let oldestTime = Date.now()

    for (const connection of this.connections.values()) {
      if (!connection.inUse && connection.lastUsed < oldestTime) {
        oldestTime = connection.lastUsed
        oldestConnection = connection
      }
    }

    if (oldestConnection) {
      this.connections.delete(oldestConnection.id)
    }
  }

  /**
   * 获取基础URL
   */
  private getBaseUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      return `${urlObj.protocol}//${urlObj.host}`
    } catch {
      return url
    }
  }

  /**
   * 启动清理任务
   */
  private startCleanupTask(): void {
    setInterval(() => {
      this.cleanupExpiredConnections()
    }, 60000) // 每分钟清理一次
  }

  /**
   * 清理过期连接
   */
  private cleanupExpiredConnections(): void {
    const now = Date.now()
    const expiredConnections: string[] = []

    for (const [id, connection] of this.connections.entries()) {
      if (!connection.inUse) {
        const age = now - connection.lastUsed
        if (age > this.config.maxIdleTime) {
          expiredConnections.push(id)
        }
      }
    }

    expiredConnections.forEach(id => {
      this.connections.delete(id)
    })

    if (expiredConnections.length > 0) {
      console.log(`🧹 清理了 ${expiredConnections.length} 个过期连接`)
    }
  }

  /**
   * 获取连接池统计
   */
  getStats() {
    const totalConnections = this.connections.size
    const activeConnections = Array.from(this.connections.values()).filter(c => c.inUse).length
    const idleConnections = totalConnections - activeConnections

    return {
      totalConnections,
      activeConnections,
      idleConnections,
      maxConnections: this.config.maxConnections,
      utilizationRate: totalConnections / this.config.maxConnections
    }
  }
}

/**
 * 请求去重管理器
 * 避免重复的相同请求
 */
export class RequestDeduplicator {
  private pendingRequests = new Map<string, Promise<any>>()
  private responseCache = new Map<string, { response: any; timestamp: number }>()
  private cacheTimeMs: number

  constructor(cacheTimeMs = 300000) {
    // 默认5分钟缓存
    this.cacheTimeMs = cacheTimeMs
    this.startCleanupTask()
  }

  /**
   * 执行去重请求
   */
  async deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    // 检查缓存
    const cached = this.getCachedResponse<T>(key)
    if (cached) {
      return cached
    }

    // 检查是否有进行中的相同请求
    const existingRequest = this.pendingRequests.get(key)
    if (existingRequest) {
      return await existingRequest
    }

    // 创建新请求
    const requestPromise = requestFn()
    this.pendingRequests.set(key, requestPromise)

    try {
      const result = await requestPromise

      // 缓存结果
      this.cacheResponse(key, result)

      return result
    } finally {
      // 清理进行中的请求
      this.pendingRequests.delete(key)
    }
  }

  /**
   * 获取缓存的响应
   */
  private getCachedResponse<T>(key: string): T | null {
    const cached = this.responseCache.get(key)
    if (!cached) {
      return null
    }

    // 检查是否过期
    const age = Date.now() - cached.timestamp
    if (age > this.cacheTimeMs) {
      this.responseCache.delete(key)
      return null
    }

    return cached.response
  }

  /**
   * 缓存响应
   */
  private cacheResponse(key: string, response: any): void {
    this.responseCache.set(key, {
      response,
      timestamp: Date.now()
    })
  }

  /**
   * 启动清理任务
   */
  private startCleanupTask(): void {
    setInterval(() => {
      this.cleanupExpiredCache()
    }, 300000) // 每5分钟清理一次
  }

  /**
   * 清理过期缓存
   */
  private cleanupExpiredCache(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, cached] of this.responseCache.entries()) {
      const age = now - cached.timestamp
      if (age > this.cacheTimeMs) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => {
      this.responseCache.delete(key)
    })

    if (expiredKeys.length > 0) {
      console.log(`🧹 清理了 ${expiredKeys.length} 个过期缓存`)
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      pendingRequests: this.pendingRequests.size,
      cachedResponses: this.responseCache.size,
      cacheTimeMs: this.cacheTimeMs
    }
  }
}

/**
 * 智能重试管理器
 * 根据错误类型和历史成功率智能调整重试策略
 */
export class SmartRetryManager {
  private retryHistory = new Map<
    string,
    { attempts: number; successes: number; lastSuccess: number }
  >()

  /**
   * 执行智能重试
   */
  async executeWithRetry<T>(
    key: string,
    requestFn: () => Promise<T>,
    maxRetries = 3,
    baseDelayMs = 1000
  ): Promise<T> {
    const history = this.getRetryHistory(key)
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await requestFn()

        // 记录成功
        history.successes++
        history.lastSuccess = Date.now()

        return result
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        history.attempts++

        // 如果是最后一次尝试，直接抛出错误
        if (attempt === maxRetries) {
          break
        }

        // 计算智能延迟
        const delay = this.calculateSmartDelay(history, attempt, baseDelayMs, lastError)

        console.log(`🔄 重试 ${attempt}/${maxRetries} [${key}] 延迟: ${delay}ms`)
        await this.sleep(delay)
      }
    }

    throw lastError || new Error('重试失败')
  }

  /**
   * 获取重试历史
   */
  private getRetryHistory(key: string) {
    if (!this.retryHistory.has(key)) {
      this.retryHistory.set(key, {
        attempts: 0,
        successes: 0,
        lastSuccess: Date.now()
      })
    }
    return this.retryHistory.get(key)!
  }

  /**
   * 计算智能延迟
   */
  private calculateSmartDelay(
    history: { attempts: number; successes: number; lastSuccess: number },
    attempt: number,
    baseDelayMs: number,
    error: Error
  ): number {
    // 基础指数退避
    let delay = baseDelayMs * Math.pow(2, attempt - 1)

    // 根据历史成功率调整
    const successRate = history.attempts > 0 ? history.successes / history.attempts : 1
    if (successRate < 0.5) {
      delay *= 2 // 成功率低，增加延迟
    }

    // 根据错误类型调整
    if (error.message.includes('timeout')) {
      delay *= 1.5 // 超时错误，增加延迟
    } else if (error.message.includes('rate limit')) {
      delay *= 3 // 限流错误，大幅增加延迟
    }

    // 添加随机抖动，避免雷群效应
    const jitter = Math.random() * 0.3 * delay
    delay += jitter

    return Math.min(delay, 30000) // 最大30秒
  }

  /**
   * 等待函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const totalAttempts = Array.from(this.retryHistory.values()).reduce(
      (sum, h) => sum + h.attempts,
      0
    )
    const totalSuccesses = Array.from(this.retryHistory.values()).reduce(
      (sum, h) => sum + h.successes,
      0
    )

    return {
      totalKeys: this.retryHistory.size,
      totalAttempts,
      totalSuccesses,
      overallSuccessRate: totalAttempts > 0 ? totalSuccesses / totalAttempts : 0
    }
  }
}
