/**
 * 优化的会话管理器 - 高性能版本
 * 减少网络请求和KV操作，使用智能缓存和批量处理
 */

import type { Env } from '../../types/env'
import type {
  ElevenLabsAccount,
  SessionInfo,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  ElevenLabsServiceConfig,
  PerformanceEvent
} from './types'

export class OptimizedSessionManager {
  private env: Env
  private config: ElevenLabsServiceConfig

  // 内存缓存
  private sessionCache: Map<string, SessionInfo> = new Map()
  private loginPromises: Map<string, Promise<SessionInfo | null>> = new Map()
  private refreshPromises: Map<string, Promise<SessionInfo | null>> = new Map()

  // 性能优化
  private lastCacheCleanup = 0
  private requestCount = 0
  private cacheHits = 0

  // 批量操作队列
  private saveQueue: Map<string, SessionInfo> = new Map()
  private isSaving = false

  // API 配置
  private readonly LOGIN_API_KEY = 'AIzaSyBSsRE_1Os04-bxpd5JTLIniy3UK4OqKys'
  private readonly BASE_URL = 'https://identitytoolkit.googleapis.com'

  constructor(env: Env, config: ElevenLabsServiceConfig) {
    this.env = env
    this.config = config

    // 启动后台任务
    this.startBackgroundTasks()
  }

  /**
   * 获取有效会话 - 极致优化版本
   */
  async getValidSession(account: ElevenLabsAccount): Promise<SessionInfo | null> {
    const startTime = Date.now()
    this.requestCount++

    try {
      // 1. 检查内存缓存
      const cachedSession = this.sessionCache.get(account.id)
      if (cachedSession && this.isSessionValid(cachedSession)) {
        this.cacheHits++
        this.recordPerformance('session_cache_hit', startTime, account.id, true)
        return cachedSession
      }

      // 2. 检查账号自带的有效token
      if (account.sessionToken && account.tokenExpiry && this.isTokenValid(account.tokenExpiry)) {
        const sessionInfo: SessionInfo = {
          token: account.sessionToken,
          refreshToken: account.refreshToken,
          expiry: account.tokenExpiry,
          userId: account.id,
          createdAt: new Date(),
          lastValidated: new Date()
        }

        // 缓存到内存
        this.sessionCache.set(account.id, sessionInfo)
        this.recordPerformance('session_from_account', startTime, account.id, true)
        return sessionInfo
      }

      // 3. 尝试刷新token（如果有refreshToken）
      if (account.refreshToken) {
        const refreshedSession = await this.refreshTokenWithDeduplication(account)
        if (refreshedSession) {
          this.recordPerformance('session_refreshed', startTime, account.id, true)
          return refreshedSession
        }
      }

      // 4. 重新登录（去重处理）
      const loginSession = await this.loginWithDeduplication(account)
      this.recordPerformance('session_login', startTime, account.id, !!loginSession)
      return loginSession
    } catch (error) {
      this.recordPerformance('session_error', startTime, account.id, false)
      console.error(`获取会话失败 [${account.email}]:`, error)
      return null
    }
  }

  /**
   * 带去重的登录
   */
  private async loginWithDeduplication(account: ElevenLabsAccount): Promise<SessionInfo | null> {
    // 检查是否已有进行中的登录请求
    const existingPromise = this.loginPromises.get(account.id)
    if (existingPromise) {
      return await existingPromise
    }

    // 创建新的登录Promise
    const loginPromise = this.performLogin(account)
    this.loginPromises.set(account.id, loginPromise)

    try {
      const result = await loginPromise
      return result
    } finally {
      // 清理Promise缓存
      this.loginPromises.delete(account.id)
    }
  }

  /**
   * 带去重的token刷新
   */
  private async refreshTokenWithDeduplication(
    account: ElevenLabsAccount
  ): Promise<SessionInfo | null> {
    // 检查是否已有进行中的刷新请求
    const existingPromise = this.refreshPromises.get(account.id)
    if (existingPromise) {
      return await existingPromise
    }

    // 创建新的刷新Promise
    const refreshPromise = this.performRefreshToken(account)
    this.refreshPromises.set(account.id, refreshPromise)

    try {
      const result = await refreshPromise
      return result
    } finally {
      // 清理Promise缓存
      this.refreshPromises.delete(account.id)
    }
  }

  /**
   * 执行登录
   */
  private async performLogin(account: ElevenLabsAccount): Promise<SessionInfo | null> {
    try {
      const loginData: LoginRequest = {
        returnSecureToken: true,
        email: account.email,
        password: account.password,
        clientType: 'CLIENT_TYPE_WEB'
      }

      const response = await fetch(
        `${this.BASE_URL}/v1/accounts:signInWithPassword?key=${this.LOGIN_API_KEY}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Referer: 'https://elevenlabs.io',
            'User-Agent':
              'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
          },
          body: JSON.stringify(loginData),
          signal: AbortSignal.timeout(this.config.connectionTimeoutMs)
        }
      )

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`登录失败: ${response.status} ${errorText}`)
      }

      const result: LoginResponse = await response.json()

      if (!result.idToken) {
        throw new Error('登录响应无效')
      }

      const expiryTime = new Date(Date.now() + Number.parseInt(result.expiresIn) * 1000)
      const sessionInfo: SessionInfo = {
        token: result.idToken,
        refreshToken: result.refreshToken,
        expiry: expiryTime,
        userId: account.id,
        createdAt: new Date(),
        lastValidated: new Date()
      }

      // 缓存到内存
      this.sessionCache.set(account.id, sessionInfo)

      // 异步保存到存储
      this.queueSave(account.id, sessionInfo)

      return sessionInfo
    } catch (error) {
      console.error(`登录失败 [${account.email}]:`, error)
      return null
    }
  }

  /**
   * 执行token刷新
   */
  private async performRefreshToken(account: ElevenLabsAccount): Promise<SessionInfo | null> {
    if (!account.refreshToken) {
      return null
    }

    try {
      const refreshData: RefreshTokenRequest = {
        grant_type: 'refresh_token',
        refresh_token: account.refreshToken
      }

      const response = await fetch(`${this.BASE_URL}/v1/token?key=${this.LOGIN_API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Referer: 'https://elevenlabs.io/',
          'sec-ch-ua': '"Google Chrome";v="120", "Chromium";v="120", "Not/A)Brand";v="24"',
          'User-Agent':
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        },
        body: new URLSearchParams(refreshData).toString(),
        signal: AbortSignal.timeout(this.config.connectionTimeoutMs)
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`刷新token失败: ${response.status} ${errorText}`)
      }

      const result: RefreshTokenResponse = await response.json()

      const expiryTime = new Date(Date.now() + Number.parseInt(result.expires_in) * 1000)
      const sessionInfo: SessionInfo = {
        token: result.access_token,
        refreshToken: result.refresh_token,
        expiry: expiryTime,
        userId: account.id,
        createdAt: new Date(),
        lastValidated: new Date()
      }

      // 缓存到内存
      this.sessionCache.set(account.id, sessionInfo)

      // 异步保存到存储
      this.queueSave(account.id, sessionInfo)

      return sessionInfo
    } catch (error) {
      console.error(`刷新token失败 [${account.email}]:`, error)
      return null
    }
  }

  /**
   * 检查会话是否有效
   */
  private isSessionValid(session: SessionInfo): boolean {
    const now = Date.now()
    const buffer = this.config.tokenRefreshThresholdMs
    return session.expiry.getTime() > now + buffer
  }

  /**
   * 检查token是否有效
   */
  private isTokenValid(expiry: Date): boolean {
    const now = Date.now()
    const buffer = this.config.tokenRefreshThresholdMs
    return expiry.getTime() > now + buffer
  }

  /**
   * 队列保存会话
   */
  private queueSave(accountId: string, session: SessionInfo): void {
    this.saveQueue.set(accountId, session)

    if (!this.isSaving) {
      // 延迟批量保存
      setTimeout(() => this.processSaveQueue(), 200)
    }
  }

  /**
   * 处理保存队列
   */
  private async processSaveQueue(): Promise<void> {
    if (this.isSaving || this.saveQueue.size === 0) {
      return
    }

    this.isSaving = true

    try {
      const saves = Array.from(this.saveQueue.entries())
      this.saveQueue.clear()

      // 批量保存
      await Promise.all(
        saves.map(([accountId, session]) => this.saveSessionToStorage(accountId, session))
      )
    } catch (error) {
      console.error('批量保存会话失败:', error)
    } finally {
      this.isSaving = false

      // 如果还有待保存的，继续处理
      if (this.saveQueue.size > 0) {
        setTimeout(() => this.processSaveQueue(), 200)
      }
    }
  }

  /**
   * 保存会话到存储
   */
  private async saveSessionToStorage(accountId: string, session: SessionInfo): Promise<void> {
    try {
      const ttl = Math.floor((session.expiry.getTime() - Date.now()) / 1000)
      if (ttl > 0) {
        await this.env.CACHE?.put(`elevenlabs:session:${accountId}`, JSON.stringify(session), {
          expirationTtl: ttl
        })
      }
    } catch (error) {
      console.error(`保存会话失败 [${accountId}]:`, error)
    }
  }

  /**
   * 从存储加载会话
   */
  private async loadSessionFromStorage(accountId: string): Promise<SessionInfo | null> {
    try {
      const sessionJson = await this.env.CACHE?.get(`elevenlabs:session:${accountId}`)
      if (!sessionJson) {
        return null
      }

      const session = JSON.parse(sessionJson) as SessionInfo
      return {
        ...session,
        expiry: new Date(session.expiry),
        createdAt: new Date(session.createdAt),
        lastValidated: session.lastValidated ? new Date(session.lastValidated) : undefined
      }
    } catch (error) {
      console.error(`加载会话失败 [${accountId}]:`, error)
      return null
    }
  }

  /**
   * 启动后台任务
   */
  private startBackgroundTasks(): void {
    // 定期清理过期缓存
    setInterval(() => {
      this.cleanupExpiredSessions()
    }, 300000) // 每5分钟清理一次
  }

  /**
   * 清理过期会话
   */
  private cleanupExpiredSessions(): void {
    const now = Date.now()

    for (const [accountId, session] of this.sessionCache.entries()) {
      if (session.expiry.getTime() <= now) {
        this.sessionCache.delete(accountId)
      }
    }

    this.lastCacheCleanup = now
  }

  /**
   * 记录性能指标
   */
  private recordPerformance(
    type: string,
    startTime: number,
    accountId: string,
    success: boolean
  ): void {
    // 简化的性能记录
    console.log(
      `Session ${type}: ${Date.now() - startTime}ms [${accountId}] ${success ? '✓' : '✗'}`
    )
  }

  /**
   * 验证token有效性
   */
  async validateToken(token: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.elevenlabs.io/v1/user', {
        headers: {
          Authorization: `Bearer ${token}`,
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        },
        signal: AbortSignal.timeout(5000)
      })

      return response.ok
    } catch (error) {
      console.error('验证token失败:', error)
      return false
    }
  }

  /**
   * 清除会话缓存
   */
  async clearSession(accountId: string): Promise<void> {
    this.sessionCache.delete(accountId)

    try {
      await this.env.CACHE?.delete(`elevenlabs:session:${accountId}`)
    } catch (error) {
      console.error(`清除会话失败 [${accountId}]:`, error)
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return {
      totalRequests: this.requestCount,
      cacheHitRate: this.requestCount > 0 ? this.cacheHits / this.requestCount : 0,
      sessionsInMemory: this.sessionCache.size,
      pendingSaves: this.saveQueue.size,
      activeLogins: this.loginPromises.size,
      activeRefreshes: this.refreshPromises.size
    }
  }
}
