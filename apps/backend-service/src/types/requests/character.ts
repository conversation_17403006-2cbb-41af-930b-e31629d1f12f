import type { CharacterCreateData, CharacterUpdateData, CharacterQueryParams, MediaQueryParams } from '../entities/character'

export interface CreateCharacterRequest {
  body: Omit<CharacterCreateData, 'userId'>
}

export interface UpdateCharacterRequest {
  params: { id: string }
  body: CharacterUpdateData
}

export interface GetCharacterRequest {
  params: { id: string }
}

export interface DeleteCharacterRequest {
  params: { id: string }
}

export interface GetCharacterListRequest {
  query: CharacterQueryParams
}

export interface GetCharacterMediaRequest {
  params: { characterId: string }
  query: Omit<MediaQueryParams, 'characterId'>
}

export interface GetCharacterImagesRequest {
  params: { characterId: string }
  query: {
    limit?: number
    offset?: number
  }
}

export interface GetCharacterVideosRequest {
  params: { characterId: string }
  query: {
    limit?: number
    offset?: number
  }
} 