export interface CharacterCreateData {
  userId: string
  name: string
  description?: string
  relationship?: string
  ethnicity?: string
  gender?: 'male' | 'female' | 'other'
  age?: string
  eyeColor?: string
  hairStyle?: string
  hairColor?: string
  faceShape?: string
  bodyType?: string
  breastSize?: string
  buttSize?: string
  personality?: string
  clothing?: string
  voice?: string
  voiceModelId?: string
  keywords: string
  prompt: string
  imageUrl?: string
  category?: string
  isPublic?: boolean
}

export interface CharacterUpdateData extends Partial<Omit<CharacterCreateData, 'userId'>> {}

export interface CharacterQueryParams {
  page?: number
  limit?: number
  offset?: number
  category?: string
}

export interface CharacterStats {
  currentCount: number
  maxAllowed: number
  canCreateMore: boolean
  membershipPlan?: string
}

export interface PermissionCheckResult {
  canCreate: boolean
  reason?: string
  currentCount?: number
  maxAllowed?: number
}

export interface MediaQueryParams {
  characterId: string
  mediaType?: 'image' | 'video' | 'audio'
  generationType?: 'multimodal_chat' | 'standalone' | 'template_based'
  status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  limit?: number
  offset?: number
}

export interface MediaGenerationData {
  id: string
  characterId: string
  chatId?: string | null
  messageId?: string | null
  mediaType: string
  generationType: string
  prompt?: string | null
  negativePrompt?: string | null
  inputImageUrl?: string | null
  outputUrls?: string[] | null
  status: string
  errorMessage?: string | null
  pointsUsed?: number | null
  generationTime?: number | null
  completedAt?: string | null
  createdAt: string
  updatedAt: string
  metadata?: any
}

export interface FormattedMediaImage {
  id: string
  url: string
  prompt?: string
  createdAt: string
  generationType: string
  metadata?: any
}

export interface FormattedMediaVideo {
  id: string
  url: string
  prompt?: string
  createdAt: string
  generationType: string
  metadata?: any
} 