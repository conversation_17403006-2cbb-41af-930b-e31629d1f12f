import type { ErrorCode } from './errors'

/**
 * 标准成功响应格式
 */
export interface ApiSuccessResponse<T = any> {
  success: true
  data: T
  message?: string
  timestamp?: string
  requestId?: string
}

/**
 * 标准错误响应格式
 */
export interface ApiErrorResponse {
  success: false
  error: string // 本地化的错误消息
  code: ErrorCode // 错误码
  data?: Record<string, any> // 附加错误数据
  timestamp: string // 时间戳
  requestId?: string // 请求ID
  stack?: string // 错误堆栈（仅开发环境）
}

/**
 * 通用API响应类型
 */
export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse

/**
 * 分页响应数据结构
 */
export interface PaginationData<T = any> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

/**
 * 分页成功响应
 */
export interface ApiPaginatedResponse<T = any> extends ApiSuccessResponse<PaginationData<T>> {}

/**
 * 创建成功响应的辅助函数
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  requestId?: string
): ApiSuccessResponse<T> {
  return {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
    requestId
  }
}

/**
 * 创建错误响应的辅助函数
 */
export function createErrorResponse(
  error: string,
  code: ErrorCode,
  data?: Record<string, any>,
  requestId?: string,
  stack?: string
): ApiErrorResponse {
  return {
    success: false,
    error,
    code,
    data,
    timestamp: new Date().toISOString(),
    requestId,
    stack
  }
}

/**
 * 创建分页响应的辅助函数
 */
export function createPaginatedResponse<T>(
  items: T[],
  total: number,
  page: number,
  pageSize: number,
  message?: string,
  requestId?: string
): ApiPaginatedResponse<T> {
  const totalPages = Math.ceil(total / pageSize)

  return createSuccessResponse<PaginationData<T>>(
    {
      items,
      total,
      page,
      pageSize,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    },
    message,
    requestId
  )
}

/**
 * 响应状态枚举
 */
export enum ResponseStatus {
  SUCCESS = 'success',
  ERROR = 'error',
  PARTIAL = 'partial'
}

/**
 * 批量操作响应格式
 */
export interface BatchOperationResponse<T = any> {
  success: boolean
  status: ResponseStatus
  total: number
  successful: number
  failed: number
  results: Array<{
    id: string | number
    success: boolean
    data?: T
    error?: string
    code?: ErrorCode
  }>
  timestamp: string
  requestId?: string
}
