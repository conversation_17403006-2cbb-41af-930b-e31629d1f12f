import type { Character } from '@/lib/db/schema'
import type { CharacterStats, MediaGenerationData, FormattedMediaImage, FormattedMediaVideo } from '../entities/character'

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginationInfo {
  page?: number
  limit?: number
  total?: number
  hasMore?: boolean
}

export interface CharacterResponse extends ApiResponse<Character> {}

export interface CharacterListResponse extends ApiResponse<Character[]> {
  pagination?: PaginationInfo
}

export interface CharacterStatsResponse extends ApiResponse<CharacterStats> {}

export interface MediaListResponse extends ApiResponse<{
  mediaGenerations: MediaGenerationData[]
  total: number
  hasMore: boolean
}> {}

export interface ImageListResponse extends ApiResponse<{
  images: FormattedMediaImage[]
  total: number
  hasMore: boolean
}> {}

export interface VideoListResponse extends ApiResponse<{
  videos: FormattedMediaVideo[]
  total: number
  hasMore: boolean
}> {} 