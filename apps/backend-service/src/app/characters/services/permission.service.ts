import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository';
import { checkUserCanCreateCharacter, getUserCharacterStats } from '@/lib/db/queries/membership-limits'
import type { Env } from '@/types/env'
import type { PermissionCheckResult, CharacterStats } from '@/types/entities/character'
import { CharacterRepository } from '../repositories'

export class PermissionService {
  private characterRepo: CharacterRepository

  constructor() {
    this.characterRepo = new CharacterRepository()
  }

  /**
   * 检查用户是否可以创建角色
   */
  async checkCanCreateCharacter(env: Env, userId: string): Promise<PermissionCheckResult> {
    return await checkUserCanCreateCharacter(env, userId)
  }

  /**
   * 检查用户是否可以更新角色
   */
  async checkCanUpdateCharacter(env: Env, characterId: string, supabaseUserId: string): Promise<boolean> {
    try {
      // 获取数据库用户信息
      const dbUser = await getUserBySupabaseId(env, supabaseUserId)
      if (!dbUser) {
        return false
      }

      // 获取角色信息
      const character = await this.characterRepo.findById(env, characterId)
      if (!character) {
        return false
      }

      // 检查权限（只有角色创建者可以更新）
      return character.userId === dbUser.id
    } catch (error) {
      console.error('检查更新权限失败:', error)
      return false
    }
  }

  /**
   * 检查用户是否可以删除角色
   */
  async checkCanDeleteCharacter(env: Env, characterId: string, supabaseUserId: string): Promise<boolean> {
    try {
      // 获取数据库用户信息
      const dbUser = await getUserBySupabaseId(env, supabaseUserId)
      if (!dbUser) {
        return false
      }

      // 获取角色信息
      const character = await this.characterRepo.findById(env, characterId)
      if (!character) {
        return false
      }

      // 检查权限（只有角色创建者可以删除）
      return character.userId === dbUser.id
    } catch (error) {
      console.error('检查删除权限失败:', error)
      return false
    }
  }

  /**
   * 获取用户角色统计信息
   */
  async getUserCharacterStats(env: Env, userId: string): Promise<CharacterStats> {
    return await getUserCharacterStats(env, userId)
  }
}