import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '@/lib/db/supabase-types'
import type { Character } from '@/lib/db/schema'
import type { Env } from '@/types/env'
import type { CharacterCreateData, CharacterUpdateData, CharacterQueryParams } from '@/types/entities/character'

export class CharacterRepository {
  /**
   * 创建新角色
   */
  async create(env: Env, data: CharacterCreateData): Promise<Character> {
    try {
      const supabase = getSupabase(env)
      const result = await supabase
        .from(TABLE_NAMES.character)
        .insert({
          user_id: data.userId,
          name: data.name,
          description: data.description,
          relationship: data.relationship || '',
          ethnicity: data.ethnicity || '',
          gender: data.gender,
          age: data.age || '',
          eye_color: data.eyeColor || '',
          hair_style: data.hairStyle || '',
          hair_color: data.hairColor || '',
          face_shape: data.faceShape || '',
          body_type: data.bodyType || '',
          breast_size: data.breastSize || '',
          butt_size: data.buttSize || '',
          personality: data.personality || '',
          clothing: data.clothing || '',
          voice: data.voice || '',
          voice_model_id: data.voiceModelId,
          keywords: data.keywords,
          prompt: data.prompt,
          image_url: data.imageUrl,
          category: data.category,
          is_public: data.isPublic || false
        })
        .select()

      const { data: characters, error } = handleSupabaseResult(result)
      if (error) throw error
      return characters![0]
    } catch (error) {
      console.error('创建角色失败', error)
      throw error
    }
  }

  /**
   * 根据ID获取角色
   */
  async findById(env: Env, id: string): Promise<Character | null> {
    try {
      const supabase = getSupabase(env)
      const result = await supabase.from(TABLE_NAMES.character).select('*').eq('id', id).single()

      const { data, error } = handleSupabaseSingleResult(result)
      if (error) throw error
      return data
    } catch (error) {
      console.error('获取角色详情失败', error)
      throw error
    }
  }

  /**
   * 根据用户ID获取角色列表
   */
  async findByUserId(env: Env, userId: string): Promise<Character[]> {
    try {
      const supabase = getSupabase(env)
      const result = await supabase
        .from(TABLE_NAMES.character)
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      const { data, error } = handleSupabaseResult(result)
      if (error) throw error
      return data || []
    } catch (error) {
      console.error('获取用户角色列表失败', error)
      throw error
    }
  }

  /**
   * 更新角色信息
   */
  async update(env: Env, id: string, data: CharacterUpdateData): Promise<Character> {
    try {
      const supabase = getSupabase(env)

      // 转换字段名为 snake_case
      const updateData: any = {}
      if (data.name !== undefined) updateData.name = data.name
      if (data.description !== undefined) updateData.description = data.description
      if (data.relationship !== undefined) updateData.relationship = data.relationship
      if (data.ethnicity !== undefined) updateData.ethnicity = data.ethnicity
      if (data.gender !== undefined) updateData.gender = data.gender
      if (data.age !== undefined) updateData.age = data.age
      if (data.eyeColor !== undefined) updateData.eye_color = data.eyeColor
      if (data.hairStyle !== undefined) updateData.hair_style = data.hairStyle
      if (data.hairColor !== undefined) updateData.hair_color = data.hairColor
      if (data.bodyType !== undefined) updateData.body_type = data.bodyType
      if (data.breastSize !== undefined) updateData.breast_size = data.breastSize
      if (data.buttSize !== undefined) updateData.butt_size = data.buttSize
      if (data.personality !== undefined) updateData.personality = data.personality
      if (data.clothing !== undefined) updateData.clothing = data.clothing
      if (data.voice !== undefined) updateData.voice = data.voice
      if (data.voiceModelId !== undefined) updateData.voice_model_id = data.voiceModelId
      if (data.keywords !== undefined) updateData.keywords = data.keywords
      if (data.prompt !== undefined) updateData.prompt = data.prompt
      if (data.imageUrl !== undefined) updateData.image_url = data.imageUrl
      if (data.category !== undefined) updateData.category = data.category
      if (data.isPublic !== undefined) updateData.is_public = data.isPublic

      updateData.updated_at = new Date().toISOString()

      const result = await supabase
        .from(TABLE_NAMES.character)
        .update(updateData)
        .eq('id', id)
        .select()

      const { data: characters, error } = handleSupabaseResult(result)
      if (error) throw error
      return characters![0]
    } catch (error) {
      console.error('更新角色失败', error)
      throw error
    }
  }

  /**
   * 删除角色
   */
  async delete(env: Env, id: string): Promise<void> {
    try {
      const supabase = getSupabase(env)
      const result = await supabase.from(TABLE_NAMES.character).delete().eq('id', id).select()

      const { error } = handleSupabaseResult(result)
      if (error) throw error
    } catch (error) {
      console.error('删除角色失败', error)
      throw error
    }
  }

  /**
   * 获取公开角色列表
   */
  async findPublic(env: Env, params: CharacterQueryParams): Promise<Character[]> {
    try {
      const supabase = getSupabase(env)
      const { limit = 20, offset = 0, category } = params

      let query = supabase
        .from(TABLE_NAMES.character)
        .select('*')
        .eq('is_public', true)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (category) {
        query = query.eq('category', category)
      }

      const result = await query
      const { data, error } = handleSupabaseResult(result)
      if (error) throw error
      return data || []
    } catch (error) {
      console.error('获取公开角色列表失败', error)
      throw error
    }
  }

  /**
   * 获取系统角色列表
   */
  async findSystem(env: Env, params: CharacterQueryParams): Promise<Character[]> {
    try {
      const supabase = getSupabase(env)
      const { limit = 50, offset = 0, category } = params

      // 系统角色的特殊用户ID
      const SYSTEM_USER_ID = '00000000-0000-0000-0000-000000000000'

      let query = supabase
        .from(TABLE_NAMES.character)
        .select('*')
        .eq('user_id', SYSTEM_USER_ID)
        .eq('is_public', true) // 确保是公开的
        .eq('is_active', true) // 确保是激活的
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (category) {
        query = query.eq('category', category)
      }

      const result = await query
      const { data, error } = handleSupabaseResult(result)
      if (error) throw error
      return data || []
    } catch (error) {
      console.error('获取系统角色列表失败', error)
      throw error
    }
  }

  /**
   * 获取角色信息（包含系统角色标识）
   */
  async findRoleInfoById(env: Env, roleId: string): Promise<(Character & { isSystemRole: boolean }) | null> {
    try {
      // 首先检查是否为UUID格式，如果是则查询自定义角色
      const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i

      if (uuidPattern.test(roleId)) {
        // 是UUID格式，查询自定义角色
        const characterData = await this.findById(env, roleId)
        if (characterData) {
          return {
            ...characterData,
            isSystemRole: false
          }
        }
      }

      // 如果不是UUID或者没有找到自定义角色，则返回null
      // 由调用方通过系统预设角色列表查找
      return null
    } catch (error) {
      console.error('获取角色信息失败:', error)
      throw error
    }
  }
} 