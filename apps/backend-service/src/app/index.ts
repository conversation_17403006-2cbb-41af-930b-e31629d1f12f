import { Hono } from 'hono'
import type { Env } from '@/types/env'

// 导入各个模块的路由
import { charactersRouter, mediaRouter } from './characters'

// 创建app路由实例
export const appRoutes = new Hono<{ Bindings: Env }>()

// 挂载各模块路由
appRoutes.route('/characters', charactersRouter)
appRoutes.route('/character-media', mediaRouter)

// 导出各模块的组件（如果其他地方需要单独使用）
export * from './characters'

// 未来可以在这里添加其他模块
// appRoutes.route('/users', userRouter)
// appRoutes.route('/chat', chatRouter)
// appRoutes.route('/payment', paymentRouter)
// etc... 