import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import type { Env } from '@/types/env'
import { ImageGenerationService } from '@/lib/image-generation'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import { permissionMiddleware, PermissionType } from '@/middleware/permission'
import { createServicePointsManager } from '@/lib/membership/service-points'
import { Insa3DGenerator } from '@/lib/image-generation/generators/insa3d'
import { generatePrompt } from '@/lib/ai/actions'

const app = new Hono<{ Bindings: Env }>()

// 图片生成请求验证
const generateImageSchema = z.object({
  prompt: z.string().min(1).max(1000),
  imageUrl: z.string().url().optional(),
  generator: z.enum(['yunwu', 'insa3d-v2', 'insa3d-v3']).default('yunwu'),
  waitForCompletion: z.boolean().default(false)
})

// 关键词优化+图片生成请求验证
const generateWithOptimizationSchema = z.object({
  keywords: z.string().min(1).max(500),
  generator: z.enum(['yunwu', 'insa3d-v2', 'insa3d-v3']).default('insa3d-v3'),
  waitForCompletion: z.boolean().default(false)
})

// 任务状态查询验证
const taskStatusSchema = z.object({
  taskId: z.string().min(1)
})

/**
 * 原来的 generate-imagev2 接口
 * POST /api/generate-imagev2
 */
app.post(
  '/generate-imagev2',
  languageMiddleware,
  authMiddleware,
  permissionMiddleware(PermissionType.IMAGE_GENERATION),
  async c => {
    try {
      const body = await c.req.json()
      const t = c.get('t')
      console.log('🔍 [DEBUG] generate-imagev2 请求体:', JSON.stringify(body, null, 2))

      const { inputs } = body

      // 验证inputs参数
      if (!inputs || typeof inputs !== 'object') {
        console.error('❌ [ERROR] inputs参数无效:', inputs)
        return c.json({ error: t('inputs_missing_or_invalid') }, 400)
      }

      // 从inputs中提取参数
      let prompt = ''
      let imageUrl: string | undefined

      // 解析inputs对象
      for (const [key, value] of Object.entries(inputs)) {
        const input = value as any
        if (input && input.title === 'prompt') {
          prompt = input.value
        } else if (input && input.title === 'Load Image') {
          imageUrl = input.value
        }
      }

      if (!prompt) {
        return c.json({ error: t('prompt_required') }, 400)
      }

      // 扣除图片生成积分
      const user = c.get('user')
      const pointsManager = createServicePointsManager(c.env)
      const generationId = `img-gen-v2-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

      const pointsResult = await pointsManager.consumeImageGenerationPoints(user.id, {
        imageCount: 1,
        generationId,
        customDescription: `图片生成 v2 - 提示词: ${prompt.substring(0, 50)}...`
      })

      if (!pointsResult.success) {
        return c.json(
          {
            success: false,
            error: pointsResult.error,
            errorCode: 'INSUFFICIENT_POINTS'
          },
          400
        )
      }

      const generator = new Insa3DGenerator(c.env, 'v2')
      const task = await generator.startGenerationTask({ prompt, imageUrl })

      return c.json({
        success: true,
        data: {
          taskId: task.taskId,
          status: 'pending',
          estimatedSteps: task.estimatedSteps,
          prompt: prompt,
          imageUrl: null,
          pointsConsumed: pointsResult.pointsConsumed || 10,
          remainingPoints: pointsResult.remainingPoints,
          generationId
        }
      })
    } catch (error) {
      const t = c.get('t')
      console.error('生成图片失败:', error)
      return c.json({ error: t('generation_failed') }, 500)
    }
  }
)

/**
 * 查询 generate-imagev2 任务状态
 * GET /api/generate-imagev2?taskId=xxx
 */
app.get('/generate-imagev2', languageMiddleware, authMiddleware, async c => {
  try {
    const taskId = c.req.query('taskId')
    const t = c.get('t')

    if (!taskId) {
      return c.json({ error: t('task_id_required') }, 400)
    }

    const generator = new Insa3DGenerator(c.env, 'v2')
    const task = await generator.getTaskStatus(taskId)

    return c.json({
      success: true,
      data: {
        taskId: task.taskId,
        status: task.status,
        progress: task.progress || 0,
        estimatedSteps: task.estimatedSteps || 0,
        completedSteps: task.completedSteps || 0,
        imageUrl: task.imageUrl,
        errorMessage: task.errorMessage
      }
    })
  } catch (error) {
    const t = c.get('t')
    console.error('查询任务状态失败:', error)
    return c.json({ error: t('query_status_failed') }, 500)
  }
})

/**
 * POST /api/image-generation/generate
 * 生成图片
 */
app.post(
  '/generate',
  languageMiddleware,
  authMiddleware,
  permissionMiddleware(PermissionType.IMAGE_GENERATION),
  zValidator('json', generateImageSchema),
  async c => {
    try {
      const { prompt, imageUrl, generator, waitForCompletion } = c.req.valid('json')
      const env = c.env
      const t = c.get('t')

      // 验证参数
      if (!prompt || prompt.trim().length === 0) {
        return c.json({ success: false, error: t('prompt_required') }, 400)
      }
      if (prompt.length > 1000) {
        return c.json({ success: false, error: t('prompt_too_long') }, 400)
      }

      // 扣除图片生成积分
      const user = c.get('user')
      const pointsManager = createServicePointsManager(env)
      const generationId = `img-gen-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

      const pointsResult = await pointsManager.consumeImageGenerationPoints(user.id, {
        imageCount: 1,
        generationId,
        customDescription: `图片生成 ${generator} - 提示词: ${prompt.substring(0, 50)}...`
      })

      if (!pointsResult.success) {
        return c.json(
          {
            success: false,
            error: pointsResult.error,
            errorCode: 'INSUFFICIENT_POINTS'
          },
          400
        )
      }

      const service = new ImageGenerationService(env)

      const result = await service.generateImage({ prompt, imageUrl }, generator, waitForCompletion)

      if (result.imageUrl) {
        // 同步生成完成
        return c.json({
          success: true,
          data: {
            type: 'completed',
            imageUrl: result.imageUrl,
            taskId: result.taskId,
            pointsConsumed: pointsResult.pointsConsumed || 10,
            remainingPoints: pointsResult.remainingPoints,
            generationId
          }
        })
      } else if (result.taskId) {
        // 异步任务已启动
        return c.json({
          success: true,
          data: {
            type: 'async',
            taskId: result.taskId,
            pointsConsumed: pointsResult.pointsConsumed || 10,
            remainingPoints: pointsResult.remainingPoints,
            generationId,
            message: '图片生成任务已启动，请使用任务ID查询状态'
          }
        })
      } else {
        throw new Error('生成结果异常')
      }
    } catch (error) {
      console.error('图片生成失败:', error)
      return c.json(
        {
          success: false,
          error: error instanceof Error ? error.message : '图片生成失败'
        },
        500
      )
    }
  }
)

/**
 * GET /api/image-generation/task/:taskId
 * 查询任务状态
 */
app.get('/task/:taskId', languageMiddleware, authMiddleware, async c => {
  try {
    const taskId = c.req.param('taskId')
    const t = c.get('t')

    if (!taskId) {
      return c.json(
        {
          success: false,
          error: t('task_id_required')
        },
        400
      )
    }

    const service = new ImageGenerationService(c.env)
    const task = await service.getTaskStatus(taskId)

    if (!task) {
      return c.json(
        {
          success: false,
          error: t('task_not_found')
        },
        404
      )
    }

    return c.json({
      success: true,
      data: task
    })
  } catch (error) {
    const t = c.get('t')
    console.error('查询任务状态失败:', error)
    return c.json(
      {
        success: false,
        error: error instanceof Error ? error.message : t('query_status_failed')
      },
      500
    )
  }
})

/**
 * DELETE /api/image-generation/task/:taskId
 * 取消任务
 */
app.delete('/task/:taskId', authMiddleware, async c => {
  try {
    const taskId = c.req.param('taskId')

    if (!taskId) {
      return c.json(
        {
          success: false,
          error: '任务ID不能为空'
        },
        400
      )
    }

    const service = new ImageGenerationService(c.env)
    await service.cancelTask(taskId)

    return c.json({
      success: true,
      message: '任务已取消'
    })
  } catch (error) {
    console.error('取消任务失败:', error)
    return c.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '取消任务失败'
      },
      500
    )
  }
})

/**
 * 获取任务列表
 * GET /api/image-generation/tasks
 */
app.get('/tasks', languageMiddleware, authMiddleware, async c => {
  try {
    // 这里可以从数据库获取用户的任务列表
    // 暂时返回空数组
    return c.json({ tasks: [] })
  } catch (error) {
    const t = c.get('t')
    console.error('获取任务列表失败:', error)
    return c.json({ error: '获取任务列表失败' }, 500)
  }
})

/**
 * POST /api/image-generation/generate-with-optimization
 * 关键词优化 + 图片生成一体化接口
 */
app.post(
  '/generate-with-optimization',
  languageMiddleware,
  authMiddleware,
  zValidator('json', generateWithOptimizationSchema),
  async c => {
    try {
      const { keywords, generator, waitForCompletion } = c.req.valid('json')
      const env = c.env
      const t = c.get('t')

      // 验证参数
      if (!keywords || keywords.trim().length === 0) {
        return c.json({ success: false, error: '关键词不能为空' }, 400)
      }
      if (keywords.length > 500) {
        return c.json({ success: false, error: '关键词过长' }, 400)
      }

      // 第一步：优化提示词
      let optimizedPrompt: string
      let optimizationFailed = false

      try {
        console.log('开始优化提示词:', keywords)

        optimizedPrompt = await generatePrompt(env, { keywords })
        console.log('提示词优化完成:', optimizedPrompt)
      } catch (optimizeError) {
        optimizationFailed = true
        console.error('优化提示词失败，使用fallback机制:', optimizeError)

        // 如果优化失败，使用基本的提示词构建逻辑
        const gender = keywords.includes('男性') || keywords.includes('male') ? 'male' : 'female'
        const ageGroup = keywords.includes('20多岁')
          ? 'in their 20s'
          : keywords.includes('30多岁')
          ? 'in their 30s'
          : keywords.includes('40多岁')
          ? 'in their 40s'
          : 'young adult'

        // 基于关键词构建更智能的fallback提示词
        optimizedPrompt = `A beautiful ${gender} ${ageGroup}, ${keywords.replace(
          /，/g,
          ', '
        )}, detailed portrait, professional photography, high quality, 4k resolution, detailed textures, vibrant colors, cinematic composition, facing camera, clear facial features, upper body visible`

        console.log('使用fallback提示词:', optimizedPrompt)
      }

      // 第二步：使用优化后的提示词生成图片
      const service = new ImageGenerationService(env)
      const result = await service.generateImage(
        { prompt: optimizedPrompt },
        generator,
        waitForCompletion
      )

      if (result.imageUrl) {
        // 同步生成完成
        return c.json({
          success: true,
          data: {
            type: 'completed',
            imageUrl: result.imageUrl,
            taskId: result.taskId,
            originalKeywords: keywords,
            optimizedPrompt: optimizedPrompt
          }
        })
      } else if (result.taskId) {
        // 异步任务已启动
        return c.json({
          success: true,
          data: {
            type: 'async',
            taskId: result.taskId,
            originalKeywords: keywords,
            optimizedPrompt: optimizedPrompt,
            message: '图片生成任务已启动，请使用任务ID查询状态'
          }
        })
      } else {
        throw new Error('生成结果异常')
      }
    } catch (error) {
      console.error('关键词优化+图片生成失败:', error)
      return c.json(
        {
          success: false,
          error: error instanceof Error ? error.message : '关键词优化+图片生成失败'
        },
        500
      )
    }
  }
)

/**
 * GET /api/image-generation/status/:messageId
 * 查询图片生成状态
 */
app.get('/status/:messageId', languageMiddleware, authMiddleware, async c => {
  try {
    const { messageId } = c.req.param()
    const user = c.get('user')
    const t = c.get('t')

    if (!user?.id) {
      return c.json({ error: t('user_not_logged_in') }, 401)
    }

    console.log('🔍 [STATUS] 查询图片生成状态:', messageId)

    // 查询消息及其附件
    const { getMessageById } = await import('@/modules/app/chat/repositories/message.repository')
    const message = await getMessageById(c.env, messageId)

    if (!message) {
      return c.json({ success: false, error: t('message_not_exist') }, 404)
    }

    const attachments = (message.attachments as any[]) || []

    // 检查完成的图片附件
    const completedImageAttachment = attachments.find(
      (att: any) =>
        att.contentType === 'image/png' ||
        att.contentType === 'image/jpeg' ||
        att.contentType === 'image/jpg'
    )

    if (completedImageAttachment) {
      console.log('✅ [STATUS] 检测到完成的图片附件')
      return c.json({
        success: true,
        data: {
          status: 'completed',
          progress: 100,
          stage: 'image_generation',
          message: '图片生成完成',
          finalImageUrl: completedImageAttachment.url
        }
      })
    }

    // 检查生成中的状态附件
    const generatingAttachments = attachments.filter(
      (att: any) => att.contentType?.includes('generating') && att.contentType?.includes('image')
    )

    if (generatingAttachments.length > 0) {
      // 获取最新的生成状态附件
      const latestGenerating = generatingAttachments.sort(
        (a: any, b: any) =>
          new Date(b.metadata?.timestamp || 0).getTime() -
          new Date(a.metadata?.timestamp || 0).getTime()
      )[0]

      const metadata = latestGenerating.metadata || {}
      console.log('🔄 [STATUS] 检测到生成中状态:', metadata.status)

      return c.json({
        success: true,
        data: {
          status: metadata.status || 'processing',
          progress: metadata.progress || 0,
          stage: 'image_generation',
          message: latestGenerating.name || t('preparing_image_generation')
        }
      })
    }

    // 没有找到任何相关状态
    console.log('❓ [STATUS] 未找到生成状态')
    return c.json({
      success: true,
      data: {
        status: 'idle',
        progress: 0,
        stage: 'image_generation',
        message: '等待开始生成'
      }
    })
  } catch (error) {
    const t = c.get('t')
    console.error('❌ [STATUS] 查询状态失败:', error)
    return c.json(
      {
        success: false,
        error: t('query_status_failed')
      },
      500
    )
  }
})

export default app
