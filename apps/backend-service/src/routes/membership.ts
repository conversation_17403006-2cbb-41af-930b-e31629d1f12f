import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import type { Env } from '@/types/env'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import {
  getMembershipPlans,
  getMembershipPlanById,
  getUserActiveSubscription,
  createUserSubscription,
  getUserSubscriptionHistory,
  getUserPoints,
  updateUserPoints,
  getUserPointsTransactions,
  getPointsPackages,
  getPointsPackageById,
  checkUserMembership,
  ensureUserExists
} from '@/lib/db/queries/membership'
import { createPointsService, POINTS_CONSUMPTION_CONFIG } from '@/lib/membership/points'
import { type PermissionType, checkPermission } from '@/middleware/permission'
import { getAllPointsConfig } from '@/lib/membership/config'
import {
  globalErrorHandler,
  requestIdMiddleware,
  throwBusinessError
} from '@/middleware/global-error-handler'
import { ErrorCode, BusinessError } from '@/types/errors'
import { createSuccessResponse, createErrorResponse } from '@/types/responses'

const app = new Hono<{ Bindings: Env }>()

// 应用全局中间件
app.use('*', requestIdMiddleware())
app.use('*', globalErrorHandler())

// 简单的用户ID缓存 (5分钟有效期)
const userIdCache = new Map<string, { localUserId: string; timestamp: number }>()
const CACHE_TTL = 5 * 60 * 1000 // 5分钟

// 辅助函数：从Supabase用户上下文解析本地用户ID（带缓存）
async function resolveLocalUserId(c: any): Promise<string> {
  const supabaseUser = c.get('user')
  if (!supabaseUser) {
    const t = c.get('t')
    throw new Error(t('membership.user_not_authenticated'))
  }

  // 检查缓存
  const cached = userIdCache.get(supabaseUser.id)
  const now = Date.now()

  if (cached && now - cached.timestamp < CACHE_TTL) {
    return cached.localUserId
  }

  // 确保用户在本地数据库中存在
  const localUserId = await ensureUserExists(c.env, supabaseUser.id, supabaseUser.email)

  // 更新缓存
  userIdCache.set(supabaseUser.id, { localUserId, timestamp: now })

  return localUserId
}

// ==================== 会员计划相关 ====================

/**
 * GET /api/membership/plans
 * 获取所有会员计划
 */
app.get('/plans', languageMiddleware, async c => {
  try {
    const plans = await getMembershipPlans(c.env)

    return c.json({
      success: true,
      data: plans
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取会员计划失败:', error)
    return c.json(
      {
        success: false,
        error: t('membership.get_plans_failed')
      },
      500
    )
  }
})

/**
 * GET /api/membership/plans/:id
 * 获取指定会员计划详情
 */
app.get('/plans/:id', languageMiddleware, async c => {
  try {
    const planId = c.req.param('id')
    const plan = await getMembershipPlanById(c.env, planId)
    const t = c.get('t')

    if (!plan) {
      return c.json(
        {
          success: false,
          error: t('membership.plan_not_found')
        },
        404
      )
    }

    return c.json({
      success: true,
      data: plan
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取会员计划详情失败:', error)
    return c.json(
      {
        success: false,
        error: t('membership.get_plan_details_failed')
      },
      500
    )
  }
})

// ==================== 用户订阅相关 ====================

/**
 * GET /api/membership/subscription
 * 获取用户当前订阅状态
 */
app.get('/subscription', authMiddleware, languageMiddleware, async c => {
  try {
    const localUserId = await resolveLocalUserId(c)
    const subscription = await getUserActiveSubscription(c.env, localUserId)

    return c.json({
      success: true,
      data: subscription
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取用户订阅状态失败:', error)
    return c.json(
      {
        success: false,
        error: t('membership.get_subscription_failed')
      },
      500
    )
  }
})

/**
 * POST /api/membership/subscription
 * 创建新的订阅
 */
const createSubscriptionSchema = z.object({
  planId: z.string().min(1, '会员计划ID不能为空'),
  paymentId: z.string().optional(),
  autoRenew: z.boolean().default(false)
})

app.post(
  '/subscription',
  authMiddleware,
  languageMiddleware,
  zValidator('json', createSubscriptionSchema),
  async c => {
    try {
      const localUserId = await resolveLocalUserId(c)
      const { planId, paymentId } = c.req.valid('json')
      const t = c.get('t')

      // 获取会员计划详情
      const plan = await getMembershipPlanById(c.env, planId)
      if (!plan) {
        return c.json(
          {
            success: false,
            error: t('membership.plan_not_found')
          },
          404
        )
      }

      // 计算订阅开始和结束时间
      const startDate = new Date()
      const endDate = new Date()
      endDate.setDate(endDate.getDate() + plan.durationDays)

      // 创建订阅
      const [subscription] = await createUserSubscription(c.env, {
        userId: localUserId,
        planId,
        startDate,
        endDate,
        status: 'active', // 假设支付已完成
        paymentId
      })

      // 给用户添加积分
      if (plan.pointsIncluded > 0) {
        await updateUserPoints(
          c.env,
          localUserId,
          plan.pointsIncluded,
          'earn',
          'subscription',
          subscription.id,
          `购买${plan.name}会员套餐获得${plan.pointsIncluded}积分`
        )
      }

      return c.json(
        {
          success: true,
          data: subscription,
          message: t('membership.subscription_created')
        },
        201
      )
    } catch (error) {
      const t = c.get('t')
      console.error('创建订阅失败:', error)
      return c.json(
        {
          success: false,
          error: t('membership.create_subscription_failed')
        },
        500
      )
    }
  }
)

/**
 * GET /api/membership/subscription/history
 * 获取用户订阅历史
 */
app.get('/subscription/history', authMiddleware, languageMiddleware, async c => {
  try {
    const localUserId = await resolveLocalUserId(c)
    const history = await getUserSubscriptionHistory(c.env, localUserId)

    return c.json({
      success: true,
      data: history
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取用户订阅历史失败:', error)
    return c.json(
      {
        success: false,
        error: t('membership.get_subscription_history_failed')
      },
      500
    )
  }
})

// ==================== 积分系统相关 ====================

/**
 * GET /api/membership/points
 * 获取用户积分余额
 */
app.get('/points', authMiddleware, languageMiddleware, async c => {
  try {
    const localUserId = await resolveLocalUserId(c)
    const points = await getUserPoints(c.env, localUserId)

    return c.json({
      success: true,
      data: points
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取用户积分失败:', error)
    return c.json(
      {
        success: false,
        error: t('membership.get_points_failed')
      },
      500
    )
  }
})

/**
 * GET /api/membership/points/transactions
 * 获取用户积分交易记录
 */
app.get('/points/transactions', authMiddleware, languageMiddleware, async c => {
  try {
    const localUserId = await resolveLocalUserId(c)
    const limit = Number.parseInt(c.req.query('limit') || '50')

    const transactions = await getUserPointsTransactions(c.env, localUserId, limit)

    return c.json({
      success: true,
      data: transactions
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取用户积分交易记录失败:', error)
    return c.json(
      {
        success: false,
        error: t('membership.get_transactions_failed')
      },
      500
    )
  }
})

/**
 * POST /api/membership/points/spend
 * 消费积分
 */
const spendPointsSchema = z.object({
  amount: z.number().min(1, '消费积分数量必须大于0'),
  source: z.enum(['generation', 'purchase', 'admin']),
  sourceId: z.string().optional(),
  description: z.string().optional()
})

app.post(
  '/points/spend',
  authMiddleware,
  languageMiddleware,
  zValidator('json', spendPointsSchema),
  async c => {
    try {
      const localUserId = await resolveLocalUserId(c)
      const { amount, source, sourceId, description } = c.req.valid('json')
      const t = c.get('t')

      const updatedPoints = await updateUserPoints(
        c.env,
        localUserId,
        amount,
        'spend',
        source,
        sourceId,
        description
      )

      return c.json({
        success: true,
        data: updatedPoints,
        message: t('membership.points_spent', { amount: amount.toString() })
      })
    } catch (error) {
      const t = c.get('t')
      console.error('消费积分失败:', error)
      return c.json(
        {
          success: false,
          error: error instanceof Error ? error.message : t('membership.spend_points_failed')
        },
        500
      )
    }
  }
)

// ==================== 积分套餐相关 ====================

/**
 * GET /api/membership/points/packages
 * 获取所有积分套餐
 */
app.get('/points/packages', languageMiddleware, async c => {
  try {
    const packages = await getPointsPackages(c.env)

    return c.json({
      success: true,
      data: packages
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取积分套餐失败:', error)
    return c.json(
      {
        success: false,
        error: t('membership.get_packages_failed')
      },
      500
    )
  }
})

/**
 * GET /api/membership/points/packages/:id
 * 获取指定积分套餐详情
 */
app.get('/points/packages/:id', languageMiddleware, async c => {
  try {
    const packageId = c.req.param('id')
    const pkg = await getPointsPackageById(c.env, packageId)
    const t = c.get('t')

    if (!pkg) {
      return c.json(
        {
          success: false,
          error: t('membership.package_not_found')
        },
        404
      )
    }

    return c.json({
      success: true,
      data: pkg
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取积分套餐详情失败:', error)
    return c.json(
      {
        success: false,
        error: t('membership.get_package_details_failed')
      },
      500
    )
  }
})

// ==================== 会员状态相关 ====================

/**
 * GET /api/membership/status
 * 获取用户完整的会员状态信息
 */
app.get('/status', authMiddleware, languageMiddleware, async c => {
  try {
    const localUserId = await resolveLocalUserId(c)

    // 并行获取用户的会员状态和积分信息
    const [membershipStatus, userPoints, activeSubscription] = await Promise.all([
      checkUserMembership(c.env, localUserId),
      getUserPoints(c.env, localUserId),
      getUserActiveSubscription(c.env, localUserId)
    ])

    // 计算剩余天数
    let daysRemaining = 0
    if (activeSubscription && activeSubscription.status === 'active') {
      const endDate = new Date(activeSubscription.endDate)
      const now = new Date()
      daysRemaining = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    }

    return c.json({
      success: true,
      data: {
        // 会员状态
        membership: {
          isMember: membershipStatus.isMember,
          subscription: membershipStatus.subscription,
          plan: membershipStatus.plan
        },
        // 积分信息
        points: {
          totalPoints: userPoints.totalPoints,
          usedPoints: userPoints.usedPoints,
          availablePoints: userPoints.availablePoints,
          lastUpdated: userPoints.lastUpdated
        },
        // 当前订阅详情
        currentSubscription: activeSubscription
          ? {
              id: activeSubscription.id,
              planId: activeSubscription.planId,
              startDate: activeSubscription.startDate,
              endDate: activeSubscription.endDate,
              status: activeSubscription.status,
              daysRemaining: Math.max(0, daysRemaining)
            }
          : null,
        // 权限信息
        permissions: {
          canAccessPremiumTemplates: membershipStatus.isMember,
          canUseAdvancedFeatures: membershipStatus.isMember,
          hasUnlimitedGenerations: false // 基于积分系统，没有无限制
        }
      }
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取用户会员状态失败:', error)
    return c.json(
      {
        success: false,
        error: t('membership.get_status_failed')
      },
      500
    )
  }
})

// ==================== 权限检查相关 ====================

/**
 * POST /api/membership/check-permission
 * 检查用户是否有权限执行特定操作
 */
const checkPermissionSchema = z.object({
  permissionType: z.enum([
    'text_chat',
    'character_create',
    'image_generation',
    'voice_generation',
    'script_purchase',
    'video_generation',
    'gallery_generation',
    'member_only'
  ]),
  pointsCost: z.number().optional(),
  requireMembership: z.boolean().optional()
})

app.post(
  '/check-permission',
  authMiddleware,
  languageMiddleware,
  zValidator('json', checkPermissionSchema),
  async c => {
    try {
      const localUserId = await resolveLocalUserId(c)
      const { permissionType, pointsCost, requireMembership } = c.req.valid('json')

      // 转换为内部权限类型
      const internalPermissionType = permissionType.toUpperCase() as PermissionType

      const permissionResult = await checkPermission(c.env, localUserId, internalPermissionType, {
        pointsCost,
        requireMembership
      })

      return c.json({
        success: true,
        data: {
          hasPermission: permissionResult.hasPermission,
          reason: permissionResult.reason,
          userType: permissionResult.userType,
          pointsAvailable: permissionResult.pointsAvailable,
          membershipInfo: permissionResult.membershipInfo
        }
      })
    } catch (error) {
      const t = c.get('t')
      console.error('权限检查失败:', error)
      return c.json(
        {
          success: false,
          error: t('membership.permission_check_failed')
        },
        500
      )
    }
  }
)

/**
 * GET /api/membership/verify-access/:feature
 * 验证用户对特定功能的访问权限（优化版本）
 */
app.get('/verify-access/:feature', authMiddleware, languageMiddleware, async c => {
  try {
    const localUserId = await resolveLocalUserId(c)
    const feature = c.req.param('feature').toUpperCase()
    const t = c.get('t')

    // 快速获取基本信息 - 并行执行关键查询
    const [userPointsInfo, activeSubscription] = await Promise.all([
      getUserPoints(c.env, localUserId).catch(() => ({
        availablePoints: 0,
        totalPoints: 0,
        usedPoints: 0
      })),
      getUserActiveSubscription(c.env, localUserId).catch(() => null)
    ])

    // 简单判断会员状态
    const isMember =
      activeSubscription?.status === 'active' &&
      activeSubscription?.endDate &&
      new Date(activeSubscription.endDate) > new Date()

    // 获取功能所需积分（角色创建功能不需要积分）
    let pointsRequired = 0
    if (feature !== 'CHARACTER_CREATE') {
      // 检查功能是否存在于积分配置中
      if (!(feature in POINTS_CONSUMPTION_CONFIG)) {
        throwBusinessError(ErrorCode.INVALID_PARAMETER, {
          feature,
          availableFeatures: Object.keys(POINTS_CONSUMPTION_CONFIG)
        })
      }
      pointsRequired =
        POINTS_CONSUMPTION_CONFIG[feature as keyof typeof POINTS_CONSUMPTION_CONFIG] || 0
    }

    // 简化的访问权限检查
    let canAccess = false
    let reason = ''
    let currentCount = 0
    let maxAllowed = 1

    if (feature === 'CHARACTER_CREATE') {
      // 角色创建功能的特殊处理 - 基于会员状态和角色数量限制
      try {
        // 导入角色创建权限检查
        const { checkUserCanCreateCharacter } = await import('@/lib/db/queries/membership-limits')
        const canCreateResult = await checkUserCanCreateCharacter(c.env, localUserId)

        canAccess = canCreateResult.canCreate
        reason =
          canCreateResult.reason ||
          (canAccess
            ? t('membership.can_create_character')
            : t('membership.character_limit_reached'))
        currentCount = canCreateResult.currentCount || 0
        maxAllowed = canCreateResult.maxAllowed || 1
      } catch (error) {
        console.error('检查角色创建权限失败:', error)
        throwBusinessError(ErrorCode.SYSTEM_ERROR, {
          originalError: error instanceof Error ? error.message : String(error)
        })
      }

      // 如果角色创建权限检查失败，抛出相应错误
      if (!canAccess) {
        throwBusinessError(ErrorCode.MEMBERSHIP_INSUFFICIENT_LEVEL, {
          userType: isMember ? 'member' : 'free',
          pointsAvailable: userPointsInfo.availablePoints,
          pointsRequired: 0,
          isMember,
          characterLimitInfo: {
            current: currentCount,
            max: maxAllowed,
            canCreate: false
          }
        })
      }
    } else if (feature === 'VOICE_GENERATION') {
      if (isMember && userPointsInfo.availablePoints >= pointsRequired) {
        canAccess = true
        reason = t('membership.member_points_sufficient')
      } else if (!isMember) {
        // 免费用户尝试使用语音生成 -> 需要会员权限
        throwBusinessError(ErrorCode.MEMBERSHIP_INSUFFICIENT_LEVEL, {
          userType: 'free',
          pointsAvailable: userPointsInfo.availablePoints,
          pointsRequired,
          isMember: false
        })
      } else {
        // 会员用户但积分不足
        throwBusinessError(ErrorCode.INSUFFICIENT_POINTS, {
          userType: 'member',
          pointsAvailable: userPointsInfo.availablePoints,
          pointsRequired,
          isMember: true
        })
      }
    } else {
      // 其他功能的权限检查（图片生成、视频生成等）
      if (isMember && userPointsInfo.availablePoints >= pointsRequired) {
        canAccess = true
        reason = t('membership.points_sufficient')
      } else if (!isMember) {
        // 免费用户尝试使用需要会员权限的功能
        throwBusinessError(ErrorCode.MEMBERSHIP_INSUFFICIENT_LEVEL, {
          userType: 'free',
          pointsAvailable: userPointsInfo.availablePoints,
          pointsRequired,
          isMember: false
        })
      } else {
        // 会员用户但积分不足
        throwBusinessError(ErrorCode.INSUFFICIENT_POINTS, {
          userType: 'member',
          pointsAvailable: userPointsInfo.availablePoints,
          pointsRequired,
          isMember: true
        })
      }
    }

    const response = createSuccessResponse({
      canAccess,
      reason,
      pointsRequired: feature === 'CHARACTER_CREATE' ? 0 : pointsRequired, // 角色创建不需要积分
      userType: isMember ? 'member' : 'free',
      pointsAvailable: userPointsInfo.availablePoints,
      isMember,
      // 角色创建特有的返回数据
      ...(feature === 'CHARACTER_CREATE' && {
        currentCount,
        maxAllowed,
        characterLimitInfo: {
          current: currentCount,
          max: maxAllowed,
          canCreate: canAccess
        }
      })
    })
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    const t = c.get('t')
    console.error('功能访问验证失败:', error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR, {
      originalError: error instanceof Error ? error.message : String(error)
    })
  }
})

/**
 * POST /api/membership/consume-points
 * 消费积分（用于各种功能）
 */
const consumePointsSchema = z.object({
  feature: z.string().min(1, '功能类型不能为空'),
  sourceId: z.string().optional(),
  customAmount: z.number().optional(),
  description: z.string().optional()
})

app.post(
  '/consume-points',
  authMiddleware,
  languageMiddleware,
  zValidator('json', consumePointsSchema),
  async c => {
    try {
      const localUserId = await resolveLocalUserId(c)
      const { feature, sourceId, customAmount, description } = c.req.valid('json')
      const t = c.get('t')

      const featureKey = feature.toUpperCase()

      // 检查功能是否存在
      if (!(featureKey in POINTS_CONSUMPTION_CONFIG)) {
        return c.json(
          {
            success: false,
            error: t('membership.unknown_feature')
          },
          400
        )
      }

      const pointsService = createPointsService(c.env)

      // 从数据库获取积分配置
      const { getFeaturePointsCost } = await import('@/lib/membership/config')
      const pointsRequired = customAmount || (await getFeaturePointsCost(c.env, featureKey))

      // 执行积分消费
      const result = await pointsService.consumePoints(
        localUserId,
        pointsRequired,
        'generation',
        sourceId,
        description || `使用${feature}功能消费${pointsRequired}积分`
      )

      if (!result.success) {
        return c.json(
          {
            success: false,
            error: result.error
          },
          400
        )
      }

      return c.json({
        success: true,
        data: {
          consumed: pointsRequired,
          remainingPoints: result.remainingPoints,
          transactionId: result.transactionId
        },
        message: t('membership.points_consumed', { amount: pointsRequired.toString() })
      })
    } catch (error) {
      const t = c.get('t')
      console.error('积分消费失败:', error)
      return c.json(
        {
          success: false,
          error: t('membership.consume_points_failed')
        },
        500
      )
    }
  }
)

/**
 * GET /api/membership/features/config
 * 获取功能积分消费配置
 */
app.get('/features/config', languageMiddleware, async c => {
  try {
    // 从数据库获取动态配置
    const pointsCosts = await getAllPointsConfig(c.env)

    return c.json({
      success: true,
      data: {
        pointsCosts,
        features: {
          IMAGE_GENERATION: {
            name: '图片生成',
            description: '生成AI图片',
            pointsCost: pointsCosts.IMAGE_GENERATION,
            requireMembership: false
          },
          VOICE_GENERATION: {
            name: '语音生成',
            description: '文字转语音',
            pointsCost: pointsCosts.VOICE_GENERATION,
            requireMembership: true
          },
          SCRIPT_PURCHASE: {
            name: '剧本购买',
            description: '购买互动剧本',
            pointsCost: pointsCosts.SCRIPT_PURCHASE,
            requireMembership: true
          },
          VIDEO_GENERATION: {
            name: '视频生成',
            description: '生成AI视频内容',
            pointsCost: pointsCosts.VIDEO_GENERATION,
            requireMembership: true
          },
          GALLERY_GENERATION: {
            name: '写真集生成',
            description: '生成角色写真集',
            pointsCost: pointsCosts.GALLERY_GENERATION,
            requireMembership: true
          }
        }
      }
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取功能配置失败:', error)
    return c.json(
      {
        success: false,
        error: t('membership.get_config_failed')
      },
      500
    )
  }
})

export default app
