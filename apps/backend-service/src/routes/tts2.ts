import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { authMiddleware } from '@/middleware/auth';
import type { Env } from '@/types/env';
import { z } from 'zod';
import { ElevenLabsService } from '@/lib/tts/elevenlabs-service';
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository';

const tts2 = new Hono<{ Bindings: Env }>();

// 根据Supabase用户ID获取数据库用户ID
async function getDbUserId(env: Env, supabaseUserId: string): Promise<string | null> {
  const dbUser = await getUserBySupabaseId(env, supabaseUserId);
  return dbUser?.id || null;
}

// 创建TTS任务的请求体验证
const generateTTSSchema = z.object({
  text: z.string().min(1).max(5000), // 限制文本长度
  messageId: z.string().optional(),
  chatId: z.string().optional(),
  voiceModelId: z.string().optional(), // 声音模型ID
});

// 同步生成TTS音频
tts2.post('/generate', authMiddleware, zValidator('json', generateTTSSchema), async (c) => {
  try {
    const supabaseUser = c.get('user');
    const { text, messageId, chatId, voiceModelId } = c.req.valid('json');
    const env = c.env;

    if (!supabaseUser) {
      return c.json({ success: false, message: '未找到用户信息' }, 401);
    }

    // 获取数据库用户ID
    const dbUserId = await getDbUserId(env, supabaseUser.id);
    if (!dbUserId) {
      return c.json({ success: false, message: '用户不存在' }, 404);
    }

    // 检查必要的环境变量
    if (!env.ELEVENLABS_API_KEY) {
      return c.json(
        {
          success: false,
          message: 'TTS服务暂时不可用',
        },
        503
      );
    }

    // 创建TTS服务实例
    const ttsService = new ElevenLabsService(env);

    // 同步处理TTS任务
    const result = await ttsService.generateAudioSync({
      text,
      messageId,
      chatId,
      voiceModelId,
      userId: dbUserId,
    });

    return c.json({
      success: true,
      data: {
        audioUrl: result.audioUrl,
        status: 'completed',
      },
    });
  } catch (error) {
    console.error('生成音频失败:', error);
    return c.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '生成音频失败',
      },
      500
    );
  }
});

// 获取可用的声音列表
tts2.get('/voices', authMiddleware, async (c) => {
  try {
    const env = c.env;

    if (!env.ELEVENLABS_API_KEY) {
      return c.json(
        {
          success: false,
          message: 'TTS服务暂时不可用',
        },
        503
      );
    }

    const ttsService = new ElevenLabsService(env);
    const voices = await ttsService.getAvailableVoices();

    return c.json({
      success: true,
      data: {
        voices,
      },
    });
  } catch (error) {
    console.error('获取声音列表失败:', error);
    return c.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '获取声音列表失败',
      },
      500
    );
  }
});

export default tts2;
