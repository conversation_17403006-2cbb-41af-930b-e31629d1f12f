import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import type { Env } from '@/types/env'
import type { ImageGenerationTask, ImageGenerationResponse } from '@/types/image'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import { permissionMiddleware, PermissionType } from '@/middleware/permission'
import { createServicePointsManager } from '@/lib/membership/service-points'
import { generateUUID } from '@/lib/utils'
import { getCachedDbUserId } from '@/lib/cache/cache-utils'
import { createMediaGeneration } from '@/lib/db/queries/media-generation'
import { getMessageById, updateMessageAttachments } from '@/modules/app/chat/repositories/message.repository'

// 消息附件类型
interface MessageAttachment {
  url: string
  name: string
  contentType: string
  metadata?: any
}

// 创建 Hono 应用
const imageGenerationRunpod = new Hono<{ Bindings: Env }>()

// 图片生成参数验证
const generateImageSchema = z.object({
  messageId: z.string().min(1, '消息ID不能为空'),
  chatId: z.string().min(1, '聊天ID不能为空'),
  prompt: z.string().min(1, '提示词不能为空').max(2000, '提示词过长'),
  characterAvatar: z.string().url().optional(),
  characterId: z.string().optional(),
  metadata: z
    .object({
      width: z.number().int().min(256).max(2048).optional(),
      height: z.number().int().min(256).max(2048).optional()
    })
    .optional()
})

/**
 * POST /api/image-generation-runpod/generate
 * RunPod图片生成主接口 - 发送任务到RunPod队列
 */
imageGenerationRunpod.post(
  '/generate',
  languageMiddleware,
  authMiddleware,
  permissionMiddleware(PermissionType.IMAGE_GENERATION),
  zValidator('json', generateImageSchema),
  async c => {
    try {
      const supabaseUser = c.get('user')
      const env = c.env
      const t = c.get('t')
      const { messageId, chatId, prompt, characterAvatar, characterId, metadata } =
        c.req.valid('json')

      // 验证参数
      if (!messageId || messageId.trim().length === 0) {
        return c.json({ success: false, message: t('message_id_required') }, 400)
      }
      if (!chatId || chatId.trim().length === 0) {
        return c.json({ success: false, message: t('chat_id_required') }, 400)
      }
      if (!prompt || prompt.trim().length === 0) {
        return c.json({ success: false, message: t('prompt_required') }, 400)
      }
      if (prompt.length > 2000) {
        return c.json({ success: false, message: t('prompt_too_long') }, 400)
      }

      if (!supabaseUser) {
        return c.json({ success: false, message: t('user_info_not_found') }, 401)
      }

      // 获取数据库用户ID（带缓存）
      const dbUserId = await getCachedDbUserId(env, supabaseUser.id)
      if (!dbUserId) {
        return c.json({ success: false, message: t('user_not_exist') }, 404)
      }

      console.log('🎨 [RunPod] 接收图片生成请求:', {
        messageId,
        chatId,
        promptLength: prompt.length,
        hasCharacterAvatar: !!characterAvatar,
        userId: dbUserId
      })

      // 创建积分管理器
      const pointsManager = createServicePointsManager(env)

      const generationId = generateUUID()

      // 扣除积分（使用数据库配置的固定积分消费）
      const pointsResult = await pointsManager.consumeImageGenerationPoints(dbUserId, {
        imageCount: 1,
        generationId,
        customDescription: `图片生成 - 提示词: ${prompt.substring(0, 50)}...`
      })

      // 获取实际消费的积分数量
      const totalPoints = pointsResult.pointsConsumed || 0

      if (!pointsResult.success) {
        return c.json(
          {
            success: false,
            error: pointsResult.error,
            errorCode: 'INSUFFICIENT_POINTS',
            data: {
              requiredPoints: totalPoints,
              remainingPoints: pointsResult.remainingPoints || 0
            }
          },
          400
        )
      }

      // 添加生成状态附件到消息
      await addGeneratingAttachment(env, messageId)

      // 使用生成ID作为任务ID
      const taskId = generationId

      // 创建媒体生成记录
      const mediaGenerationRecord = await createMediaGeneration(env, {
        userId: dbUserId,
        characterId, // 直接使用前端传递的 characterId
        chatId,
        messageId,
        mediaType: 'image',
        generationType: 'multimodal_chat',
        prompt,
        inputImageUrl: characterAvatar,
        pointsUsed: totalPoints,
        metadata: {
          taskId,
          service: 'runpod',
          runpodEndpoint: env.RUNPOD_API_ENDPOINT,
          width: metadata?.width || 1024,
          height: metadata?.height || 1440,
          ...metadata
        }
      })

      console.log('📊 [RunPod] 媒体生成记录已创建:', {
        id: mediaGenerationRecord.id,
        generationId: generationId,
        pointsUsed: totalPoints
      })

      // 创建队列任务
      const task: ImageGenerationTask = {
        taskId,
        messageId,
        chatId,
        prompt,
        characterAvatar,
        userId: dbUserId,
        timestamp: Date.now(),
        metadata: {
          ...metadata,
          mediaGenerationId: mediaGenerationRecord.id,
          pointsUsed: totalPoints
        }
      }

      // 发送任务到 RunPod 图片生成队列
      try {
        await env.IMAGE_RUNPOD_GENERATION_QUEUE.send(task)
        console.log('✅ [RunPod] 图片生成任务已发送到队列:', taskId)
      } catch (queueError) {
        console.error('❌ [RunPod] 发送图片生成任务到队列失败:', queueError)

        // 队列发送失败，退还积分
        try {
          await refundPointsForFailedGeneration(env, dbUserId, totalPoints, generationId)
          console.log('✅ [RunPod] 积分已退还:', totalPoints)
        } catch (refundError) {
          console.error('❌ [RunPod] 积分退还失败:', refundError)
        }

        // 清理初始附件
        await cleanupGeneratingAttachment(env, messageId)
        return c.json(
          {
            success: false,
            message: t('queue_send_failed'),
            data: {
              pointsRefunded: totalPoints
            }
          },
          500
        )
      }

      // 立即返回成功响应，包含积分信息
      const response: ImageGenerationResponse = {
        success: true,
        taskId,
        message: `${t('image_generation_task_started')}，${t('generation_task_points_consumed', {
          points: totalPoints.toString()
        })}，请等待完成`
      }

      console.log('✅ [RunPod] 图片生成任务响应:', {
        taskId,
        pointsConsumed: totalPoints
      })

      return c.json(response)
    } catch (error) {
      console.error('❌ [RunPod] 图片生成接口错误:', error)
      return c.json(
        {
          success: false,
          message: '服务器内部错误，请稍后重试'
        },
        500
      )
    }
  }
)

/**
 * GET /api/image-generation-runpod/health
 * RunPod 健康检查接口
 */
imageGenerationRunpod.get('/health', languageMiddleware, async c => {
  try {
    const env = c.env
    const t = c.get('t')

    console.log('🔍 [RunPod健康检查] 开始检查环境配置')
    console.log('🔍 [RunPod健康检查] WORKER_ENV:', env.WORKER_ENV)
    console.log('🔍 [RunPod健康检查] NODE_ENV:', env.NODE_ENV)

    // 检查必要的环境变量
    const runpodToken = env.RUNPOD_API_TOKEN
    const runpodEndpoint = env.RUNPOD_API_ENDPOINT

    console.log('🔍 [RunPod健康检查] RUNPOD_API_TOKEN 存在:', !!runpodToken)
    console.log('🔍 [RunPod健康检查] RUNPOD_API_ENDPOINT 存在:', !!runpodEndpoint)

    if (runpodToken) {
      console.log('🔍 [RunPod健康检查] RUNPOD_API_TOKEN 长度:', runpodToken.length)
      console.log(
        '🔍 [RunPod健康检查] RUNPOD_API_TOKEN 前缀:',
        runpodToken.substring(0, 10) + '...'
      )
    }

    if (runpodEndpoint) {
      console.log('🔍 [RunPod健康检查] RUNPOD_API_ENDPOINT:', runpodEndpoint)
    }

    const missingVars = []
    if (!runpodToken) missingVars.push('RUNPOD_API_TOKEN')
    if (!runpodEndpoint) missingVars.push('RUNPOD_API_ENDPOINT')

    if (missingVars.length > 0) {
      console.error('❌ [RunPod健康检查] 缺少环境变量:', missingVars)
      return c.json(
        {
          success: false,
          data: {
            service: 'image-generation-runpod',
            status: 'unhealthy',
            error: `缺少环境变量: ${missingVars.join(', ')}`,
            workerEnv: env.WORKER_ENV,
            nodeEnv: env.NODE_ENV
          }
        },
        500
      )
    }

    // 检查队列绑定
    const hasRunpodQueue = !!env.IMAGE_RUNPOD_GENERATION_QUEUE
    console.log('🔍 [RunPod健康检查] IMAGE_RUNPOD_GENERATION_QUEUE 绑定:', hasRunpodQueue)

    // 尝试调用 RunPod API 健康检查
    let apiHealthy = false
    let apiError = null

    try {
      console.log('🔍 [RunPod健康检查] 测试 RunPod API 连接...')
      const testResponse = await fetch(`https://api.runpod.ai/v2/${runpodEndpoint}/health`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${runpodToken}`
        },
        signal: AbortSignal.timeout(10000) // 10秒超时
      })

      console.log('🔍 [RunPod健康检查] RunPod API 响应状态:', testResponse.status)

      if (testResponse.ok) {
        apiHealthy = true
        console.log('✅ [RunPod健康检查] RunPod API 连接正常')
      } else {
        const errorText = await testResponse.text()
        apiError = `HTTP ${testResponse.status}: ${errorText}`
        console.error('❌ [RunPod健康检查] RunPod API 错误:', apiError)
      }
    } catch (error) {
      apiError = error instanceof Error ? error.message : String(error)
      console.error('❌ [RunPod健康检查] RunPod API 连接异常:', apiError)
    }

    return c.json({
      success: true,
      data: {
        service: 'image-generation-runpod',
        status: apiHealthy ? 'healthy' : 'degraded',
        environment: {
          workerEnv: env.WORKER_ENV,
          nodeEnv: env.NODE_ENV
        },
        runpodApi: {
          endpoint: runpodEndpoint,
          tokenConfigured: !!runpodToken,
          tokenLength: runpodToken?.length || 0,
          healthy: apiHealthy,
          error: apiError
        },
        queue: {
          bound: hasRunpodQueue,
          name: 'IMAGE_RUNPOD_GENERATION_QUEUE'
        },
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('❌ [RunPod健康检查] 检查失败:', error)
    return c.json(
      {
        success: false,
        message: '健康检查失败',
        error: error instanceof Error ? error.message : String(error)
      },
      500
    )
  }
})

/**
 * 添加生成状态附件到消息
 */
async function addGeneratingAttachment(env: Env, messageId: string): Promise<void> {
  try {
    // 查询现有附件
    const currentMessages = await getMessageById(env, messageId)

    if (!currentMessages) {
      console.warn('⚠️ [RunPod] 消息不存在，无法添加附件')
      return
    }

    const currentMessage = currentMessages

    // 解析现有附件
    let existingAttachments: MessageAttachment[] = []
    if (currentMessage.attachments) {
      existingAttachments = Array.isArray(currentMessage.attachments)
        ? (currentMessage.attachments as MessageAttachment[])
        : JSON.parse(currentMessage.attachments as string)
    }

    // 添加生成状态附件
    const generatingAttachment: MessageAttachment = {
      url: 'generating://image',
      name: '正在生成图片...',
      contentType: 'image/generating',
      metadata: {
        status: 'starting',
        progress: 0,
        timestamp: new Date().toISOString(),
        service: 'runpod'
      }
    }

    const updatedAttachments = [...existingAttachments, generatingAttachment]

    // 更新数据库
    await updateMessageAttachments(env, messageId, updatedAttachments)

    console.log('✅ [RunPod] 生成状态附件已添加')
  } catch (error) {
    console.error('❌ [RunPod] 添加生成状态附件失败:', error)
  }
}

/**
 * 清理生成状态附件
 */
async function cleanupGeneratingAttachment(env: Env, messageId: string): Promise<void> {
  try {
    // 查询现有附件
    const currentMessages = await getMessageById(env, messageId)

    if (!currentMessages) {
      return
    }

    const currentMessage = currentMessages

    // 解析现有附件
    let existingAttachments: MessageAttachment[] = []
    if (currentMessage.attachments) {
      existingAttachments = Array.isArray(currentMessage.attachments)
        ? (currentMessage.attachments as MessageAttachment[])
        : JSON.parse(currentMessage.attachments as string)
    }

    // 移除生成状态附件
    const filteredAttachments = existingAttachments.filter(
      (att: MessageAttachment) => !att.contentType?.startsWith('image/generating')
    )

    // 更新数据库
    await updateMessageAttachments(env, messageId, filteredAttachments)

    console.log('✅ [RunPod] 生成状态附件已清理')
  } catch (error) {
    console.error('❌ [RunPod] 清理生成状态附件失败:', error)
  }
}

/**
 * 退还积分（生成失败时）
 */
async function refundPointsForFailedGeneration(
  env: Env,
  userId: string,
  points: number,
  generationId: string
): Promise<void> {
  const pointsManager = createServicePointsManager(env)

  const refundResult = await pointsManager.refundPoints(userId, {
    amount: points,
    source: 'refund',
    sourceId: generationId,
    description: `图片生成失败退还 - 生成ID: ${generationId}`
  })

  if (!refundResult.success) {
    console.error('❌ [RunPod] 积分退还失败:', refundResult.error)
    throw new Error('积分退还失败')
  }
}

export default imageGenerationRunpod
