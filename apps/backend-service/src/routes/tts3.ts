import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import { permissionMiddleware, PermissionType } from '@/middleware/permission'
import { languageMiddleware } from '@/middleware/language'
import {
  globalErrorHandler,
  requestIdMiddleware,
  throwBusinessError
} from '@/middleware/global-error-handler'
import { ErrorCode, BusinessError } from '@/types/errors'
import { createSuccessResponse, createErrorResponse } from '@/types/responses'
import { getErrorMessage } from '@/i18n/messages/errors'
import type { SupportedLanguage } from '@/i18n/config'
import type { Env } from '@/types/env'
import { z } from 'zod'
import { createServicePointsManager } from '@/lib/membership/service-points'
import { generateUUID } from '@/lib/utils'
import type { AudioProcessingTask } from '@/types/audio'
import { uploadToR2, IMAGE_UPLOAD_OPTIONS, getR2ConfigFromEnv } from '@/lib/utils/r2-upload'
import { getCachedDbUserId, getCachedRealVoiceId } from '@/lib/cache/cache-utils'
import IntegratedElevenLabsService from '@/lib/elevenlabs/integrated-service'
import { TTSService } from '@/lib/tts/tts-service'

const tts3 = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
  }
}>()

// 应用全局中间件
tts3.use('*', requestIdMiddleware())
tts3.use('*', globalErrorHandler())

// 集成的 TTS 服务实例缓存
const serviceInstances = new Map<string, IntegratedElevenLabsService>()

/**
 * 获取或创建 TTS 服务实例
 */
function getTTSService(env: Env): IntegratedElevenLabsService {
  const key = 'default' // 可以根据环境或其他因素创建不同的实例

  if (!serviceInstances.has(key)) {
    const service = new IntegratedElevenLabsService(env, {
      maxRetries: 2,
      retryDelayMs: 500,
      connectionTimeoutMs: 10000,
      requestTimeoutMs: 45000,
      enableRequestDeduplication: true
    })
    serviceInstances.set(key, service)
  }

  return serviceInstances.get(key)!
}

// 创建TTS任务的请求体验证
const generateTTSSchema = z.object({
  text: z.string().min(1).max(5000),
  messageId: z.string().optional(),
  chatId: z.string().optional(),
  voiceModelId: z.string().optional()
})

/**
 * 创建音频拦截器，收集数据并提交到队列处理
 */
function createSimpleAudioInterceptor(
  env: Env,
  ctx: any,
  params: {
    messageId?: string
    chatId?: string
    text: string
    t?: (key: string, params?: Record<string, string | number>) => string
  }
): TransformStream<Uint8Array, Uint8Array> {
  const chunks: Uint8Array[] = []
  let totalBytes = 0

  return new TransformStream({
    transform(chunk, controller) {
      // 收集音频数据但不阻塞
      chunks.push(chunk)
      totalBytes += chunk.length

      // 立即传递给客户端
      controller.enqueue(chunk)
    },

    flush() {
      // 流传输完成，记录信息
      if (params.t) {
        console.log(params.t('tts3.audio_stream_completed'))
        console.log(params.t('tts3.audio_stats', { chunks: chunks.length, bytes: totalBytes }))
      } else {
        console.log('🎵 音频流传输完成')
        console.log('📊 总计:', chunks.length, '个块,', totalBytes, 'bytes')
      }

      // 如果有消息ID和聊天ID，提交到队列进行后台处理
      if (params.messageId && params.chatId && chunks.length > 0) {
        // 异步提交到队列，不阻塞响应
        ctx.waitUntil(submitAudioToQueue(env, chunks, params))
      }
    }
  })
}

/**
 * 提交音频数据到队列进行后台处理
 * 新方案：先上传到临时存储，队列只传递URL
 */
async function submitAudioToQueue(
  env: Env,
  chunks: Uint8Array[],
  params: {
    messageId?: string
    chatId?: string
    text: string
    t?: (key: string, params?: Record<string, string | number>) => string
  }
): Promise<void> {
  try {
    if (params.t) {
      console.log(params.t('tts3.submit_audio_start'))
    } else {
      console.log('🔄 开始提交音频数据到队列...')
    }

    // 合并所有音频块
    const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0)
    const audioData = new Uint8Array(totalLength)
    let offset = 0

    for (const chunk of chunks) {
      audioData.set(chunk, offset)
      offset += chunk.length
    }

    if (params.t) {
      console.log(params.t('tts3.audio_data_size', { size: totalLength }))
    } else {
      console.log('📊 音频数据大小:', totalLength, 'bytes')
    }

    // 上传到临时存储
    const tempAudioUrl = await uploadAudioToTempStorage(env, audioData.buffer)
    if (params.t) {
      console.log(params.t('tts3.audio_uploaded', { url: tempAudioUrl }))
    } else {
      console.log('📤 音频已上传到临时存储:', tempAudioUrl)
    }

    // 创建队列任务（只包含URL和元数据）
    const audioTask: AudioProcessingTask = {
      taskId: generateUUID(),
      audioUrl: tempAudioUrl,
      messageId: params.messageId,
      chatId: params.chatId,
      text: params.text,
      timestamp: Date.now(),
      isTemporary: true
    }

    // 提交到音频处理队列
    await env.AUDIO_PROCESSING_QUEUE.send(audioTask)
    if (params.t) {
      console.log(params.t('tts3.task_submitted', { taskId: audioTask.taskId }))
    } else {
      console.log('✅ 音频任务已提交到队列:', audioTask.taskId)
    }
  } catch (error) {
    if (params.t) {
      console.error(params.t('tts3.submit_task_failed', { error: String(error) }))
    } else {
      console.error('❌ 提交音频任务到队列失败:', error)
    }
    // 不抛出错误，避免影响主流程
  }
}

/**
 * 上传音频到临时存储
 */
async function uploadAudioToTempStorage(env: Env, audioBuffer: ArrayBuffer): Promise<string> {
  const r2Config = getR2ConfigFromEnv(env)
  if (!r2Config) {
    throw new Error('tts3.r2_config_incomplete')
  }

  const tempTaskId = generateUUID()
  const fileName = `temp_tts_${tempTaskId}.mp3`
  const uploadOptions = {
    ...IMAGE_UPLOAD_OPTIONS,
    fileName,
    folder: 'audio/temp', // 临时文件夹
    allowedTypes: ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'application/octet-stream'],
    maxSize: 50 * 1024 * 1024 // 50MB
  }

  const result = await uploadToR2(audioBuffer, r2Config, uploadOptions)

  if (!result.success || !result.url) {
    throw new Error(result.error || 'tts3.upload_temp_audio_failed')
  }

  return result.url
}

// 音频处理函数已移动到 index.ts 的 Queue Consumer 中

// 流式生成TTS音频（主接口）
tts3.post(
  '/generate',
  authMiddleware,
  permissionMiddleware(PermissionType.VOICE_GENERATION),
  languageMiddleware,
  zValidator('json', generateTTSSchema),
  async c => {
    try {
      const supabaseUser = c.get('user')
      const { text, voiceModelId, messageId, chatId } = c.req.valid('json')
      const env = c.env

      const t = c.get('t')

      if (!supabaseUser) {
        throwBusinessError(ErrorCode.AUTH_UNAUTHORIZED)
      }

      // 获取数据库用户ID（带缓存）
      const dbUserId = await getCachedDbUserId(env, supabaseUser.id)
      if (!dbUserId) {
        throwBusinessError(ErrorCode.USER_NOT_FOUND)
      }

      // 获取真正的 voice ID 用于积分计算（带缓存）
      const realVoiceId = await getCachedRealVoiceId(env, voiceModelId)

      // 创建积分管理器并扣除积分
      // const pointsManager = createServicePointsManager(env)
      const ttsTaskId = generateUUID() // 使用标准UUID格式

      // 扣除TTS积分（使用真正的 voice ID）
      // const pointsResult = await pointsManager.consumeTTSPoints(dbUserId, {
      //   textLength: text.length,
      //   voiceModel: realVoiceId,
      //   taskId: ttsTaskId,
      //   customDescription: `TTS语音生成 - 文本${text.length}字符`
      // })

      // if (!pointsResult.success) {
      //   return c.json(
      //     {
      //       success: false,
      //       error: pointsResult.error,
      //       errorCode: 'INSUFFICIENT_POINTS'
      //     },
      //     400
      //   )
      // }

      console.log(t('tts3.tts_generation_start', { length: text.length }))
      console.log(
        t('tts3.message_chat_info', { messageId: messageId || 'none', chatId: chatId || 'none' })
      )

      try {
        // 使用集成的 TTS 服务
        console.log(t('tts3.using_integrated_service'))

        const ttsService = getTTSService(env)

        const ttsRequest = {
          text: text,
          voice_id: realVoiceId,
          model_id: 'eleven_v3',
          stability: 0.5,
          use_speaker_boost: true
        }

        // 直接调用集成服务的流式方法
        const response = await ttsService.generateTTSStream(ttsRequest)

        console.log(t('tts3.integrated_service_success'))

        // 创建音频拦截器用于后台处理
        const audioInterceptor = createSimpleAudioInterceptor(env, c.executionCtx, {
          messageId,
          chatId,
          text,
          t: c.get('t')
        })

        // 将响应流通过拦截器
        const interceptedStream = response.body?.pipeThrough(audioInterceptor)

        // 返回优化的流式响应
        return new Response(interceptedStream, {
          headers: {
            'Content-Type': 'audio/mpeg',
            'Transfer-Encoding': 'chunked',
            'X-Service': 'integrated-tts-v3',
            'X-Audio-Source': 'integrated-tts',
            'X-Response-Time': response.headers.get('X-Response-Time') || '0',
            'X-Account': response.headers.get('X-Account') || 'unknown',
            'X-Task-Id': ttsTaskId
          }
        })
      } catch (ttsError) {
        console.error(t('tts3.fallback_to_fish_audio', { error: String(ttsError) }))

        // 降级到 Fish Audio（使用 tts-service.ts）
        const { TTSService } = await import('@/lib/tts/tts-service')
        const fallbackService = new TTSService(env)

        try {
          console.log(t('tts3.using_fish_audio'))

          // generateAudioSync 会自动处理上传和附件更新
          const result = await fallbackService.generateAudioSync({
            text,
            userId: dbUserId,
            voiceModelId,
            messageId,
            chatId
          })

          console.log(t('tts3.fish_audio_completed', { url: result.audioUrl }))

          // 获取已上传的音频文件并返回流
          const audioResponse = await fetch(result.audioUrl)
          if (!audioResponse.ok) {
            throw new Error(t('tts3.fallback_audio_failed'))
          }

          // 返回音频流
          return new Response(audioResponse.body, {
            headers: {
              'Content-Type': 'audio/mpeg',
              'Transfer-Encoding': 'chunked',
              'X-Fallback-Service': 'fish-audio',
              'X-Audio-Source': 'fish-audio-fallback',
              // 'X-Points-Consumed': pointsResult.pointsConsumed?.toString() || '5',
              // 'X-Remaining-Points': (pointsResult.remainingPoints ?? 0).toString(),
              'X-Task-Id': ttsTaskId
            }
          })
        } catch (fallbackError) {
          console.error(t('tts3.fish_audio_failed', { error: String(fallbackError) }))
          throwBusinessError(ErrorCode.TTS_SERVICE_ERROR, {
            primaryError: ttsError instanceof Error ? ttsError.message : String(ttsError),
            fallbackError:
              fallbackError instanceof Error ? fallbackError.message : String(fallbackError)
          })
        }
      }
    } catch (error) {
      if (error instanceof BusinessError) {
        throw error
      }
      const t = c.get('t')
      console.error(t('tts3.tts_generation_failed'), error)
      throwBusinessError(ErrorCode.TTS_SERVICE_ERROR, {
        originalError: error instanceof Error ? error.message : String(error)
      })
    }
  }
)

// 健康检查接口
tts3.get('/health', languageMiddleware, async c => {
  try {
    const t = c.get('t')
    console.log(t('tts3.checking_health'))

    const env = c.env
    const ttsService = getTTSService(env)

    // 获取集成服务的健康状态
    const healthResult = await ttsService.healthCheck()

    // 获取服务统计信息
    const serviceStats = ttsService.getServiceStats()

    const response = createSuccessResponse({
      service: 'tts3-integrated',
      status: healthResult.success ? 'healthy' : 'degraded',
      integrated: {
        available: healthResult.success,
        data: healthResult.data,
        error: healthResult.error
      },
      stats: serviceStats,
      fallbackAvailable: true // Fish Audio 降级可用
    })
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    const t = c.get('t')
    console.error(t('tts3.health_check_failed'), error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR, {
      originalError: error instanceof Error ? error.message : String(error)
    })
  }
})

export default tts3
