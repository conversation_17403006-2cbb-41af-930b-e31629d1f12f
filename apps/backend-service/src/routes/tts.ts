import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { authMiddleware } from '@/middleware/auth';
import type { Env } from '@/types/env';
import { z } from 'zod';
import { TTSService } from '@/lib/tts/tts-service';
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository';

const tts = new Hono<{ Bindings: Env }>();

// 根据Supabase用户ID获取数据库用户ID
async function getDbUserId(env: Env, supabaseUserId: string): Promise<string | null> {
  const dbUser = await getUserBySupabaseId(env, supabaseUserId);
  return dbUser?.id || null;
}

// 创建TTS任务的请求体验证
const generateTTSSchema = z.object({
  text: z.string().min(1).max(5000), // 限制文本长度
  messageId: z.string().optional(),
  chatId: z.string().optional(),
  voiceModelId: z.string().optional(), // 声音模型ID
});

// 同步生成TTS音频
tts.post('/generate', authMiddleware, zValidator('json', generateTTSSchema), async (c) => {
  try {
    const supabaseUser = c.get('user');
    const { text, messageId, chatId, voiceModelId } = c.req.valid('json');
    const env = c.env;

    if (!supabaseUser) {
      return c.json({ success: false, message: '未找到用户信息' }, 401);
    }

    // 获取数据库用户ID
    const dbUserId = await getDbUserId(env, supabaseUser.id);
    if (!dbUserId) {
      return c.json({ success: false, message: '用户不存在' }, 404);
    }

    // 检查必要的环境变量
    if (!env.FISH_AUDIO_API_KEY) {
      return c.json(
        {
          success: false,
          message: 'TTS服务配置不完整',
        },
        503
      );
    }

    // 创建TTS服务实例
    const ttsService = new TTSService(env);

    // 同步处理TTS任务
    const result = await ttsService.generateAudioSync({
      text,
      messageId,
      chatId,
      voiceModelId,
      userId: dbUserId,
    });

    return c.json({
      success: true,
      data: {
        audioUrl: result.audioUrl,
        status: 'completed',
      },
    });
  } catch (error) {
    console.error('生成TTS音频失败:', error);
    return c.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '生成TTS音频失败',
      },
      500
    );
  }
});

// 注意：删除了状态查询和任务列表路由，因为现在是同步处理

export default tts;
