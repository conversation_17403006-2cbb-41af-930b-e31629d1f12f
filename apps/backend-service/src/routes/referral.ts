import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import type { SupportedLanguage } from '@/i18n/config'
import type { Env } from '@/types/env'
import type { Context } from 'hono'
import {
  createInviteCode,
  getUserInviteCode,
  validateInviteCode,
  getUserInvitees,
  getInviteStats
} from '@/lib/db/queries/referral'
import {
  getUserCommissionAccount,
  getUserCommissionRecords,
  getUserCommissionStats,
  createWithdrawRequest,
  getUserWithdrawRequests,
  freezeCommissionBalance
} from '@/lib/db/queries/commission'
import { getWithdrawConfig, calculateWithdrawFee } from '@/lib/db/queries/system-config'
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository';

// 获取国际化函数的辅助函数
function getI18n(
  c: Context<{
    Bindings: Env
    Variables: {
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
      user: any
    }
  }>
): (key: string, params?: Record<string, string | number>) => string {
  return c.get('t')
}

const app = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
    user: any
  }
}>()

// 辅助函数：从Supabase用户上下文解析本地用户ID
async function resolveLocalUserId(c: any): Promise<string> {
  const supabaseUser = c.get('user')
  const t = getI18n(c)
  if (!supabaseUser) {
    throw new Error(t('user_not_authenticated'))
  }

  // 获取数据库用户信息
  const dbUser = await getUserBySupabaseId(c.env, supabaseUser.id)
  if (!dbUser) {
    throw new Error(t('user_data_not_found'))
  }

  return dbUser.id
}

// ==================== 邀请码管理 ====================

// 生成邀请码
app.post('/invite-code/generate', authMiddleware, languageMiddleware, async c => {
  try {
    const userId = await resolveLocalUserId(c)

    const inviteCode = await createInviteCode(c.env, userId)

    return c.json({
      success: true,
      data: inviteCode
    })
  } catch (error) {
    console.error('Generate invite code error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: error instanceof Error ? error.message : t('invite_code_generate_failed')
      },
      500
    )
  }
})

// 获取我的邀请码信息
app.get('/invite-code/my', authMiddleware, languageMiddleware, async c => {
  try {
    const userId = await resolveLocalUserId(c)

    const myInviteCode = await getUserInviteCode(c.env, userId)
    const stats = await getInviteStats(c.env, userId)
    const commissionAccount = await getUserCommissionAccount(c.env, userId)

    return c.json({
      success: true,
      data: {
        inviteCode: myInviteCode,
        stats,
        commissionAccount
      }
    })
  } catch (error) {
    console.error('Get my invite code error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: t('invite_code_info_get_failed')
      },
      500
    )
  }
})

// 验证邀请码 - 静态版本
const validateInviteCodeSchema = z.object({
  code: z.string().min(1)
})

app.post(
  '/invite-code/validate',
  languageMiddleware,
  zValidator('json', validateInviteCodeSchema),
  async c => {
    try {
      const { code } = c.req.valid('json')
      const t = getI18n(c)

      // 验证邀请码
      if (!code || code.trim().length === 0) {
        return c.json(
          {
            success: false,
            error: t('invite_code_required')
          },
          400
        )
      }

      const result = await validateInviteCode(c.env, code)

      if (!result.valid) {
        return c.json(
          {
            success: false,
            error: result.error
          },
          400
        )
      }

      return c.json({
        success: true,
        data: {
          id: result.inviteCode!.id,
          code: result.inviteCode!.code,
          inviterEmail: result.inviteCode!.inviterEmail
        }
      })
    } catch (error) {
      console.error('Validate invite code error:', error)
      const t = getI18n(c)
      return c.json(
        {
          success: false,
          error: t('invite_code_validate_failed')
        },
        500
      )
    }
  }
)

// 获取我邀请的用户列表
app.get('/invites', authMiddleware, languageMiddleware, async c => {
  try {
    const userId = await resolveLocalUserId(c)
    const page = Number.parseInt(c.req.query('page') || '1')
    const limit = Number.parseInt(c.req.query('limit') || '10')

    const result = await getUserInvitees(c.env, userId, page, limit)

    return c.json({
      success: true,
      data: {
        list: result.list,
        pagination: {
          page,
          limit,
          total: result.total,
          totalPages: Math.ceil(result.total / limit)
        }
      }
    })
  } catch (error) {
    console.error('Get invites error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: t('invites_list_get_failed')
      },
      500
    )
  }
})

// ==================== 佣金账户管理 ====================

// 获取佣金账户信息
app.get('/commission/account', authMiddleware, languageMiddleware, async c => {
  try {
    const userId = await resolveLocalUserId(c)

    const account = await getUserCommissionAccount(c.env, userId)
    const stats = await getUserCommissionStats(c.env, userId)

    return c.json({
      success: true,
      data: {
        account: account || {
          totalEarned: '0.00',
          availableBalance: '0.00',
          frozenBalance: '0.00',
          totalWithdrawn: '0.00'
        },
        stats
      }
    })
  } catch (error) {
    console.error('Get commission account error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: t('commission_account_get_failed')
      },
      500
    )
  }
})

// 获取佣金记录列表
app.get('/commission/records', authMiddleware, languageMiddleware, async c => {
  try {
    const userId = await resolveLocalUserId(c)
    const page = Number.parseInt(c.req.query('page') || '1')
    const limit = Number.parseInt(c.req.query('limit') || '10')

    const result = await getUserCommissionRecords(c.env, userId, page, limit)

    return c.json({
      success: true,
      data: {
        list: result.list,
        pagination: {
          page,
          limit,
          total: result.total,
          totalPages: Math.ceil(result.total / limit)
        }
      }
    })
  } catch (error) {
    console.error('Get commission records error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: t('commission_records_get_failed')
      },
      500
    )
  }
})

// 获取邀请统计数据
app.get('/statistics', authMiddleware, languageMiddleware, async c => {
  try {
    const userId = await resolveLocalUserId(c)

    const inviteStats = await getInviteStats(c.env, userId)
    const commissionStats = await getUserCommissionStats(c.env, userId)

    return c.json({
      success: true,
      data: {
        ...inviteStats,
        commissionStats
      }
    })
  } catch (error) {
    console.error('Get statistics error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: t('statistics_get_failed')
      },
      500
    )
  }
})

// ==================== 提现申请功能 ====================

// 获取提现配置
app.get('/withdraw/config', authMiddleware, languageMiddleware, async c => {
  try {
    const config = await getWithdrawConfig(c.env)
    const t = getI18n(c)

    return c.json({
      success: true,
      data: {
        minWithdrawAmount: config.minAmount,
        withdrawFeeRate: config.feeRate,
        feeDescription: t('withdraw_fee_description', { rate: (config.feeRate * 100).toFixed(1) })
      }
    })
  } catch (error) {
    console.error('Get withdraw config error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: t('withdraw_config_get_failed')
      },
      500
    )
  }
})

// 提交提现申请 - 静态版本
const withdrawRequestSchema = z.object({
  amount: z.number().min(0.01),
  bankInfo: z.object({
    bankName: z.string().min(1),
    accountName: z.string().min(1),
    accountNumber: z.string().min(1),
    phone: z.string().optional()
  })
})

app.post(
  '/withdraw/apply',
  authMiddleware,
  languageMiddleware,
  zValidator('json', withdrawRequestSchema),
  async c => {
    try {
      const userId = await resolveLocalUserId(c)
      const { amount, bankInfo } = c.req.valid('json')
      const t = getI18n(c)

      // 验证提现金额
      if (amount <= 0) {
        return c.json(
          {
            success: false,
            error: t('withdraw_amount_required')
          },
          400
        )
      }

      // 验证银行信息
      if (!bankInfo.bankName || bankInfo.bankName.trim().length === 0) {
        return c.json(
          {
            success: false,
            error: t('withdraw_bank_name_required')
          },
          400
        )
      }

      if (!bankInfo.accountName || bankInfo.accountName.trim().length === 0) {
        return c.json(
          {
            success: false,
            error: t('withdraw_account_name_required')
          },
          400
        )
      }

      if (!bankInfo.accountNumber || bankInfo.accountNumber.trim().length === 0) {
        return c.json(
          {
            success: false,
            error: t('withdraw_account_number_required')
          },
          400
        )
      }

      const config = await getWithdrawConfig(c.env)

      // 验证最低提现金额
      if (amount < config.minAmount) {
        return c.json(
          {
            success: false,
            error: t('withdraw_amount_too_low', { minAmount: config.minAmount.toString() })
          },
          400
        )
      }

      // 获取用户佣金账户
      const account = await getUserCommissionAccount(c.env, userId)
      if (!account) {
        return c.json(
          {
            success: false,
            error: t('commission_account_not_found')
          },
          400
        )
      }

      const availableBalance = Number.parseFloat(account.availableBalance)

      // 验证余额是否足够
      if (amount > availableBalance) {
        return c.json(
          {
            success: false,
            error: t('withdraw_insufficient_balance', { balance: availableBalance.toString() })
          },
          400
        )
      }

      // 计算手续费和实际到账金额
      const { feeAmount, actualAmount } = calculateWithdrawFee(amount, config.feeRate)

      // 冻结相应金额
      await freezeCommissionBalance(c.env, userId, amount)

      // 创建提现申请记录
      await createWithdrawRequest(c.env, {
        userId,
        amount,
        feeAmount,
        actualAmount,
        bankInfo
      })

      return c.json({
        success: true,
        message: t('withdraw_apply_success'),
        data: {
          amount,
          feeAmount,
          actualAmount,
          status: 'pending'
        }
      })
    } catch (error) {
      console.error('Submit withdraw request error:', error)
      const t = getI18n(c)
      return c.json(
        {
          success: false,
          error: error instanceof Error ? error.message : t('withdraw_apply_failed')
        },
        500
      )
    }
  }
)

// 获取提现记录
app.get('/withdraw/records', authMiddleware, languageMiddleware, async c => {
  try {
    const userId = await resolveLocalUserId(c)
    const page = Number.parseInt(c.req.query('page') || '1')
    const limit = Number.parseInt(c.req.query('limit') || '10')

    const result = await getUserWithdrawRequests(c.env, userId, page, limit)

    return c.json({
      success: true,
      data: {
        list: result.list,
        pagination: {
          page,
          limit,
          total: result.total,
          totalPages: Math.ceil(result.total / limit)
        }
      }
    })
  } catch (error) {
    console.error('Get withdraw records error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: t('withdraw_records_get_failed')
      },
      500
    )
  }
})

export default app
