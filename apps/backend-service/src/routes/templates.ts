import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import type { Env } from '@/types/env'
import type { FaceSwapTask } from '@/types/face-swap'
import { generateUUID } from '@/lib/utils'
import {
  getTemplates,
  getTemplateById,
  updateTemplateUsageCount,
  getTemplatesByCategory,
  getTemplateCategories,
  searchTemplates,
  createGenerationHistory,
  getUserGenerationHistory,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  getTemplateStats
} from '@/lib/db/queries/template'
import { checkUserMembership, updateUserPoints, getUserPoints } from '@/modules/app/membership/repositories/membership.repository'
import { authMiddleware, optionalAuthMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import { clearTemplateCaches } from '@/lib/cache/cache-utils'
import type { Template, MediaGeneration } from '@/lib/db/schema'

const app = new Hono<{ Bindings: Env }>()

// ==================== 工具函数 ====================

/**
 * 过滤模板敏感字段，用于返回给前端的列表
 * 移除 prompt 和 negativePrompt 字段以保护提示词不被泄露
 */
function filterTemplateForList(template: Template): Omit<Template, 'prompt' | 'negativePrompt'> {
  const { prompt, negativePrompt, ...filteredTemplate } = template
  return filteredTemplate
}

/**
 * 批量过滤模板数组
 */
function filterTemplatesForList(
  templates: Template[]
): Omit<Template, 'prompt' | 'negativePrompt'>[] {
  return templates.map(filterTemplateForList)
}

/**
 * 过滤生成历史敏感字段，用于返回给前端
 * 移除 prompt 和 negativePrompt 字段以保护用户隐私
 */
function filterGenerationHistoryForList(
  generation: MediaGeneration
): Omit<MediaGeneration, 'prompt' | 'negativePrompt'> {
  const { prompt, negativePrompt, ...filteredGeneration } = generation
  return filteredGeneration
}

/**
 * 批量过滤生成历史数组
 */
function filterGenerationHistoriesForList(
  generations: MediaGeneration[]
): Omit<MediaGeneration, 'prompt' | 'negativePrompt'>[] {
  return generations.map(filterGenerationHistoryForList)
}

// ==================== 验证 Schema ====================

// 模板查询验证
const templateQuerySchema = z.object({
  category: z.string().optional(),
  gender: z.enum(['male', 'female']).optional(), // 新增性别参数
  premium: z
    .string()
    .optional()
    .transform(val => val === 'true'),
  limit: z
    .string()
    .optional()
    .transform(val => (val ? Number.parseInt(val) : 20)),
  offset: z
    .string()
    .optional()
    .transform(val => (val ? Number.parseInt(val) : 0))
})

// 模板搜索验证
const templateSearchSchema = z.object({
  q: z.string().optional(),
  category: z.string().optional(),
  tags: z
    .string()
    .optional()
    .transform(val => (val ? val.split(',').filter(Boolean) : [])),
  premium: z
    .string()
    .optional()
    .transform(val => val === 'true'),
  sort: z.enum(['created', 'name', 'cost', 'popular']).optional(),
  order: z.enum(['asc', 'desc']).optional(),
  limit: z
    .string()
    .optional()
    .transform(val => (val ? Number.parseInt(val) : 20)),
  offset: z
    .string()
    .optional()
    .transform(val => (val ? Number.parseInt(val) : 0))
})

// 创建模板验证
const createTemplateSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  category: z.string().min(1).max(50),
  previewImage: z.string().url().optional(),
  prompt: z.string().min(1),
  negativePrompt: z.string().optional(),
  pointsCost: z.number().int().min(1),
  isPremium: z.boolean().default(false),
  isActive: z.boolean().default(true),
  tags: z.array(z.string()).optional(),
  settings: z
    .object({
      width: z.number().int().min(256).max(2048).default(1024),
      height: z.number().int().min(256).max(2048).default(1024),
      steps: z.number().int().min(10).max(50).default(20),
      guidance: z.number().min(1).max(20).default(7.5)
    })
    .optional()
})

// 更新模板验证
const updateTemplateSchema = createTemplateSchema.partial()

// 模板生成验证
const generateWithTemplateSchema = z.object({
  templateId: z.string().min(1),
  originalImageUrl: z.string().url().optional(),
  customPrompt: z.string().optional()
})

// // ==================== 公开模板路由 ====================

/**
 * GET /api/templates
 * 获取模板列表
 */
app.get(
  '/',
  languageMiddleware,
  optionalAuthMiddleware,
  zValidator('query', templateQuerySchema),
  async c => {
    try {
      const { category, gender, premium } = c.req.valid('query')
      const user = c.get('user')

      // 检查用户会员状态
      let isMember = false
      if (user?.id) {
        const membershipStatus = await checkUserMembership(c.env, user.id)
        isMember = membershipStatus.isMember
      }

      // 根据性别确定查询的 category
      let queryCategory = category
      if (gender && !category) {
        // 如果传入了性别但没有指定 category，则使用性别作为 category
        queryCategory = gender
      }

      // 获取模板列表 - 返回全部，不分页
      const templates = await getTemplates(c.env, {
        category: queryCategory,
        isPremium: premium,
        limit: 1000, // 设置一个很大的数字，返回全部
        offset: 0
      })

      // 根据用户会员状态和性别过滤模板
      const filteredTemplates = templates.filter(template => {
        // 如果是会员专属模板但用户不是会员，则过滤掉
        if (template.isPremium && !isMember) {
          return false
        }
        // 如果指定只显示会员模板
        if (premium && !template.isPremium) {
          return false
        }
        // 如果指定了性别，确保模板的 category 匹配
        if (gender && template.category !== gender) {
          return false
        }
        return true
      })

      // 过滤敏感字段并添加用户访问权限信息
      const templatesWithAccess = filteredTemplates.map(template => ({
        ...filterTemplateForList(template),
        hasAccess: !template.isPremium || isMember,
        requiresMembership: template.isPremium && !isMember
      }))

      return c.json({
        success: true,
        data: {
          templates: templatesWithAccess,
          userMembership: {
            isMember,
            canAccessPremium: isMember
          }
        }
      })
    } catch (error) {
      const t = c.get('t')
      console.error('获取模板列表失败:', error)
      return c.json({ error: t('get_templates_failed') }, 500)
    }
  }
)

/**
 * GET /api/templates/search
 * 模板搜索
 */
app.get(
  '/search',
  languageMiddleware,
  optionalAuthMiddleware,
  zValidator('query', templateSearchSchema),
  async c => {
    try {
      const searchParams = c.req.valid('query')
      const user = c.get('user')

      // 检查用户会员状态
      let userMembership = null
      if (user?.id) {
        userMembership = await checkUserMembership(c.env, user.id)
      }

      // 执行搜索
      const searchResult = await searchTemplates(c.env, {
        query: searchParams.q,
        category: searchParams.category,
        tags: searchParams.tags,
        isPremium: searchParams.premium,
        sortBy: searchParams.sort || 'created',
        sortOrder: searchParams.order || 'desc',
        limit: searchParams.limit,
        offset: searchParams.offset
      })

      // 权限过滤 - 非会员用户不能看到会员专属模板
      const filteredTemplates = searchResult.templates.filter(template => {
        return !template.isPremium || userMembership?.isMember || false
      })

      // 过滤敏感字段并添加访问权限信息
      const templatesWithAccess = filteredTemplates.map(template => ({
        ...filterTemplateForList(template),
        hasAccess: !template.isPremium || userMembership?.isMember || false,
        requiresMembership: template.isPremium && !userMembership?.isMember
      }))

      return c.json({
        success: true,
        data: {
          templates: templatesWithAccess,
          total: searchResult.total,
          pagination: {
            limit: searchParams.limit,
            offset: searchParams.offset,
            hasMore: searchParams.offset + searchParams.limit < searchResult.total
          },
          filters: {
            query: searchParams.q,
            category: searchParams.category,
            tags: searchParams.tags,
            premium: searchParams.premium,
            sort: searchParams.sort || 'created',
            order: searchParams.order || 'desc'
          },
          userMembership: {
            isMember: userMembership?.isMember || false,
            canAccessPremium: userMembership?.isMember || false
          }
        }
      })
    } catch (error) {
      const t = c.get('t')
      console.error('模板搜索失败:', error)
      return c.json({ error: t('template_search_failed') }, 500)
    }
  }
)

/**
 * GET /api/templates/categories
 * 获取所有模板分类及统计信息
 */
app.get('/categories', languageMiddleware, async c => {
  try {
    // 获取所有分类
    const categories = await getTemplateCategories(c.env)

    // 为每个分类获取统计信息
    const categoriesWithStats = await Promise.all(
      categories.map(async category => {
        try {
          // 获取该分类下的所有模板
          const allTemplates = await getTemplatesByCategory(c.env, category)
          const premiumTemplates = await getTemplatesByCategory(c.env, category, true)
          const regularTemplates = await getTemplatesByCategory(c.env, category, false)

          return {
            name: category,
            totalTemplates: allTemplates.length,
            premiumTemplates: premiumTemplates.length,
            regularTemplates: regularTemplates.length,
            // TODO: 添加使用统计
            totalUsage: 0,
            averageRating: 0
          }
        } catch (error) {
          console.error(`获取分类 ${category} 统计失败:`, error)
          return {
            name: category,
            totalTemplates: 0,
            premiumTemplates: 0,
            regularTemplates: 0,
            totalUsage: 0,
            averageRating: 0
          }
        }
      })
    )

    // 按模板数量排序
    categoriesWithStats.sort((a, b) => b.totalTemplates - a.totalTemplates)

    return c.json({
      success: true,
      data: {
        categories: categoriesWithStats,
        total: categoriesWithStats.length
      }
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取模板分类失败:', error)
    return c.json({ error: t('get_categories_failed') }, 500)
  }
})

/**
 * GET /api/templates/:id
 * 根据ID获取模板详情
 */
app.get('/:id', languageMiddleware, optionalAuthMiddleware, async c => {
  try {
    const id = c.req.param('id')
    const user = c.get('user')
    const t = c.get('t')

    if (!id) {
      return c.json({ error: t('template_id_required') }, 400)
    }

    const template = await getTemplateById(c.env, id)

    if (!template) {
      return c.json({ error: t('template_not_found') }, 404)
    }

    // 检查用户会员状态
    let isMember = false
    if (user?.id) {
      const membershipStatus = await checkUserMembership(c.env, user.id)
      isMember = membershipStatus.isMember
    }

    // 检查用户是否有权限访问此模板
    const hasAccess = !template.isPremium || isMember

    if (template.isPremium && !isMember) {
      // 如果是会员专属模板但用户不是会员，返回受限信息（不包含提示词）
      return c.json(
        {
          success: false,
          message: t('template_requires_membership'),
          data: {
            ...filterTemplateForList(template),
            hasAccess: false,
            requiresMembership: true
          }
        },
        403
      )
    }

    // 有权限访问的用户返回完整模板信息（包含提示词）
    return c.json({
      success: true,
      data: {
        ...template,
        hasAccess,
        requiresMembership: template.isPremium && !isMember
      }
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取模板详情失败:', error)
    return c.json({ error: t('get_template_detail_failed') }, 500)
  }
})

// ==================== 模板生成路由 ====================

/**
 * POST /api/templates/generate
 * 基于模板生成图像
 */
app.post(
  '/generate',
  languageMiddleware,
  authMiddleware,
  zValidator('json', generateWithTemplateSchema),
  async c => {
    try {
      const { templateId, originalImageUrl, customPrompt } = c.req.valid('json')
      const user = c.get('user')
      const t = c.get('t')

      // 验证参数
      if (!templateId || templateId.trim().length === 0) {
        return c.json({ error: t('template_id_required') }, 400)
      }
      if (originalImageUrl) {
        try {
          new URL(originalImageUrl)
        } catch {
          return c.json({ error: t('original_image_url_invalid') }, 400)
        }
      }

      if (!user?.id) {
        return c.json({ error: t('user_not_logged_in') }, 401)
      }

      // 获取模板详情
      const template = await getTemplateById(c.env, templateId)
      if (!template) {
        return c.json({ error: t('template_not_found') }, 404)
      }

      // 检查用户会员状态
      const membershipStatus = await checkUserMembership(c.env, user.id)
      const isMember = membershipStatus.isMember

      // 检查是否有权限使用此模板
      if (template.isPremium && !isMember) {
        return c.json({ error: t('template_requires_membership') }, 403)
      }

      // 检查用户点数是否足够
      const userPoints = await getUserPoints(c.env, user.id)
      if (userPoints.availablePoints < template.pointsCost) {
        return c.json(
          {
            error: t('insufficient_points'),
            data: {
              required: template.pointsCost,
              available: userPoints.availablePoints
            }
          },
          400
        )
      }

      // 扣除点数
      await updateUserPoints(
        c.env,
        user.id,
        template.pointsCost,
        'spend',
        'generation',
        templateId,
        `使用模板"${template.name}"生成图像`
      )

      // 创建生成记录
      const [generationRecord] = await createGenerationHistory(c.env, {
        userId: user.id,
        templateId: template.id,
        originalImageUrl,
        prompt: customPrompt || template.prompt,
        negativePrompt: template.negativePrompt || undefined,
        pointsUsed: template.pointsCost,
        status: 'pending'
      })

      // 异步更新模板使用次数，不阻塞响应
      updateTemplateUsageCount(c.env, template.id).catch(error => {
        console.error('更新模板使用次数失败:', error)
      })

      // 暂时使用换脸队列替代原来的写真集生成队列
      // TODO: 原来应该调用 PHOTO_ALBUM_GENERATION_QUEUE
      const taskId = generateUUID()

      // 创建换脸任务
      const faceSwapTask: FaceSwapTask = {
        taskId,
        messageId: generationRecord.id, // 使用生成记录ID作为消息ID
        chatId: 'template-generation', // 模板生成的虚拟聊天ID
        userId: user.id,
        inputImageUrl: 'placeholder', // 需要先生成图片
        swapImageUrl: originalImageUrl || '', // 用户上传的原始图片作为换脸目标
        timestamp: Date.now(),
        metadata: {
          templateId: template.id,
          originalPrompt: customPrompt || template.prompt,
          templateName: template.name,
          pointsUsed: template.pointsCost,
          generationRecordId: generationRecord.id,
          width: (template.settings as any)?.width || 1024,
          height: (template.settings as any)?.height || 1024
        }
      }

      try {
        // 发送任务到换脸队列（暂时替代写真集队列）
        await c.env.FACE_SWAP_QUEUE.send(faceSwapTask)
        console.log('✅ 模板生成任务已发送到换脸队列:', taskId)
      } catch (queueError) {
        console.error('❌ 发送模板生成任务到队列失败:', queueError)

        // 队列发送失败，退还积分
        await updateUserPoints(
          c.env,
          user.id,
          template.pointsCost,
          'earn',
          'refund',
          templateId,
          `模板生成失败退还 - 模板"${template.name}"`
        )

        return c.json(
          {
            success: false,
            message: t('queue_send_failed'),
            data: {
              pointsRefunded: template.pointsCost
            }
          },
          500
        )
      }

      return c.json(
        {
          success: true,
          data: {
            taskId,
            generationId: generationRecord.id,
            status: 'pending',
            template: {
              id: template.id,
              name: template.name,
              pointsCost: template.pointsCost
            },
            pointsUsed: template.pointsCost,
            remainingPoints: userPoints.availablePoints - template.pointsCost
          },
          message: t('generation_started')
        },
        202 // 202 Accepted - 请求已接受，正在处理
      )
    } catch (error) {
      const t = c.get('t')
      console.error('模板图像生成失败:', error)
      return c.json({ error: t('generation_failed') }, 500)
    }
  }
)

// ==================== 用户生成历史 ====================

/**
 * GET /api/templates/history
 * 获取用户生成历史
 */
app.get('/history', languageMiddleware, authMiddleware, async c => {
  try {
    const user = c.get('user')
    const t = c.get('t')
    const { searchParams } = new URL(c.req.url)

    const limit = Number.parseInt(searchParams.get('limit') || '20')
    const offset = Number.parseInt(searchParams.get('offset') || '0')

    if (!user?.id) {
      return c.json({ error: t('user_not_logged_in') }, 401)
    }

    const history = await getUserGenerationHistory(c.env, user.id, {
      limit,
      offset
    })

    // 过滤敏感字段（提示词）后返回
    const filteredHistory = filterGenerationHistoriesForList(history)

    return c.json({
      success: true,
      data: {
        history: filteredHistory,
        pagination: {
          limit,
          offset,
          hasMore: history.length === limit
        }
      }
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取生成历史失败:', error)
    return c.json({ error: t('get_generation_history_failed') }, 500)
  }
})

// ==================== 管理员路由 ====================

/**
 * POST /api/templates
 * 创建新模板（管理员）
 */
app.post(
  '/',
  languageMiddleware,
  authMiddleware,
  zValidator('json', createTemplateSchema),
  async c => {
    try {
      const data = c.req.valid('json')
      const user = c.get('user')
      const t = c.get('t')

      // 验证参数
      if (!data.name || data.name.trim().length === 0) {
        return c.json({ error: t('template_name_required') }, 400)
      }
      if (data.name.length > 100) {
        return c.json({ error: t('template_name_too_long') }, 400)
      }
      if (!data.category || data.category.trim().length === 0) {
        return c.json({ error: t('template_category_required') }, 400)
      }
      if (data.category.length > 50) {
        return c.json({ error: t('category_name_too_long') }, 400)
      }
      if (data.previewImage) {
        try {
          new URL(data.previewImage)
        } catch {
          return c.json({ error: t('preview_image_url_invalid') }, 400)
        }
      }
      if (!data.prompt || data.prompt.trim().length === 0) {
        return c.json({ error: t('prompt_required') }, 400)
      }
      if (!data.pointsCost || data.pointsCost < 1) {
        return c.json({ error: t('points_cost_invalid') }, 400)
      }

      if (!user?.id) {
        return c.json({ error: t('user_not_logged_in') }, 401)
      }

      // TODO: 检查管理员权限
      // const isAdmin = await checkAdminPermission(user.id)
      // if (!isAdmin) {
      //   return c.json({ error: '权限不足' }, 403)
      // }

      // 创建模板
      const [newTemplate] = await createTemplate(c.env, {
        ...data,
        createdBy: user.id
      })

      // 创建成功后，预热缓存（可选）
      // 由于是新模板，暂时不需要清除缓存

      console.log('模板创建成功:', newTemplate.id)

      return c.json(
        {
          success: true,
          data: newTemplate,
          message: t('template_created')
        },
        201
      )
    } catch (error) {
      const t = c.get('t')
      console.error('创建模板失败:', error)
      return c.json({ error: t('create_template_failed') }, 500)
    }
  }
)

/**
 * PUT /api/templates/:id
 * 更新模板（管理员）
 */
app.put(
  '/:id',
  languageMiddleware,
  authMiddleware,
  zValidator('json', updateTemplateSchema),
  async c => {
    try {
      const id = c.req.param('id')
      const data = c.req.valid('json')
      const user = c.get('user')
      const t = c.get('t')

      if (!user?.id) {
        return c.json({ error: t('user_not_logged_in') }, 401)
      }

      // TODO: 检查管理员权限
      // const isAdmin = await checkAdminPermission(user.id)
      // if (!isAdmin) {
      //   return c.json({ error: '权限不足' }, 403)
      // }

      // 检查模板是否存在
      const existingTemplate = await getTemplateById(c.env, id)
      if (!existingTemplate) {
        return c.json({ error: t('template_not_found') }, 404)
      }

      // 更新模板
      const [updatedTemplate] = await updateTemplate(c.env, id, data)

      // 更新后清除该模板的缓存
      try {
        await clearTemplateCaches(c.env, id)
        console.log('✅ 模板缓存已清除:', id)
      } catch (cacheError) {
        console.error('⚠️ 清除模板缓存失败:', cacheError)
        // 缓存清除失败不影响主流程
      }

      console.log('模板更新成功:', id)

      return c.json({
        success: true,
        data: updatedTemplate,
        message: t('template_updated')
      })
    } catch (error) {
      const t = c.get('t')
      console.error('更新模板失败:', error)
      return c.json({ error: t('update_template_failed') }, 500)
    }
  }
)

/**
 * DELETE /api/templates/:id
 * 删除模板（管理员）
 */
app.delete('/:id', languageMiddleware, authMiddleware, async c => {
  try {
    const id = c.req.param('id')
    const user = c.get('user')
    const t = c.get('t')

    if (!user?.id) {
      return c.json({ error: t('user_not_logged_in') }, 401)
    }

    // TODO: 检查管理员权限
    // const isAdmin = await checkAdminPermission(user.id)
    // if (!isAdmin) {
    //   return c.json({ error: '权限不足' }, 403)
    // }

    // 检查模板是否存在
    const existingTemplate = await getTemplateById(c.env, id)
    if (!existingTemplate) {
      return c.json({ error: t('template_not_found') }, 404)
    }

    // 删除模板（软删除）
    const [deletedTemplate] = await deleteTemplate(c.env, id)

    // 删除后清除该模板的缓存
    try {
      await clearTemplateCaches(c.env, id)
      console.log('✅ 模板缓存已清除:', id)
    } catch (cacheError) {
      console.error('⚠️ 清除模板缓存失败:', cacheError)
      // 缓存清除失败不影响主流程
    }

    console.log('模板删除成功:', id)

    return c.json({
      success: true,
      data: deletedTemplate,
      message: t('template_deleted')
    })
  } catch (error) {
    const t = c.get('t')
    console.error('删除模板失败:', error)
    return c.json({ error: t('delete_template_failed') }, 500)
  }
})

/**
 * GET /api/templates/stats
 * 获取模板统计数据（管理员）
 */
app.get('/stats', languageMiddleware, authMiddleware, async c => {
  try {
    const user = c.get('user')
    const t = c.get('t')

    if (!user?.id) {
      return c.json({ error: t('user_not_logged_in') }, 401)
    }

    // TODO: 检查管理员权限
    // const isAdmin = await checkAdminPermission(user.id)
    // if (!isAdmin) {
    //   return c.json({ error: '权限不足' }, 403)
    // }

    const stats = await getTemplateStats(c.env)

    return c.json({
      success: true,
      data: stats
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取模板统计失败:', error)
    return c.json({ error: t('get_template_stats_failed') }, 500)
  }
})

export default app
