import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import {
  getVoiceList,
  getVoiceDetail,
  getVoiceSamples,
  createVoice,
  updateVoice,
  createVoiceSampleHandler,
  updateVoiceSampleHandler,
  deleteVoiceSampleHandler,
  getVoiceCacheStats,
  clearVoiceCache
} from '@/modules/app/voice/controllers/voice.controller'
import { authMiddleware } from '@/middleware/auth'
import type { Env } from '@/types/env'

const voices = new Hono<{ Bindings: Env }>()

// ==================== 验证模式 ====================

// 声音模型创建验证模式
const createVoiceModelSchema = z.object({
  modelId: z.string().min(1, '模型ID不能为空').max(100, '模型ID不能超过100个字符'),
  name: z.string().min(1, '声音名称不能为空').max(50, '声音名称不能超过50个字符'),
  displayName: z.string().min(1, '显示名称不能为空').max(100, '显示名称不能超过100个字符'),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
  gender: z.enum(['male', 'female', 'neutral'], {
    errorMap: () => ({ message: '请选择有效的性别' })
  }),
  language: z.string().max(10, '语言代码不能超过10个字符').default('zh-CN'),
  supportedLanguages: z.array(z.string()).optional(),
  category: z.string().max(50, '分类不能超过50个字符').optional(),
  tags: z.array(z.string()).optional(),
  isPremium: z.boolean().default(false),
  sortOrder: z.number().int().min(0).default(0)
})

// 声音模型更新验证模式
const updateVoiceModelSchema = createVoiceModelSchema.partial()

// 声音示例创建验证模式
const createVoiceSampleSchema = z.object({
  language: z.string().min(1, '语言代码不能为空').max(10, '语言代码不能超过10个字符'),
  sampleText: z.string().min(1, '示例文本不能为空'),
  audioUrl: z.string().url('请提供有效的音频URL').optional(),
  duration: z.number().int().min(0).optional(),
  fileSize: z.number().int().min(0).optional(),
  isDefault: z.boolean().default(false)
})

// 声音示例更新验证模式
const updateVoiceSampleSchema = createVoiceSampleSchema.partial()

// 查询参数验证模式
const genderQuerySchema = z.object({
  gender: z.enum(['male', 'female', 'neutral']).optional()
})

// ==================== 公开接口 ====================

/**
 * 获取所有可用的声音模型（带缓存）
 * GET /api/voices
 */
voices.get('/', async c => {
  try {
    const env = c.env
    const query = c.req.query()

    let voiceModels

    if (query.withSamples === 'true') {
      // 包含默认示例的声音模型
      if (query.gender) {
        // 带示例 + 性别筛选
        const { gender } = genderQuerySchema.parse(query)
        if (gender) {
          voiceModels = await getCachedVoiceModelsList(
            env,
            async () => {
              const allModels = await getAllVoiceModelsWithDefaultSamples(env)
              return allModels.filter(model => model.gender === gender)
            },
            'withSamples',
            gender
          )
        } else {
          voiceModels = await getCachedVoiceModelsList(
            env,
            () => getAllVoiceModelsWithDefaultSamples(env),
            'withSamples'
          )
        }
      } else {
        // 带示例，无性别筛选
        voiceModels = await getCachedVoiceModelsList(
          env,
          () => getAllVoiceModelsWithDefaultSamples(env),
          'withSamples'
        )
      }
    } else if (query.gender) {
      // 根据性别筛选（不包含示例）
      const { gender } = genderQuerySchema.parse(query)
      if (gender) {
        voiceModels = await getCachedVoiceModelsList(
          env,
          () => getVoiceModelsByGender(env, gender),
          'gender',
          gender
        )
      } else {
        voiceModels = await getCachedVoiceModelsList(env, () => getActiveVoiceModels(env), 'all')
      }
    } else {
      // 获取所有活跃的声音模型
      voiceModels = await getCachedVoiceModelsList(env, () => getActiveVoiceModels(env), 'all')
    }

    return c.json({
      success: true,
      data: voiceModels
    })
  } catch (error) {
    console.error('获取声音模型失败:', error)
    return c.json(
      {
        success: false,
        message: '获取声音模型失败'
      },
      500
    )
  }
})

/**
 * 根据ID获取声音模型详情
 * GET /api/voices/:id
 */
voices.get('/:id', async c => {
  try {
    const env = c.env
    const id = c.req.param('id')
    const withSamples = c.req.query('withSamples') === 'true'

    let voiceModel

    if (withSamples) {
      voiceModel = await getVoiceModelWithSamples(env, id)
    } else {
      voiceModel = await getVoiceModelById(env, id)
    }

    if (!voiceModel) {
      return c.json(
        {
          success: false,
          message: '声音模型不存在'
        },
        404
      )
    }

    return c.json({
      success: true,
      data: voiceModel
    })
  } catch (error) {
    console.error('获取声音模型详情失败:', error)
    return c.json(
      {
        success: false,
        message: '获取声音模型详情失败'
      },
      500
    )
  }
})

/**
 * 获取声音模型的示例音频
 * GET /api/voices/:id/samples
 */
voices.get('/:id/samples', async c => {
  try {
    const env = c.env
    const id = c.req.param('id')
    const language = c.req.query('language')

    let samples

    if (language) {
      // 获取特定语言的示例
      const sample = await getVoiceSampleByLanguage(env, id, language)
      samples = sample ? [sample] : []
    } else {
      // 获取所有示例
      samples = await getVoiceSamplesByModelId(env, id)
    }

    return c.json({
      success: true,
      data: samples
    })
  } catch (error) {
    console.error('获取声音示例失败:', error)
    return c.json(
      {
        success: false,
        message: '获取声音示例失败'
      },
      500
    )
  }
})

// ==================== 管理员接口 ====================

/**
 * 创建声音模型（需要管理员权限）
 * POST /api/voices
 */
voices.post('/', authMiddleware, zValidator('json', createVoiceModelSchema), async c => {
  try {
    const user = c.get('user')
    const data = c.req.valid('json')
    const env = c.env

    // TODO: 检查管理员权限
    // if (!user.isAdmin) {
    //   return c.json({ success: false, message: '权限不足' }, 403)
    // }

    // 检查modelId是否已存在
    const existingModel = await getVoiceModelByModelId(env, data.modelId)
    if (existingModel) {
      return c.json(
        {
          success: false,
          message: '模型ID已存在'
        },
        400
      )
    }

    const [newVoiceModel] = await createVoiceModel(env, data)

    // 清除声音模型列表缓存
    await clearAllVoiceModelsListCache(env)

    return c.json(
      {
        success: true,
        data: newVoiceModel
      },
      201
    )
  } catch (error) {
    console.error('创建声音模型失败:', error)
    return c.json(
      {
        success: false,
        message: '创建声音模型失败'
      },
      500
    )
  }
})

/**
 * 更新声音模型（需要管理员权限）
 * PUT /api/voices/:id
 */
voices.put('/:id', authMiddleware, zValidator('json', updateVoiceModelSchema), async c => {
  try {
    const user = c.get('user')
    const id = c.req.param('id')
    const data = c.req.valid('json')
    const env = c.env

    // TODO: 检查管理员权限
    // if (!user.isAdmin) {
    //   return c.json({ success: false, message: '权限不足' }, 403)
    // }

    // 检查声音模型是否存在
    const existingModel = await getVoiceModelById(env, id)
    if (!existingModel) {
      return c.json(
        {
          success: false,
          message: '声音模型不存在'
        },
        404
      )
    }

    const [updatedVoiceModel] = await updateVoiceModel(env, id, data)

    // 清除声音模型列表缓存
    await clearAllVoiceModelsListCache(env)

    return c.json({
      success: true,
      data: updatedVoiceModel
    })
  } catch (error) {
    console.error('更新声音模型失败:', error)
    return c.json(
      {
        success: false,
        message: '更新声音模型失败'
      },
      500
    )
  }
})

/**
 * 为声音模型添加示例音频（需要管理员权限）
 * POST /api/voices/:id/samples
 */
voices.post(
  '/:id/samples',
  authMiddleware,
  zValidator('json', createVoiceSampleSchema),
  async c => {
    try {
      const user = c.get('user')
      const voiceModelId = c.req.param('id')
      const data = c.req.valid('json')
      const env = c.env

      // TODO: 检查管理员权限
      // if (!user.isAdmin) {
      //   return c.json({ success: false, message: '权限不足' }, 403)
      // }

      // 检查声音模型是否存在
      const existingModel = await getVoiceModelById(env, voiceModelId)
      if (!existingModel) {
        return c.json(
          {
            success: false,
            message: '声音模型不存在'
          },
          404
        )
      }

      const [newSample] = await createVoiceSample(env, {
        voiceModelId,
        ...data
      })

      return c.json(
        {
          success: true,
          data: newSample
        },
        201
      )
    } catch (error) {
      console.error('创建声音示例失败:', error)
      return c.json(
        {
          success: false,
          message: '创建声音示例失败'
        },
        500
      )
    }
  }
)

/**
 * 更新声音示例（需要管理员权限）
 * PUT /api/voices/:voiceId/samples/:sampleId
 */
voices.put(
  '/:voiceId/samples/:sampleId',
  authMiddleware,
  zValidator('json', updateVoiceSampleSchema),
  async c => {
    try {
      const user = c.get('user')
      const sampleId = c.req.param('sampleId')
      const data = c.req.valid('json')
      const env = c.env

      // TODO: 检查管理员权限
      // if (!user.isAdmin) {
      //   return c.json({ success: false, message: '权限不足' }, 403)
      // }

      const [updatedSample] = await updateVoiceSample(env, sampleId, data)

      if (!updatedSample) {
        return c.json(
          {
            success: false,
            message: '声音示例不存在'
          },
          404
        )
      }

      return c.json({
        success: true,
        data: updatedSample
      })
    } catch (error) {
      console.error('更新声音示例失败:', error)
      return c.json(
        {
          success: false,
          message: '更新声音示例失败'
        },
        500
      )
    }
  }
)

/**
 * 删除声音示例（需要管理员权限）
 * DELETE /api/voices/:voiceId/samples/:sampleId
 */
voices.delete('/:voiceId/samples/:sampleId', authMiddleware, async c => {
  try {
    const user = c.get('user')
    const sampleId = c.req.param('sampleId')
    const env = c.env

    // TODO: 检查管理员权限
    // if (!user.isAdmin) {
    //   return c.json({ success: false, message: '权限不足' }, 403)
    // }

    const [deletedSample] = await deleteVoiceSample(env, sampleId)

    if (!deletedSample) {
      return c.json(
        {
          success: false,
          message: '声音示例不存在'
        },
        404
      )
    }

    return c.json({
      success: true,
      message: '声音示例删除成功'
    })
  } catch (error) {
    console.error('删除声音示例失败:', error)
    return c.json(
      {
        success: false,
        message: '删除声音示例失败'
      },
      500
    )
  }
})

// ==================== 调试接口 ====================

/**
 * 获取声音模型缓存统计（调试用）
 * GET /api/voices/cache/stats
 */
voices.get('/cache/stats', async c => {
  try {
    const env = c.env
    const stats = await getCacheStats(env)

    return c.json({
      success: true,
      data: {
        cache: stats,
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('获取声音模型缓存统计失败:', error)
    return c.json(
      {
        success: false,
        message: '获取缓存统计失败',
        error: error instanceof Error ? error.message : String(error)
      },
      500
    )
  }
})

/**
 * 清除所有声音模型列表缓存（调试用）
 * DELETE /api/voices/cache/clear
 */
voices.delete('/cache/clear', authMiddleware, async c => {
  try {
    const env = c.env

    await clearAllVoiceModelsListCache(env)

    return c.json({
      success: true,
      message: '声音模型列表缓存已清除'
    })
  } catch (error) {
    console.error('清除声音模型列表缓存失败:', error)
    return c.json(
      {
        success: false,
        message: '清除缓存失败',
        error: error instanceof Error ? error.message : String(error)
      },
      500
    )
  }
})

export default voices
