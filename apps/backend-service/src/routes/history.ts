import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import type { Env } from '@/types/env';
import { getChatsByRoleAndUserId, getChatsByUserId } from '@/modules/app/chat/repositories/chat.repository';
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository';
import { authMiddleware } from '@/middleware/auth';
import { languageMiddleware } from '@/middleware/language';

const app = new Hono<{ Bindings: Env }>();

// 历史记录查询验证
const historyQuerySchema = z.object({
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? Number.parseInt(val) : 10)),
  starting_after: z.string().optional(),
  ending_before: z.string().optional(),
  role: z.string().optional(),
});

/**
 * GET /api/history
 * 获取用户聊天历史记录
 */
app.get('/', languageMiddleware, authMiddleware, zValidator('query', historyQuerySchema), async (c) => {
  try {
    const { limit, starting_after, ending_before, role } = c.req.valid('query');
    const user = c.get('user');
    const t = c.get('t');

    // 验证参数
    if (starting_after && ending_before) {
      return c.json(
        {
          error: t('both_pagination_params'),
        },
        400
      );
    }

    if (!user?.id) {
      return c.json({ error: t('unauthorized') }, 401);
    }

    // 获取数据库用户信息（与聊天创建时保持一致）
    const dbUser = await getUserBySupabaseId(c.env, user.id);
    if (!dbUser) {
      return c.json({ error: t('user_not_found_in_db') }, 404);
    }

    // 根据是否有角色ID选择不同的查询方法
    if (role) {
      // 按角色查询聊天记录
      const chats = await getChatsByRoleAndUserId(c.env, {
        userId: dbUser.id, // 使用数据库用户ID
        roleId: role,
        limit,
      });

      // 将 characterId 映射为 roleId 以匹配前端期望
      const mappedChats = chats.map((chat) => ({
        ...chat,
        roleId: chat.characterId,
      }));

      return c.json({ chats: mappedChats });
    } else {
      // 查询用户所有聊天记录
      const result = await getChatsByUserId(c.env, {
        userId: dbUser.id, // 使用数据库用户ID
        limit,
        startingAfter: starting_after ?? undefined,
          endingBefore: ending_before ?? undefined,
      });

      // 将 characterId 映射为 roleId 以匹配前端期望
      const mappedChats = result.chats.map((chat) => ({
        ...chat,
        roleId: chat.characterId,
      }));

      return c.json({
        chats: mappedChats,
        hasMore: result.hasMore,
      });
    }
  } catch (error) {
    const t = c.get('t');
    console.error('获取聊天记录失败:', error);
    return c.json(
      {
        error: t('get_chat_history_failed'),
        chats: [],
      },
      500
    );
  }
});

export default app;
