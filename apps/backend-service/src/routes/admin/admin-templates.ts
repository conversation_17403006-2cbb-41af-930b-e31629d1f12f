import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { authMiddleware } from '@/middleware/auth';
import type { Env } from '@/types/env';
import { getSupabase } from '@/lib/db/queries/base';
import { handleSupabaseResult, TABLE_NAMES } from '@/lib/db/supabase-types';
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository';

const adminTemplates = new Hono<{ Bindings: Env }>();

// 检查管理员权限
async function checkAdminPermission(c: any): Promise<boolean> {
  try {
    const supabaseUser = c.get('user');
    if (!supabaseUser) {
      return false;
    }

    // 检查用户的 user_metadata 中是否有管理员标识
    const userMetadata =
      supabaseUser.user_metadata || (supabaseUser as any).raw_user_meta_data || {};
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true;

    if (isAdmin) {
      return true;
    }

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>',
      // 在这里添加其他管理员邮箱
    ];

    if (adminEmails.includes(supabaseUser.email)) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('检查管理员权限失败:', error);
    return false;
  }
}

// ==================== 模板列表管理 ====================

// 查询参数验证
const templateListSchema = z.object({
  page: z
    .string()
    .transform((val) => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform((val) => Number.parseInt(val))
    .default('20'),
  keyword: z.string().optional(),
  category: z.string().optional(),
  isPublic: z
    .string()
    .transform((val) => val === 'true')
    .optional(),
  isActive: z
    .string()
    .transform((val) => val === 'true')
    .optional(),
  isPremium: z
    .string()
    .transform((val) => val === 'true')
    .optional(),
});

// 获取模板列表
adminTemplates.get(
  '/',
  authMiddleware,
  zValidator('query', templateListSchema),
  async (c) => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c);
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403);
      }

      const { page, pageSize, keyword, category, isPublic, isActive, isPremium } = c.req.valid('query');
      const env = c.env;

      console.log('📋 [ADMIN-TEMPLATES] 获取模板列表:', {
        page,
        pageSize,
        keyword,
        category,
        isPublic,
        isActive,
        isPremium
      });

      // 从数据库查询模板列表
      const supabase = getSupabase(env);
      
      let query = supabase
        .from(TABLE_NAMES.template)
        .select('*');

      // 构建筛选条件
      if (keyword) {
        query = query.or(`name.ilike.%${keyword}%,description.ilike.%${keyword}%`);
      }

      if (category) {
        query = query.eq('category', category);
      }

      if (isPublic !== undefined) {
        query = query.eq('is_public', isPublic);
      }

      if (isActive !== undefined) {
        query = query.eq('is_active', isActive);
      }

      if (isPremium !== undefined) {
        query = query.eq('is_premium', isPremium);
      }

      // 添加排序
      query = query.order('created_at', { ascending: false });
      
      // 计算总数
      let countQuery = supabase
        .from(TABLE_NAMES.template)
        .select('*', { count: 'exact', head: true });
      
      if (keyword) {
        countQuery = countQuery.or(`name.ilike.%${keyword}%,description.ilike.%${keyword}%`);
      }
      if (category) {
        countQuery = countQuery.eq('category', category);
      }
      if (isPublic !== undefined) {
        countQuery = countQuery.eq('is_public', isPublic);
      }
      if (isActive !== undefined) {
        countQuery = countQuery.eq('is_active', isActive);
      }
      if (isPremium !== undefined) {
        countQuery = countQuery.eq('is_premium', isPremium);
      }
      
      const countResult = await countQuery;
      const total = countResult.count || 0;

      // 分页查询
      const offset = (page - 1) * pageSize;
      const result = await query.range(offset, offset + pageSize - 1);
      const { data: templates, error } = handleSupabaseResult(result);
      
      if (error) throw error;

      console.log(`📋 [ADMIN-TEMPLATES] 查询到 ${templates.length} 个模板，共 ${total} 个`);
      console.log('🔍 [ADMIN-TEMPLATES] 原始数据样本:', templates?.[0] ? JSON.stringify(templates[0]) : 'No data');

      // 转换数据格式
      const formattedTemplates = (templates || []).map((template: any) => ({
        id: template.id,
        name: template.name,
        description: template.description,
        category: template.category,
        previewImage: template.previewImage,
        prompt: template.prompt,
        negativePrompt: template.negativePrompt,
        pointsCost: template.pointsCost,
        isPremium: template.isPremium,
        isPublic: template.isPublic,
        isActive: template.isActive,
        tags: Array.isArray(template.tags) ? template.tags : [],
        settings: template.settings || {},
        createdBy: template.createdBy,
        usageCount: template.usageCount,
        createdAt: template.createdAt,
        updatedAt: template.updatedAt,
      }));

      console.log('📤 [ADMIN-TEMPLATES] 格式化后数据样本:', formattedTemplates?.[0] ? JSON.stringify(formattedTemplates[0]) : 'No data');

      return c.json({
        success: true,
        data: {
          data: formattedTemplates,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize),
        },
      });
    } catch (error) {
      console.error('❌ [ADMIN-TEMPLATES] 获取模板列表失败:', error);
      return c.json(
        {
          success: false,
          message: '获取模板列表失败',
        },
        500
      );
    }
  }
);

// ==================== 模板分类和统计 ====================

// 获取模板分类列表 (需要在 /:id 之前定义)
adminTemplates.get('/categories', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const env = c.env;

    console.log('📂 [ADMIN-TEMPLATES] 获取模板分类');

    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.template)
      .select('category')
      .eq('is_public', true)
      .eq('is_active', true)
      .not('category', 'is', null);

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;

    // 手动计算分类统计
    const categoryStats = (data || []).reduce(
      (acc: any, item: any) => {
        const category = item.category;
        if (category) {
          acc[category] = (acc[category] || 0) + 1;
        }
        return acc;
      },
      {} as Record<string, number>
    );

    // 转换为数组并按数量排序，提取分类名称
    const categories = Object.entries(categoryStats)
      .map(([category]) => category)
      .sort();

    return c.json({
      success: true,
      data: categories,
    });
  } catch (error) {
    console.error('❌ [ADMIN-TEMPLATES] 获取模板分类失败:', error);
    return c.json(
      {
        success: false,
        message: '获取分类失败',
      },
      500
    );
  }
});

// 获取模板统计数据
adminTemplates.get('/stats/summary', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const env = c.env;

    console.log('📊 [ADMIN-TEMPLATES] 获取模板统计');

    const supabase = getSupabase(env);

    // 并行查询各种统计数据
    const [
      totalTemplatesResult,
      publicTemplatesResult,
      activeTemplatesResult,
      premiumTemplatesResult,
      totalUsageResult,
    ] = await Promise.all([
      // 总模板数
      supabase.from(TABLE_NAMES.template).select('*', { count: 'exact', head: true }),
      
      // 公开模板数
      supabase.from(TABLE_NAMES.template).select('*', { count: 'exact', head: true }).eq('is_public', true),
      
      // 启用模板数
      supabase.from(TABLE_NAMES.template).select('*', { count: 'exact', head: true }).eq('is_active', true),
      
      // 会员专享模板数
      supabase.from(TABLE_NAMES.template).select('*', { count: 'exact', head: true }).eq('is_premium', true),
      
      // 总使用次数
      supabase.from(TABLE_NAMES.template).select('usage_count'),
    ]);

    // 计算总使用次数
    const totalUsage = totalUsageResult.data?.reduce((sum, item) => sum + (item.usage_count || 0), 0) || 0;

    const stats = {
      totalTemplates: totalTemplatesResult.count || 0,
      publicTemplates: publicTemplatesResult.count || 0,
      privateTemplates: (totalTemplatesResult.count || 0) - (publicTemplatesResult.count || 0),
      activeTemplates: activeTemplatesResult.count || 0,
      premiumTemplates: premiumTemplatesResult.count || 0,
      totalUsage,
    };

    console.log('📊 [ADMIN-TEMPLATES] 统计结果:', stats);

    return c.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('❌ [ADMIN-TEMPLATES] 获取模板统计失败:', error);
    return c.json(
      {
        success: false,
        message: '获取统计数据失败',
      },
      500
    );
  }
});

// ==================== 模板详情管理 ====================

// 获取模板详情
adminTemplates.get('/:id', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const id = c.req.param('id');
    const env = c.env;

    console.log('🔍 [ADMIN-TEMPLATES] 获取模板详情:', id);

    // 查询模板详情
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.template)
      .select('*')
      .eq('id', id)
      .single();

    const { data: template, error } = handleSupabaseResult(result);
    if (error || !template) {
      return c.json(
        {
          success: false,
          message: '模板不存在',
        },
        404
      );
    }

    // 格式化数据
    const formattedTemplate = {
      id: template.id,
      name: template.name,
      description: template.description,
      category: template.category,
      previewImage: template.previewImage,
      prompt: template.prompt,
      negativePrompt: template.negativePrompt,
      pointsCost: template.pointsCost,
      isPremium: template.isPremium,
      isPublic: template.isPublic,
      isActive: template.isActive,
      tags: Array.isArray(template.tags) ? template.tags : [],
      settings: template.settings || {},
      createdBy: template.createdBy,
      usageCount: template.usageCount,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
    };

    return c.json({
      success: true,
      data: formattedTemplate,
    });
  } catch (error) {
    console.error('❌ [ADMIN-TEMPLATES] 获取模板详情失败:', error);
    return c.json(
      {
        success: false,
        message: '获取模板详情失败',
      },
      500
    );
  }
});

// ==================== 模板创建和编辑 ====================

// 创建模板验证模式
const createTemplateSchema = z.object({
  name: z.string().min(1, '模板名称不能为空'),
  description: z.string().optional(),
  category: z.string().min(1, '分类不能为空'),
  previewImage: z.string().optional(),
  prompt: z.string().min(1, '提示词不能为空'),
  negativePrompt: z.string().optional(),
  pointsCost: z.number().default(1),
  isPremium: z.boolean().default(false),
  isPublic: z.boolean().default(true),
  isActive: z.boolean().default(true),
  tags: z.array(z.string()).default([]),
  settings: z.object({}).passthrough().default({}),
});

// 创建模板
adminTemplates.post('/', authMiddleware, zValidator('json', createTemplateSchema), async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const templateData = c.req.valid('json');
    const env = c.env;

    console.log('➕ [ADMIN-TEMPLATES] 创建模板:', templateData.name);

    // 获取创建者用户ID
    const supabaseUser = c.get('user');
    if (!supabaseUser) {
      return c.json({ success: false, message: '用户信息获取失败' }, 401);
    }

    // 查找对应的数据库用户
    const dbUser = await getUserBySupabaseId(env, supabaseUser.id);

    if (!dbUser) {
      return c.json({ success: false, message: '管理员用户不存在' }, 404);
    }

    const createdBy = dbUser.id;

    // 创建模板
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.template)
      .insert({
        name: templateData.name,
        description: templateData.description,
        category: templateData.category,
        preview_image: templateData.previewImage,
        prompt: templateData.prompt,
        negative_prompt: templateData.negativePrompt,
        points_cost: templateData.pointsCost,
        is_premium: templateData.isPremium,
        is_public: templateData.isPublic,
        is_active: templateData.isActive,
        tags: templateData.tags,
        settings: templateData.settings,
        created_by: createdBy,
      })
      .select()
      .single();

    const { data: newTemplate, error } = handleSupabaseResult(result);
    if (error || !newTemplate) {
      return c.json({ success: false, message: '创建模板失败' }, 500);
    }

    console.log('✅ [ADMIN-TEMPLATES] 模板创建成功:', newTemplate.id);

    return c.json({
      success: true,
      data: {
        id: newTemplate.id,
        name: newTemplate.name,
        description: newTemplate.description,
        category: newTemplate.category,
        previewImage: newTemplate.previewImage,
        prompt: newTemplate.prompt,
        negativePrompt: newTemplate.negativePrompt,
        pointsCost: newTemplate.pointsCost,
        isPremium: newTemplate.isPremium,
        isPublic: newTemplate.isPublic,
        isActive: newTemplate.isActive,
        tags: Array.isArray(newTemplate.tags) ? newTemplate.tags : [],
        settings: newTemplate.settings || {},
        createdBy: newTemplate.createdBy,
        usageCount: newTemplate.usageCount,
        createdAt: newTemplate.createdAt,
        updatedAt: newTemplate.updatedAt,
      },
      message: '模板创建成功'
    });
  } catch (error) {
    console.error('❌ [ADMIN-TEMPLATES] 创建模板失败:', error);
    return c.json(
      {
        success: false,
        message: '创建模板失败',
      },
      500
    );
  }
});

// 更新模板
adminTemplates.put('/:id', authMiddleware, zValidator('json', createTemplateSchema.partial()), async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const id = c.req.param('id');
    const updateData = c.req.valid('json');
    const env = c.env;

    console.log('📝 [ADMIN-TEMPLATES] 更新模板:', id);

    // 更新模板
    const supabase = getSupabase(env);
    
    // 构建更新数据
    const updateFields: any = {
      updated_at: new Date().toISOString(),
    };

    if (updateData.name !== undefined) updateFields.name = updateData.name;
    if (updateData.description !== undefined) updateFields.description = updateData.description;
    if (updateData.category !== undefined) updateFields.category = updateData.category;
    if (updateData.previewImage !== undefined) updateFields.preview_image = updateData.previewImage;
    if (updateData.prompt !== undefined) updateFields.prompt = updateData.prompt;
    if (updateData.negativePrompt !== undefined) updateFields.negative_prompt = updateData.negativePrompt;
    if (updateData.pointsCost !== undefined) updateFields.points_cost = updateData.pointsCost;
    if (updateData.isPremium !== undefined) updateFields.is_premium = updateData.isPremium;
    if (updateData.isPublic !== undefined) updateFields.is_public = updateData.isPublic;
    if (updateData.isActive !== undefined) updateFields.is_active = updateData.isActive;
    if (updateData.tags !== undefined) updateFields.tags = updateData.tags;
    if (updateData.settings !== undefined) updateFields.settings = updateData.settings;

    const result = await supabase
      .from(TABLE_NAMES.template)
      .update(updateFields)
      .eq('id', id)
      .select()
      .single();

    const { data: updatedTemplate, error } = handleSupabaseResult(result);
    if (error || !updatedTemplate) {
      return c.json({ success: false, message: '模板不存在' }, 404);
    }

    console.log('✅ [ADMIN-TEMPLATES] 模板更新成功:', id);

    return c.json({
      success: true,
      data: {
        id: updatedTemplate.id,
        name: updatedTemplate.name,
        description: updatedTemplate.description,
        category: updatedTemplate.category,
        previewImage: updatedTemplate.previewImage,
        prompt: updatedTemplate.prompt,
        negativePrompt: updatedTemplate.negativePrompt,
        pointsCost: updatedTemplate.pointsCost,
        isPremium: updatedTemplate.isPremium,
        isPublic: updatedTemplate.isPublic,
        isActive: updatedTemplate.isActive,
        tags: Array.isArray(updatedTemplate.tags) ? updatedTemplate.tags : [],
        settings: updatedTemplate.settings || {},
        createdBy: updatedTemplate.createdBy,
        usageCount: updatedTemplate.usageCount,
        createdAt: updatedTemplate.createdAt,
        updatedAt: updatedTemplate.updatedAt,
      },
      message: '模板更新成功'
    });
  } catch (error) {
    console.error('❌ [ADMIN-TEMPLATES] 更新模板失败:', error);
    return c.json(
      {
        success: false,
        message: '更新模板失败',
      },
      500
    );
  }
});

// 删除模板
adminTemplates.delete('/:id', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const id = c.req.param('id');
    const env = c.env;

    console.log('🗑️ [ADMIN-TEMPLATES] 删除模板:', id);

    // 删除模板（软删除）
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.template)
      .update({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    const { data: deletedTemplate, error } = handleSupabaseResult(result);
    if (error || !deletedTemplate) {
      return c.json({ success: false, message: '模板不存在' }, 404);
    }

    console.log('✅ [ADMIN-TEMPLATES] 模板删除成功:', id);

    return c.json({
      success: true,
      message: '模板删除成功'
    });
  } catch (error) {
    console.error('❌ [ADMIN-TEMPLATES] 删除模板失败:', error);
    return c.json(
      {
        success: false,
        message: '删除模板失败',
      },
      500
    );
  }
});

// ==================== 模板状态管理 ====================

// 切换模板启用状态
adminTemplates.post('/:id/toggle-status', authMiddleware, zValidator('json', z.object({
  isActive: z.boolean()
})), async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const id = c.req.param('id');
    const { isActive } = c.req.valid('json');
    const env = c.env;

    console.log('🔄 [ADMIN-TEMPLATES] 切换模板状态:', id, isActive);

    // 更新状态
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.template)
      .update({
        is_active: isActive,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    const { data: updatedTemplate, error } = handleSupabaseResult(result);
    if (error || !updatedTemplate) {
      return c.json({ success: false, message: '模板不存在' }, 404);
    }

    console.log('✅ [ADMIN-TEMPLATES] 模板状态更新成功:', id);

    return c.json({
      success: true,
      message: isActive ? '模板已启用' : '模板已禁用'
    });
  } catch (error) {
    console.error('❌ [ADMIN-TEMPLATES] 切换模板状态失败:', error);
    return c.json(
      {
        success: false,
        message: '状态更新失败',
      },
      500
    );
  }
});

// 切换模板公开状态
adminTemplates.post('/:id/toggle-public', authMiddleware, zValidator('json', z.object({
  isPublic: z.boolean()
})), async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const id = c.req.param('id');
    const { isPublic } = c.req.valid('json');
    const env = c.env;

    console.log('🔄 [ADMIN-TEMPLATES] 切换模板公开状态:', id, isPublic);

    // 更新公开状态
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.template)
      .update({
        is_public: isPublic,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    const { data: updatedTemplate, error } = handleSupabaseResult(result);
    if (error || !updatedTemplate) {
      return c.json({ success: false, message: '模板不存在' }, 404);
    }

    console.log('✅ [ADMIN-TEMPLATES] 模板公开状态更新成功:', id);

    return c.json({
      success: true,
      message: isPublic ? '模板已设为公开' : '模板已设为私有'
    });
  } catch (error) {
    console.error('❌ [ADMIN-TEMPLATES] 切换模板公开状态失败:', error);
    return c.json(
      {
        success: false,
        message: '公开状态更新失败',
      },
      500
    );
  }
});

export default adminTemplates;