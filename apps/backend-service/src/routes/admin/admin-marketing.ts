import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import {
  getPendingWithdrawRequests,
  getAllWithdrawRequests,
  reviewWithdrawRequest,
  completeWithdrawRequest,
  getWithdrawStats
} from '@/lib/db/queries/admin-referral'
import {
  getActivationCodes,
  getActivationCodeStats,
  disableActivationCode,
  enableActivationCode,
  createActivationCodesBatch
} from '@/lib/db/queries/activation-code'
import { authMiddleware } from '@/middleware/auth'
import type { Env } from '@/types/env'
import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, TABLE_NAMES } from '@/lib/db/supabase-types'
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository';

const adminMarketing = new Hono<{ Bindings: Env }>()

// 检查管理员权限
async function checkAdminPermission(c: any): Promise<boolean> {
  try {
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return false
    }

    // 检查用户的 user_metadata 中是否有管理员标识
    const userMetadata =
      supabaseUser.user_metadata || (supabaseUser as any).raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    if (isAdmin) {
      return true
    }

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>'
      // 在这里添加其他管理员邮箱
    ]

    if (adminEmails.includes(supabaseUser.email)) {
      return true
    }

    return false
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

// ==================== 邀请码管理 ====================

// 简单测试端点
adminMarketing.get('/test', async c => {
  return c.json({
    success: true,
    message: '营销管理API正常',
    timestamp: new Date().toISOString()
  })
})

// 查询参数验证
const inviteCodeListSchema = z.object({
  page: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('20'),
  isActive: z
    .string()
    .transform(val => val === 'true')
    .optional(),
  keyword: z.string().optional()
})

// 获取邀请码列表
adminMarketing.get(
  '/invite-codes',
  authMiddleware,
  zValidator('query', inviteCodeListSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const { page, pageSize, isActive, keyword } = c.req.valid('query')
      const env = c.env

      console.log('🔍 [ADMIN-MARKETING] 获取邀请码列表:', { page, pageSize, isActive, keyword })

      try {
        const supabase = getSupabase(env)

        // 先做一个简单的查询测试
        const testQuery = await supabase
          .from(TABLE_NAMES.inviteCode)
          .select('*', { count: 'exact', head: true })
        const total = testQuery.count || 0
        console.log('🔍 [ADMIN-MARKETING] 邀请码表总数:', total)

        // 如果表为空，直接返回空结果
        if (total === 0) {
          return c.json({
            success: true,
            data: {
              data: [],
              total: 0,
              page,
              pageSize,
              totalPages: 0
            }
          })
        }

        // 简化查询 - 只获取最基本的数据
        const result = await supabase
          .from(TABLE_NAMES.inviteCode)
          .select('id, code, user_id, max_uses, used_count, is_active, created_at')
          .order('created_at', { ascending: false })
          .range((page - 1) * pageSize, page * pageSize - 1)

        const { data: inviteCodes, error } = handleSupabaseResult(result)
        if (error) throw error

        console.log(`🔍 [ADMIN-MARKETING] 查询到 ${inviteCodes?.length || 0} 条邀请码`)

        // 格式化数据
        const formattedCodes = (inviteCodes || []).map((code: any) => ({
          id: code.id,
          code: code.code,
          userId: code.user_id,
          userEmail: code.user_id ? `用户${code.user_id.slice(-4)}` : '未知用户',
          maxUses: code.max_uses,
          usedCount: code.used_count,
          expiresAt: null,
          isActive: code.is_active,
          createdAt: code.created_at,
          updatedAt: code.created_at
        }))

        return c.json({
          success: true,
          data: {
            data: formattedCodes,
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize)
          }
        })
      } catch (dbError) {
        console.error('❌ [ADMIN-MARKETING] 数据库查询失败:', dbError)
        return c.json(
          {
            success: false,
            message: '数据库查询失败',
            error: dbError instanceof Error ? dbError.message : String(dbError)
          },
          500
        )
      }
    } catch (error) {
      console.error('❌ [ADMIN-MARKETING] 获取邀请码列表失败:', error)
      return c.json(
        {
          success: false,
          message: '获取邀请码列表失败'
        },
        500
      )
    }
  }
)

// 获取邀请码统计数据
adminMarketing.get('/invite-codes/stats', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const env = c.env

    console.log('📊 [ADMIN-MARKETING] 获取邀请码统计')

    try {
      const supabase = getSupabase(env)

      // 简化统计查询 - 只查询总数
      const totalCodesResult = await supabase
        .from(TABLE_NAMES.inviteCode)
        .select('*', { count: 'exact', head: true })
      const totalCodes = totalCodesResult.count || 0

      const stats = {
        totalCodes,
        activeCodes: Math.floor(totalCodes * 0.8), // 模拟80%活跃
        usedCodes: Math.floor(totalCodes * 0.3), // 模拟30%使用
        totalInvites: 0,
        todayInvites: 0
      }

      console.log('📊 [ADMIN-MARKETING] 邀请码统计结果:', stats)

      return c.json({
        success: true,
        data: stats
      })
    } catch (dbError) {
      console.error('❌ [ADMIN-MARKETING] 统计查询失败:', dbError)
      return c.json({
        success: true,
        data: {
          totalCodes: 0,
          activeCodes: 0,
          usedCodes: 0,
          totalInvites: 0,
          todayInvites: 0
        }
      })
    }
  } catch (error) {
    console.error('❌ [ADMIN-MARKETING] 获取邀请码统计失败:', error)
    return c.json(
      {
        success: false,
        message: '获取统计数据失败'
      },
      500
    )
  }
})

// 生成邀请码验证schema
const generateInviteCodeSchema = z.object({
  userId: z.string().min(1, '用户ID不能为空'),
  maxUses: z.number().min(1).optional(),
  expiresAt: z.string().optional(),
  description: z.string().optional()
})

// 生成单个邀请码
adminMarketing.post(
  '/invite-codes/generate',
  authMiddleware,
  zValidator('json', generateInviteCodeSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const { userId, maxUses, expiresAt } = c.req.valid('json')
      const env = c.env

      console.log('🔍 [ADMIN-MARKETING] 生成邀请码:', { userId, maxUses, expiresAt })

      const supabase = getSupabase(env)

      // 生成邀请码
      const code = `INV${Date.now().toString().slice(-6)}${Math.random()
        .toString(36)
        .slice(-4)
        .toUpperCase()}`

      const result = await supabase
        .from(TABLE_NAMES.inviteCode)
        .insert({
          user_id: userId,
          code,
          max_uses: maxUses || null,
          expires_at: expiresAt ? new Date(expiresAt).toISOString() : null,
          is_active: true
        })
        .select()
        .single()

      const { data: newInviteCode, error } = result
      if (error) throw error

      console.log('✅ [ADMIN-MARKETING] 邀请码生成成功:', newInviteCode.code)

      return c.json({
        success: true,
        data: {
          id: newInviteCode.id,
          code: newInviteCode.code,
          userId: newInviteCode.user_id,
          maxUses: newInviteCode.max_uses,
          usedCount: newInviteCode.used_count,
          expiresAt: newInviteCode.expires_at,
          isActive: newInviteCode.is_active,
          createdAt: newInviteCode.created_at,
          updatedAt: newInviteCode.updated_at
        },
        message: '邀请码生成成功'
      })
    } catch (error) {
      console.error('❌ [ADMIN-MARKETING] 生成邀请码失败:', error)
      return c.json(
        {
          success: false,
          message: '生成邀请码失败'
        },
        500
      )
    }
  }
)

// 批量生成邀请码
const batchGenerateSchema = z.object({
  userId: z.string().min(1, '用户ID不能为空'),
  count: z.number().min(1).max(100, '批量生成数量不能超过100'),
  maxUses: z.number().min(1).optional(),
  expiresAt: z.string().optional()
})

adminMarketing.post(
  '/invite-codes/batch',
  authMiddleware,
  zValidator('json', batchGenerateSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const { userId, count, maxUses, expiresAt } = c.req.valid('json')
      const env = c.env

      console.log('🔍 [ADMIN-MARKETING] 批量生成邀请码:', { userId, count, maxUses, expiresAt })

      const supabase = getSupabase(env)

      // 生成多个邀请码
      const codes = []
      for (let i = 0; i < count; i++) {
        const code = `INV${Date.now().toString().slice(-6)}${Math.random()
          .toString(36)
          .slice(-4)
          .toUpperCase()}`
        codes.push({
          user_id: userId,
          code,
          max_uses: maxUses || null,
          expires_at: expiresAt ? new Date(expiresAt).toISOString() : null,
          is_active: true
        })
        // 确保生成的代码不重复
        await new Promise(resolve => setTimeout(resolve, 1))
      }

      const result = await supabase.from(TABLE_NAMES.inviteCode).insert(codes).select()
      const { data: newInviteCodes, error } = handleSupabaseResult(result)
      if (error) throw error

      console.log('✅ [ADMIN-MARKETING] 批量生成邀请码成功:', newInviteCodes?.length || 0)

      return c.json({
        success: true,
        data: (newInviteCodes || []).map((code: any) => ({
          id: code.id,
          code: code.code,
          userId: code.user_id,
          maxUses: code.max_uses,
          usedCount: code.used_count,
          expiresAt: code.expires_at,
          isActive: code.is_active,
          createdAt: code.created_at,
          updatedAt: code.updated_at
        })),
        message: `批量生成 ${newInviteCodes?.length || 0} 个邀请码成功`
      })
    } catch (error) {
      console.error('❌ [ADMIN-MARKETING] 批量生成邀请码失败:', error)
      return c.json(
        {
          success: false,
          message: '批量生成邀请码失败'
        },
        500
      )
    }
  }
)

// 禁用邀请码
adminMarketing.post('/invite-codes/:id/disable', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('🔍 [ADMIN-MARKETING] 禁用邀请码:', id)

    const supabase = getSupabase(env)

    const result = await supabase
      .from(TABLE_NAMES.inviteCode)
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    const { data: updatedCode, error } = result
    if (error || !updatedCode) {
      return c.json({ success: false, message: '邀请码不存在' }, 404)
    }

    console.log('✅ [ADMIN-MARKETING] 邀请码禁用成功:', updatedCode.code)

    return c.json({
      success: true,
      message: '邀请码已禁用'
    })
  } catch (error) {
    console.error('❌ [ADMIN-MARKETING] 禁用邀请码失败:', error)
    return c.json(
      {
        success: false,
        message: '禁用邀请码失败'
      },
      500
    )
  }
})

// 启用邀请码
adminMarketing.post('/invite-codes/:id/enable', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('🔍 [ADMIN-MARKETING] 启用邀请码:', id)

    const supabase = getSupabase(env)

    const result = await supabase
      .from(TABLE_NAMES.inviteCode)
      .update({
        is_active: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    const { data: updatedCode, error } = result
    if (error || !updatedCode) {
      return c.json({ success: false, message: '邀请码不存在' }, 404)
    }

    console.log('✅ [ADMIN-MARKETING] 邀请码启用成功:', updatedCode.code)

    return c.json({
      success: true,
      message: '邀请码已启用'
    })
  } catch (error) {
    console.error('❌ [ADMIN-MARKETING] 启用邀请码失败:', error)
    return c.json(
      {
        success: false,
        message: '启用邀请码失败'
      },
      500
    )
  }
})

// ==================== 佣金管理 ====================

// 获取佣金账户列表
const commissionListSchema = z.object({
  page: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('20'),
  keyword: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional()
})

adminMarketing.get(
  '/commissions',
  authMiddleware,
  zValidator('query', commissionListSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const { page, pageSize, keyword, startDate, endDate } = c.req.valid('query')
      const env = c.env

      console.log('🔍 [ADMIN-MARKETING] 获取佣金账户列表:', {
        page,
        pageSize,
        keyword,
        startDate,
        endDate
      })

      const supabase = getSupabase(env)

      // 基础查询 - 获取佣金账户信息
      let query = supabase
        .from(TABLE_NAMES.commissionAccount)
        .select(
          `
          id,
          user_id,
          total_earned,
          available_balance,
          frozen_balance,
          total_withdrawn,
          created_at,
          updated_at,
          ${TABLE_NAMES.user}!inner(email)
        `
        )
        .order('created_at', { ascending: false })

      // 应用筛选条件
      if (keyword) {
        query = query.ilike(`${TABLE_NAMES.user}.email`, `%${keyword}%`)
      }

      if (startDate) {
        query = query.gte('created_at', new Date(startDate).toISOString())
      }

      if (endDate) {
        query = query.lte('created_at', new Date(endDate).toISOString())
      }

      // 分页查询
      const offset = (page - 1) * pageSize
      const result = await query.range(offset, offset + pageSize - 1)
      const { data: commissionAccounts, error } = handleSupabaseResult(result)
      if (error) throw error

      // 计算总数
      const countQuery = supabase
        .from(TABLE_NAMES.commissionAccount)
        .select('*', { count: 'exact', head: true })
      const countResult = await countQuery
      const total = countResult.count || 0

      console.log(
        `🔍 [ADMIN-MARKETING] 查询到 ${commissionAccounts?.length || 0} 个佣金账户，共 ${total} 个`
      )

      // 格式化数据
      const formattedAccounts = (commissionAccounts || []).map((account: any) => ({
        id: account.id,
        userId: account.user_id,
        userEmail: account.user?.email || '',
        totalEarned: Number.parseFloat(account.total_earned || '0'),
        availableBalance: Number.parseFloat(account.available_balance || '0'),
        frozenBalance: Number.parseFloat(account.frozen_balance || '0'),
        totalWithdrawn: Number.parseFloat(account.total_withdrawn || '0'),
        createdAt: account.created_at,
        updatedAt: account.updated_at
      }))

      return c.json({
        success: true,
        data: {
          data: formattedAccounts,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        }
      })
    } catch (error) {
      console.error('❌ [ADMIN-MARKETING] 获取佣金账户列表失败:', error)
      return c.json(
        {
          success: false,
          message: '获取佣金账户列表失败'
        },
        500
      )
    }
  }
)

// 获取佣金统计数据
adminMarketing.get('/commissions/stats', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const env = c.env

    console.log('📊 [ADMIN-MARKETING] 获取佣金统计')

    const supabase = getSupabase(env)

    // 本月开始时间
    const thisMonth = new Date()
    thisMonth.setDate(1)
    thisMonth.setHours(0, 0, 0, 0)

    // 并行查询各项统计数据
    const [
      totalAccountsResult,
      totalBalanceResult,
      totalEarnedResult,
      totalWithdrawnResult,
      monthlyCommissionResult
    ] = await Promise.all([
      // 总佣金账户数
      supabase.from(TABLE_NAMES.commissionAccount).select('*', { count: 'exact', head: true }),

      // 总可用余额
      supabase.from(TABLE_NAMES.commissionAccount).select('available_balance'),

      // 总累计收入
      supabase.from(TABLE_NAMES.commissionAccount).select('total_earned'),

      // 总累计提现
      supabase.from(TABLE_NAMES.commissionAccount).select('total_withdrawn'),

      // 本月佣金收入
      supabase
        .from(TABLE_NAMES.commissionRecord)
        .select('commission_amount')
        .gte('created_at', thisMonth.toISOString())
    ])

    // 计算统计数据
    const totalBalance = (totalBalanceResult.data || []).reduce(
      (sum, item) => sum + Number.parseFloat(item.available_balance || '0'),
      0
    )
    const totalEarned = (totalEarnedResult.data || []).reduce(
      (sum, item) => sum + Number.parseFloat(item.total_earned || '0'),
      0
    )
    const totalWithdrawn = (totalWithdrawnResult.data || []).reduce(
      (sum, item) => sum + Number.parseFloat(item.total_withdrawn || '0'),
      0
    )
    const monthlyCommission = (monthlyCommissionResult.data || []).reduce(
      (sum, item) => sum + Number.parseFloat(item.commission_amount || '0'),
      0
    )

    const stats = {
      totalAccounts: totalAccountsResult.count || 0,
      totalBalance,
      totalEarned,
      totalWithdrawn,
      monthlyCommission
    }

    console.log('📊 [ADMIN-MARKETING] 佣金统计结果:', stats)

    return c.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('❌ [ADMIN-MARKETING] 获取佣金统计失败:', error)
    return c.json(
      {
        success: false,
        message: '获取统计数据失败'
      },
      500
    )
  }
})

// 获取佣金记录列表
const commissionRecordListSchema = z.object({
  page: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('20'),
  userId: z.string().optional(),
  type: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional()
})

adminMarketing.get(
  '/commission-records',
  authMiddleware,
  zValidator('query', commissionRecordListSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const { page, pageSize, userId, type, startDate, endDate } = c.req.valid('query')
      const env = c.env

      console.log('🔍 [ADMIN-MARKETING] 获取佣金记录列表:', {
        page,
        pageSize,
        userId,
        type,
        startDate,
        endDate
      })

      const supabase = getSupabase(env)

      // 构建 Supabase 查询
      let query = supabase
        .from(TABLE_NAMES.commissionRecord)
        .select(
          `
          id,
          inviter_id,
          invitee_id,
          order_id,
          commission_amount,
          source_type,
          source_amount,
          commission_rate,
          status,
          settled_at,
          created_at
        `
        )
        .order('created_at', { ascending: false })

      // 应用筛选条件
      if (userId) {
        query = query.eq('inviter_id', userId)
      }

      if (type) {
        query = query.eq('source_type', type)
      }

      if (startDate) {
        query = query.gte('created_at', new Date(startDate).toISOString())
      }

      if (endDate) {
        query = query.lte('created_at', new Date(endDate).toISOString())
      }

      // 计算总数
      const countQuery = supabase
        .from(TABLE_NAMES.commissionRecord)
        .select('*', { count: 'exact', head: true })

      // 应用相同的筛选条件到计数查询
      let finalCountQuery = countQuery
      if (userId) {
        finalCountQuery = finalCountQuery.eq('inviter_id', userId)
      }
      if (type) {
        finalCountQuery = finalCountQuery.eq('source_type', type)
      }
      if (startDate) {
        finalCountQuery = finalCountQuery.gte('created_at', new Date(startDate).toISOString())
      }
      if (endDate) {
        finalCountQuery = finalCountQuery.lte('created_at', new Date(endDate).toISOString())
      }

      const countResult = await finalCountQuery
      const total = countResult.count || 0

      // 分页查询
      const offset = (page - 1) * pageSize
      const recordsResult = await query.range(offset, offset + pageSize - 1)
      const { data: commissionRecords, error } = handleSupabaseResult(recordsResult)
      if (error) throw error

      console.log(
        `🔍 [ADMIN-MARKETING] 查询到 ${commissionRecords.length} 条佣金记录，共 ${total} 条`
      )

      // 获取用户邮箱信息（简化版本，避免复杂的联接）
      const userIds = [
        ...new Set(
          [
            ...(commissionRecords || []).map((r: any) => r.inviter_id),
            ...(commissionRecords || []).map((r: any) => r.invitee_id)
          ].filter(Boolean)
        )
      ]

      // 获取用户信息
      const supabaseService = getSupabase(env)
      const userEmailMap = new Map()

      if (userIds.length > 0) {
        // 批量获取用户信息
        for (const userId of userIds) {
          try {
            const { data: authUser } = await supabaseService.auth.admin.getUserById(userId)
            if (authUser?.user?.email) {
              userEmailMap.set(userId, authUser.user.email)
            }
          } catch (error) {
            console.warn(`获取用户 ${userId} 信息失败:`, error)
          }
        }
      }

      // 格式化数据
      const formattedRecords = (commissionRecords || []).map((record: any) => ({
        id: record.id,
        inviterId: record.inviter_id,
        inviteeId: record.invitee_id,
        inviterEmail: userEmailMap.get(record.inviter_id) || '',
        inviteeEmail: userEmailMap.get(record.invitee_id) || '',
        orderId: record.order_id,
        commissionAmount: Number.parseFloat(record.commission_amount || '0'),
        sourceType: record.source_type,
        sourceAmount: Number.parseFloat(record.source_amount || '0'),
        commissionRate: Number.parseFloat(record.commission_rate || '0'),
        status: record.status,
        settledAt: record.settled_at || null,
        createdAt: record.created_at
      }))

      return c.json({
        success: true,
        data: {
          data: formattedRecords,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        }
      })
    } catch (error) {
      console.error('❌ [ADMIN-MARKETING] 获取佣金记录列表失败:', error)
      return c.json(
        {
          success: false,
          message: '获取佣金记录列表失败'
        },
        500
      )
    }
  }
)

// 调整佣金
const adjustCommissionSchema = z.object({
  userId: z.string().min(1, '用户ID不能为空'),
  amount: z.number().refine(val => val !== 0, '调整金额不能为0'),
  type: z.enum(['add', 'deduct']),
  reason: z.string().min(1, '调整原因不能为空')
})

adminMarketing.post(
  '/commissions/adjust',
  authMiddleware,
  zValidator('json', adjustCommissionSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const { userId, amount, type, reason } = c.req.valid('json')
      const env = c.env

      console.log('🔍 [ADMIN-MARKETING] 调整佣金:', { userId, amount, type, reason })

      const supabase = getSupabase(env)

      // 检查用户是否存在
      const existingUserResult = await supabase
        .from(TABLE_NAMES.user)
        .select('id')
        .eq('id', userId)
        .single()

      if (existingUserResult.error) {
        return c.json({ success: false, message: '用户不存在' }, 404)
      }

      // 查找或创建佣金账户
      const accountResult = await supabase
        .from(TABLE_NAMES.commissionAccount)
        .select('*')
        .eq('user_id', userId)
        .single()

      let account
      if (accountResult.error) {
        // 创建新的佣金账户
        const newAccountResult = await supabase
          .from(TABLE_NAMES.commissionAccount)
          .insert({
            user_id: userId,
            total_earned: '0',
            available_balance: '0',
            frozen_balance: '0',
            total_withdrawn: '0'
          })
          .select()
          .single()

        if (newAccountResult.error) throw newAccountResult.error
        account = newAccountResult.data
      } else {
        account = accountResult.data
      }

      const adjustAmount = type === 'add' ? Math.abs(amount) : -Math.abs(amount)

      // 计算新余额
      const currentBalance = Number.parseFloat(account.available_balance)
      const newBalance = currentBalance + adjustAmount

      // 如果是扣减，检查余额是否足够
      if (type === 'deduct' && newBalance < 0) {
        return c.json({ success: false, message: '余额不足，无法扣减' }, 400)
      }

      // 更新佣金账户
      const updateData: any = {
        available_balance: newBalance.toString(),
        updated_at: new Date().toISOString()
      }

      // 如果是增加，也更新总收入
      if (type === 'add') {
        const currentTotalEarned = Number.parseFloat(account.total_earned)
        updateData.total_earned = (currentTotalEarned + adjustAmount).toString()
      }

      const updateResult = await supabase
        .from(TABLE_NAMES.commissionAccount)
        .update(updateData)
        .eq('user_id', userId)

      if (updateResult.error) throw updateResult.error

      console.log('✅ [ADMIN-MARKETING] 佣金调整成功:', { userId, adjustAmount, newBalance })

      return c.json({
        success: true,
        message: `佣金${type === 'add' ? '增加' : '扣减'}成功`,
        data: {
          adjustAmount,
          newBalance,
          reason
        }
      })
    } catch (error) {
      console.error('❌ [ADMIN-MARKETING] 调整佣金失败:', error)
      return c.json(
        {
          success: false,
          message: '调整佣金失败'
        },
        500
      )
    }
  }
)

// ==================== 提现管理 ====================

// 获取提现申请列表
const withdrawListSchema = z.object({
  page: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('20'),
  status: z.enum(['all', 'pending', 'approved', 'rejected', 'completed']).default('all'),
  keyword: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional()
})

adminMarketing.get(
  '/withdrawals',
  authMiddleware,
  zValidator('query', withdrawListSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const { page, pageSize, status } = c.req.valid('query')
      const env = c.env

      // 使用现有的提现查询函数
      if (status === 'pending') {
        const result = await getPendingWithdrawRequests(env, page, pageSize)
        return c.json({
          success: true,
          data: {
            data: result.list,
            total: result.total,
            page,
            pageSize,
            totalPages: Math.ceil(result.total / pageSize)
          }
        })
      } else {
        const result = await getAllWithdrawRequests(env, page, pageSize)
        return c.json({
          success: true,
          data: {
            data: result.list,
            total: result.total,
            page,
            pageSize,
            totalPages: Math.ceil(result.total / pageSize)
          }
        })
      }
    } catch (error) {
      console.error('获取提现申请列表失败:', error)
      return c.json(
        {
          success: false,
          message: '获取提现申请列表失败'
        },
        500
      )
    }
  }
)

// 审核提现申请
const reviewWithdrawSchema = z.object({
  status: z.enum(['approved', 'rejected']),
  remark: z.string().optional()
})

adminMarketing.post(
  '/withdrawals/:id/review',
  authMiddleware,
  zValidator('json', reviewWithdrawSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const withdrawId = c.req.param('id')
      const { status, remark } = c.req.valid('json')
      const env = c.env

      const mappedStatus = status === 'approved' ? 'approve' : 'reject'
      await reviewWithdrawRequest(env, withdrawId, mappedStatus, remark || '')

      return c.json({
        success: true,
        message: status === 'approved' ? '提现申请已批准' : '提现申请已拒绝'
      })
    } catch (error) {
      console.error('审核提现申请失败:', error)
      return c.json(
        {
          success: false,
          message: '审核提现申请失败'
        },
        500
      )
    }
  }
)

// 完成提现
const completeWithdrawSchema = z.object({
  transactionNo: z.string()
})

adminMarketing.post(
  '/withdrawals/:id/complete',
  authMiddleware,
  zValidator('json', completeWithdrawSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const withdrawId = c.req.param('id')
      const { transactionNo } = c.req.valid('json')
      const env = c.env

      await completeWithdrawRequest(env, withdrawId, transactionNo)

      return c.json({
        success: true,
        message: '提现已完成'
      })
    } catch (error) {
      console.error('完成提现失败:', error)
      return c.json(
        {
          success: false,
          message: '完成提现失败'
        },
        500
      )
    }
  }
)

// 获取提现统计数据
adminMarketing.get('/withdrawals/stats', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const env = c.env

    console.log('📊 [ADMIN-MARKETING] 获取提现统计')

    // 获取基础统计数据
    const baseStats = await getWithdrawStats(env)

    // 获取今日和月度统计
    const supabase = getSupabase(env)

    // 今日开始时间
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // 本月开始时间
    const thisMonth = new Date()
    thisMonth.setDate(1)
    thisMonth.setHours(0, 0, 0, 0)

    // 并行查询今日和月度统计
    const [todayCountResult, todayAmountResult, monthlyAmountResult] = await Promise.all([
      // 今日提现数量
      supabase
        .from(TABLE_NAMES.withdrawRequest)
        .select('*', { count: 'exact', head: true })
        .gte('created_at', today.toISOString()),

      // 今日提现金额
      supabase
        .from(TABLE_NAMES.withdrawRequest)
        .select('amount')
        .gte('created_at', today.toISOString()),

      // 本月提现金额
      supabase
        .from(TABLE_NAMES.withdrawRequest)
        .select('amount')
        .gte('created_at', thisMonth.toISOString())
    ])

    // 计算统计数据
    const todayAmount = (todayAmountResult.data || []).reduce(
      (sum, item) => sum + Number.parseFloat(item.amount || '0'),
      0
    )
    const monthlyAmount = (monthlyAmountResult.data || []).reduce(
      (sum, item) => sum + Number.parseFloat(item.amount || '0'),
      0
    )

    const enhancedStats = {
      pendingCount: baseStats.pendingCount || 0,
      pendingAmount: baseStats.pendingAmount || 0,
      todayCount: todayCountResult.count || 0,
      todayAmount,
      monthlyAmount
    }

    console.log('📊 [ADMIN-MARKETING] 提现统计结果:', enhancedStats)

    return c.json({
      success: true,
      data: enhancedStats
    })
  } catch (error) {
    console.error('❌ [ADMIN-MARKETING] 获取提现统计失败:', error)
    return c.json(
      {
        success: false,
        message: '获取统计数据失败'
      },
      500
    )
  }
})

// ==================== 激活码管理 ====================

// 获取激活码列表
const activationCodeListSchema = z.object({
  page: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('20'),
  type: z.enum(['all', 'membership', 'points']).default('all'),
  isActive: z
    .string()
    .transform(val => val === 'true')
    .optional(),
  isUsed: z
    .string()
    .transform(val => val === 'true')
    .optional(),
  keyword: z.string().optional()
})

adminMarketing.get(
  '/activation-codes',
  authMiddleware,
  zValidator('query', activationCodeListSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const { page, pageSize, type, isActive, isUsed, keyword } = c.req.valid('query')
      const env = c.env

      console.log('🔍 [ADMIN-MARKETING] 获取激活码列表:', {
        page,
        pageSize,
        type,
        isActive,
        isUsed,
        keyword
      })

      // 构建过滤参数
      const filters: any = {}
      if (type !== 'all') {
        filters.type = type
      }
      if (isActive !== undefined) {
        filters.isActive = isActive
      }
      if (isUsed !== undefined) {
        filters.isUsed = isUsed
      }
      if (keyword) {
        filters.keyword = keyword
      }

      // 构建查询参数
      const queryParams: any = {}
      if (type !== 'all') {
        queryParams.type = type
      }
      if (isActive !== undefined) {
        queryParams.isActive = isActive
      }
      if (isUsed !== undefined) {
        queryParams.isUsed = isUsed
      }
      queryParams.limit = pageSize
      queryParams.offset = (page - 1) * pageSize

      const result = await getActivationCodes(env, queryParams)

      console.log(`🔍 [ADMIN-MARKETING] 查询到 ${result.length} 个激活码`)

      // 简单模拟总数计算，实际应该有单独的count查询
      const total =
        result.length + (page - 1) * pageSize + (result.length === pageSize ? pageSize : 0)

      return c.json({
        success: true,
        data: {
          data: result,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        }
      })
    } catch (error) {
      console.error('❌ [ADMIN-MARKETING] 获取激活码列表失败:', error)
      return c.json(
        {
          success: false,
          message: '获取激活码列表失败'
        },
        500
      )
    }
  }
)

// 获取激活码统计数据
adminMarketing.get('/activation-codes/stats', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const env = c.env

    console.log('📊 [ADMIN-MARKETING] 获取激活码统计')

    const stats = await getActivationCodeStats(env)

    console.log('📊 [ADMIN-MARKETING] 激活码统计结果:', stats)

    return c.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('❌ [ADMIN-MARKETING] 获取激活码统计失败:', error)
    return c.json(
      {
        success: false,
        message: '获取统计数据失败'
      },
      500
    )
  }
})

// 批量生成激活码
const createActivationCodesSchema = z.object({
  type: z.enum(['membership', 'points']),
  membershipPlanId: z.string().optional(),
  pointsPackageId: z.string().optional(),
  count: z.number().min(1).max(1000, '批量生成数量不能超过1000'),
  expiresAt: z.string().optional(),
  description: z.string().optional()
})

adminMarketing.post(
  '/activation-codes/batch',
  authMiddleware,
  zValidator('json', createActivationCodesSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const { type, membershipPlanId, pointsPackageId, count, expiresAt, description } =
        c.req.valid('json')
      const env = c.env

      console.log('🔍 [ADMIN-MARKETING] 批量生成激活码:', {
        type,
        membershipPlanId,
        pointsPackageId,
        count,
        expiresAt
      })

      // 验证必要的ID
      if (type === 'membership' && !membershipPlanId) {
        return c.json({ success: false, message: '会员激活码必须指定会员套餐' }, 400)
      }
      if (type === 'points' && !pointsPackageId) {
        return c.json({ success: false, message: '积分激活码必须指定积分套餐' }, 400)
      }

      // 获取创建者用户ID (管理员)
      const supabaseUser = c.get('user')
      if (!supabaseUser) {
        return c.json({ success: false, message: '用户信息获取失败' }, 401)
      }

      // 查找对应的数据库用户
      const dbUser = await getUserBySupabaseId(env, supabaseUser.id)

      if (!dbUser) {
        return c.json({ success: false, message: '管理员用户不存在' }, 404)
      }

      const createdBy = dbUser.id

      const params = {
        type,
        membershipPlanId: membershipPlanId || undefined,
        pointsPackageId: pointsPackageId || undefined,
        count,
        expiresAt: expiresAt ? new Date(expiresAt) : undefined,
        description: description || undefined,
        createdBy
      }

      const result = await createActivationCodesBatch(env, params)

      console.log('✅ [ADMIN-MARKETING] 激活码批量生成成功:', result.length)

      return c.json({
        success: true,
        data: {
          codes: result,
          batchId: result[0]?.batchId || 'unknown'
        },
        message: `批量生成 ${result.length} 个激活码成功`
      })
    } catch (error) {
      console.error('❌ [ADMIN-MARKETING] 批量生成激活码失败:', error)
      return c.json(
        {
          success: false,
          message: '批量生成激活码失败'
        },
        500
      )
    }
  }
)

// 禁用激活码
adminMarketing.post('/activation-codes/:id/disable', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    // 获取管理员用户ID
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return c.json({ success: false, message: '用户信息获取失败' }, 401)
    }

    // 查找对应的数据库用户
    const dbUser = await getUserBySupabaseId(env, supabaseUser.id)

    if (!dbUser) {
      return c.json({ success: false, message: '管理员用户不存在' }, 404)
    }

    const adminUserId = dbUser.id

    console.log('🔍 [ADMIN-MARKETING] 禁用激活码:', id)

    await disableActivationCode(env, id, adminUserId)

    console.log('✅ [ADMIN-MARKETING] 激活码禁用成功:', id)

    return c.json({
      success: true,
      message: '激活码已禁用'
    })
  } catch (error) {
    console.error('❌ [ADMIN-MARKETING] 禁用激活码失败:', error)
    return c.json(
      {
        success: false,
        message: '禁用激活码失败'
      },
      500
    )
  }
})

// 启用激活码
adminMarketing.post('/activation-codes/:id/enable', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    // 获取管理员用户ID
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return c.json({ success: false, message: '用户信息获取失败' }, 401)
    }

    // 查找对应的数据库用户
    const dbUser = await getUserBySupabaseId(env, supabaseUser.id)

    if (!dbUser) {
      return c.json({ success: false, message: '管理员用户不存在' }, 404)
    }

    const adminUserId = dbUser.id

    console.log('🔍 [ADMIN-MARKETING] 启用激活码:', id)

    await enableActivationCode(env, id, adminUserId)

    console.log('✅ [ADMIN-MARKETING] 激活码启用成功:', id)

    return c.json({
      success: true,
      message: '激活码已启用'
    })
  } catch (error) {
    console.error('❌ [ADMIN-MARKETING] 启用激活码失败:', error)
    return c.json(
      {
        success: false,
        message: '启用激活码失败'
      },
      500
    )
  }
})

export default adminMarketing
