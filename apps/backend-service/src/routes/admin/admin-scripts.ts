import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import type { Env } from '@/types/env'
import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, TABLE_NAMES } from '@/lib/db/supabase-types'
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository';

const adminScripts = new Hono<{ Bindings: Env }>()

// 检查管理员权限
async function checkAdminPermission(c: any): Promise<boolean> {
  try {
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return false
    }

    // 检查用户的 user_metadata 中是否有管理员标识
    const userMetadata =
      supabaseUser.user_metadata || (supabaseUser as any).raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    if (isAdmin) {
      return true
    }

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>'
      // 在这里添加其他管理员邮箱
    ]

    if (adminEmails.includes(supabaseUser.email)) {
      return true
    }

    return false
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

// ==================== 剧本列表管理 ====================

// 查询参数验证
const scriptListSchema = z.object({
  page: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('20'),
  keyword: z.string().optional(),
  category: z.string().optional(),
  isPublic: z
    .string()
    .transform(val => val === 'true')
    .optional(),
  isActive: z
    .string()
    .transform(val => val === 'true')
    .optional(),
  isPremium: z
    .string()
    .transform(val => val === 'true')
    .optional()
})

// 获取剧本列表
adminScripts.get('/', authMiddleware, zValidator('query', scriptListSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const { page, pageSize, keyword, category, isPublic, isActive, isPremium } =
      c.req.valid('query')
    const env = c.env

    console.log('📋 [ADMIN-SCRIPTS] 获取剧本列表:', {
      page,
      pageSize,
      keyword,
      category,
      isPublic,
      isActive,
      isPremium
    })

    // 从数据库查询剧本列表
    const supabase = getSupabase(env)

    let query = supabase.from(TABLE_NAMES.script).select('*')

    // 构建筛选条件
    if (keyword) {
      query = query.or(`title.ilike.%${keyword}%,description.ilike.%${keyword}%`)
    }

    if (category) {
      query = query.eq('category', category)
    }

    if (isPublic !== undefined) {
      query = query.eq('is_public', isPublic)
    }

    if (isActive !== undefined) {
      query = query.eq('is_active', isActive)
    }

    if (isPremium !== undefined) {
      query = query.eq('is_premium', isPremium)
    }

    // 添加排序
    query = query.order('created_at', { ascending: false })

    // 计算总数
    let countQuery = supabase.from(TABLE_NAMES.script).select('*', { count: 'exact', head: true })

    if (keyword) {
      countQuery = countQuery.or(`title.ilike.%${keyword}%,description.ilike.%${keyword}%`)
    }
    if (category) {
      countQuery = countQuery.eq('category', category)
    }
    if (isPublic !== undefined) {
      countQuery = countQuery.eq('is_public', isPublic)
    }
    if (isActive !== undefined) {
      countQuery = countQuery.eq('is_active', isActive)
    }
    if (isPremium !== undefined) {
      countQuery = countQuery.eq('is_premium', isPremium)
    }

    const countResult = await countQuery
    const total = countResult.count || 0

    // 分页查询
    const offset = (page - 1) * pageSize
    const result = await query.range(offset, offset + pageSize - 1)
    const { data: scripts, error } = handleSupabaseResult(result)

    if (error) throw error

    console.log(`📋 [ADMIN-SCRIPTS] 查询到 ${scripts.length} 个剧本，共 ${total} 个`)

    // 转换数据格式
    const formattedScripts = (scripts || []).map((script: any) => ({
      id: script.id,
      title: script.title,
      description: script.description,
      coverImage: script.coverImage,
      duration: script.duration,
      tags: Array.isArray(script.tags) ? script.tags : [],
      category: script.category,
      audioUrl: script.audioUrl,
      totalDuration: script.totalDuration,
      stageCount: script.stageCount,
      isPublic: script.isPublic,
      isActive: script.isActive,
      isPremium: script.isPremium,
      pointsCost: script.pointsCost,
      usageCount: script.usageCount,
      rating: script.rating ? Number.parseFloat(script.rating as string) : 0,
      ratingCount: script.ratingCount,
      createdBy: script.createdBy,
      createdAt: script.createdAt,
      updatedAt: script.updatedAt
    }))

    return c.json({
      success: true,
      data: {
        data: formattedScripts,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    })
  } catch (error) {
    console.error('❌ [ADMIN-SCRIPTS] 获取剧本列表失败:', error)
    return c.json(
      {
        success: false,
        message: '获取剧本列表失败'
      },
      500
    )
  }
})

// ==================== 剧本分类和统计 ====================

// 获取剧本分类列表 (需要在 /:id 之前定义)
adminScripts.get('/categories', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const env = c.env

    console.log('📂 [ADMIN-SCRIPTS] 获取剧本分类')

    // 获取分类
    const { getScriptCategories } = await import('@/lib/db/queries/script')
    const categoriesWithCount = await getScriptCategories(env)

    // 提取分类名称
    const categories = categoriesWithCount.map(item => item.category)

    return c.json({
      success: true,
      data: categories
    })
  } catch (error) {
    console.error('❌ [ADMIN-SCRIPTS] 获取剧本分类失败:', error)
    return c.json(
      {
        success: false,
        message: '获取分类失败'
      },
      500
    )
  }
})

// 获取剧本统计数据
adminScripts.get('/stats/summary', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const env = c.env

    console.log('📊 [ADMIN-SCRIPTS] 获取剧本统计')

    const supabase = getSupabase(env)

    // 并行查询各种统计数据
    const [
      totalScriptsResult,
      publicScriptsResult,
      activeScriptsResult,
      premiumScriptsResult,
      totalUsageResult,
      averageRatingResult
    ] = await Promise.all([
      // 总剧本数
      supabase.from(TABLE_NAMES.script).select('*', { count: 'exact', head: true }),

      // 公开剧本数
      supabase
        .from(TABLE_NAMES.script)
        .select('*', { count: 'exact', head: true })
        .eq('is_public', true),

      // 启用剧本数
      supabase
        .from(TABLE_NAMES.script)
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true),

      // 会员专享剧本数
      supabase
        .from(TABLE_NAMES.script)
        .select('*', { count: 'exact', head: true })
        .eq('is_premium', true),

      // 总使用次数
      supabase.from(TABLE_NAMES.script).select('usage_count'),

      // 平均评分
      supabase.from(TABLE_NAMES.script).select('rating, rating_count').gt('rating_count', 0)
    ])

    // 计算总使用次数
    const totalUsage =
      totalUsageResult.data?.reduce((sum, item) => sum + (item.usage_count || 0), 0) || 0

    // 计算平均评分
    const scriptsWithRating = averageRatingResult.data || []
    const totalRating = scriptsWithRating.reduce(
      (sum, item) => sum + (item.rating || 0) * (item.rating_count || 0),
      0
    )
    const totalRatingCount = scriptsWithRating.reduce(
      (sum, item) => sum + (item.rating_count || 0),
      0
    )
    const averageRating = totalRatingCount > 0 ? totalRating / totalRatingCount : 0

    const stats = {
      totalScripts: totalScriptsResult.count || 0,
      publicScripts: publicScriptsResult.count || 0,
      privateScripts: (totalScriptsResult.count || 0) - (publicScriptsResult.count || 0),
      activeScripts: activeScriptsResult.count || 0,
      premiumScripts: premiumScriptsResult.count || 0,
      totalUsage,
      averageRating: Math.round(averageRating * 100) / 100 // 保留两位小数
    }

    console.log('📊 [ADMIN-SCRIPTS] 统计结果:', stats)

    return c.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('❌ [ADMIN-SCRIPTS] 获取剧本统计失败:', error)
    return c.json(
      {
        success: false,
        message: '获取统计数据失败'
      },
      500
    )
  }
})

// ==================== 剧本详情管理 ====================

// 获取剧本详情
adminScripts.get('/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('🔍 [ADMIN-SCRIPTS] 获取剧本详情:', id)

    // 查询剧本详情
    const { getScriptById } = await import('@/lib/db/queries/script')
    const script = await getScriptById(env, id)

    if (!script) {
      return c.json(
        {
          success: false,
          message: '剧本不存在'
        },
        404
      )
    }

    // 格式化数据
    const formattedScript = {
      id: script.id,
      title: script.title,
      description: script.description,
      coverImage: script.coverImage,
      duration: script.duration,
      tags: Array.isArray(script.tags) ? script.tags : [],
      category: script.category,
      content: script.content,
      audioUrl: script.audioUrl,
      totalDuration: script.totalDuration,
      stageCount: script.stageCount,
      isPublic: script.isPublic,
      isActive: script.isActive,
      isPremium: script.isPremium,
      pointsCost: script.pointsCost,
      usageCount: script.usageCount,
      rating: script.rating ? Number.parseFloat(script.rating as string) : 0,
      ratingCount: script.ratingCount,
      createdBy: script.createdBy,
      createdAt: script.createdAt,
      updatedAt: script.updatedAt
    }

    return c.json({
      success: true,
      data: formattedScript
    })
  } catch (error) {
    console.error('❌ [ADMIN-SCRIPTS] 获取剧本详情失败:', error)
    return c.json(
      {
        success: false,
        message: '获取剧本详情失败'
      },
      500
    )
  }
})

// ==================== 剧本创建和编辑 ====================

// 创建剧本验证模式
const createScriptSchema = z.object({
  title: z.string().min(1, '标题不能为空'),
  description: z.string().min(1, '描述不能为空'),
  coverImage: z.string().url('封面图片URL格式不正确'),
  duration: z.string().min(1, '时长不能为空'),
  tags: z.array(z.string()).default([]),
  category: z.string().min(1, '分类不能为空'),
  content: z.any().optional(),
  audioUrl: z.string().url().optional(),
  totalDuration: z.number().optional(),
  stageCount: z.number().optional(),
  isPublic: z.boolean().default(true),
  isActive: z.boolean().default(true),
  isPremium: z.boolean().default(false),
  pointsCost: z.number().default(0)
})

// 创建剧本
adminScripts.post('/', authMiddleware, zValidator('json', createScriptSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const scriptData = c.req.valid('json')
    const env = c.env

    console.log('➕ [ADMIN-SCRIPTS] 创建剧本:', scriptData.title)

    // 获取创建者用户ID
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return c.json({ success: false, message: '用户信息获取失败' }, 401)
    }

    // 查找对应的数据库用户
    const dbUser = await getUserBySupabaseId(env, supabaseUser.id)

    if (!dbUser) {
      return c.json({ success: false, message: '管理员用户不存在' }, 404)
    }

    const createdBy = dbUser.id

    // 创建剧本
    const { createScript } = await import('@/lib/db/queries/script')
    const newScriptResult = await createScript(env, {
      ...scriptData,
      createdBy
    })

    const newScript = newScriptResult[0]
    if (!newScript) {
      return c.json({ success: false, message: '创建剧本失败' }, 500)
    }

    console.log('✅ [ADMIN-SCRIPTS] 剧本创建成功:', newScript.id)

    return c.json({
      success: true,
      data: {
        id: newScript.id,
        title: newScript.title,
        description: newScript.description,
        coverImage: newScript.coverImage,
        duration: newScript.duration,
        tags: Array.isArray(newScript.tags) ? newScript.tags : [],
        category: newScript.category,
        audioUrl: newScript.audioUrl,
        totalDuration: newScript.totalDuration,
        stageCount: newScript.stageCount,
        isPublic: newScript.isPublic,
        isActive: newScript.isActive,
        isPremium: newScript.isPremium,
        pointsCost: newScript.pointsCost,
        usageCount: newScript.usageCount,
        rating: newScript.rating ? Number.parseFloat(newScript.rating as string) : 0,
        ratingCount: newScript.ratingCount,
        createdBy: newScript.createdBy,
        createdAt: newScript.createdAt,
        updatedAt: newScript.updatedAt
      },
      message: '剧本创建成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-SCRIPTS] 创建剧本失败:', error)
    return c.json(
      {
        success: false,
        message: '创建剧本失败'
      },
      500
    )
  }
})

// 更新剧本
adminScripts.put(
  '/:id',
  authMiddleware,
  zValidator('json', createScriptSchema.partial()),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const id = c.req.param('id')
      const updateData = c.req.valid('json')
      const env = c.env

      console.log('📝 [ADMIN-SCRIPTS] 更新剧本:', id)

      // 更新剧本
      const { updateScriptById } = await import('@/lib/db/queries/script')
      const updatedScriptResult = await updateScriptById(env, id, updateData)

      const updatedScript = updatedScriptResult[0]
      if (!updatedScript) {
        return c.json({ success: false, message: '剧本不存在' }, 404)
      }

      console.log('✅ [ADMIN-SCRIPTS] 剧本更新成功:', id)

      return c.json({
        success: true,
        data: {
          id: updatedScript.id,
          title: updatedScript.title,
          description: updatedScript.description,
          coverImage: updatedScript.coverImage,
          duration: updatedScript.duration,
          tags: Array.isArray(updatedScript.tags) ? updatedScript.tags : [],
          category: updatedScript.category,
          audioUrl: updatedScript.audioUrl,
          totalDuration: updatedScript.totalDuration,
          stageCount: updatedScript.stageCount,
          isPublic: updatedScript.isPublic,
          isActive: updatedScript.isActive,
          isPremium: updatedScript.isPremium,
          pointsCost: updatedScript.pointsCost,
          usageCount: updatedScript.usageCount,
          rating: updatedScript.rating ? Number.parseFloat(updatedScript.rating as string) : 0,
          ratingCount: updatedScript.ratingCount,
          createdBy: updatedScript.createdBy,
          createdAt: updatedScript.createdAt,
          updatedAt: updatedScript.updatedAt
        },
        message: '剧本更新成功'
      })
    } catch (error) {
      console.error('❌ [ADMIN-SCRIPTS] 更新剧本失败:', error)
      return c.json(
        {
          success: false,
          message: '更新剧本失败'
        },
        500
      )
    }
  }
)

// 删除剧本
adminScripts.delete('/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('🗑️ [ADMIN-SCRIPTS] 删除剧本:', id)

    // 删除剧本
    const { deleteScriptById } = await import('@/lib/db/queries/script')
    const deleted = await deleteScriptById(env, id)

    if (!deleted) {
      return c.json({ success: false, message: '剧本不存在' }, 404)
    }

    console.log('✅ [ADMIN-SCRIPTS] 剧本删除成功:', id)

    return c.json({
      success: true,
      message: '剧本删除成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-SCRIPTS] 删除剧本失败:', error)
    return c.json(
      {
        success: false,
        message: '删除剧本失败'
      },
      500
    )
  }
})

// ==================== 剧本状态管理 ====================

// 切换剧本启用状态
adminScripts.post(
  '/:id/toggle-status',
  authMiddleware,
  zValidator(
    'json',
    z.object({
      isActive: z.boolean()
    })
  ),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const id = c.req.param('id')
      const { isActive } = c.req.valid('json')
      const env = c.env

      console.log('🔄 [ADMIN-SCRIPTS] 切换剧本状态:', id, isActive)

      // 更新状态
      const { updateScriptById } = await import('@/lib/db/queries/script')
      const updatedScriptResult = await updateScriptById(env, id, { isActive })

      const updatedScript = updatedScriptResult[0]
      if (!updatedScript) {
        return c.json({ success: false, message: '剧本不存在' }, 404)
      }

      console.log('✅ [ADMIN-SCRIPTS] 剧本状态更新成功:', id)

      return c.json({
        success: true,
        message: isActive ? '剧本已启用' : '剧本已禁用'
      })
    } catch (error) {
      console.error('❌ [ADMIN-SCRIPTS] 切换剧本状态失败:', error)
      return c.json(
        {
          success: false,
          message: '状态更新失败'
        },
        500
      )
    }
  }
)

// 切换剧本公开状态
adminScripts.post(
  '/:id/toggle-public',
  authMiddleware,
  zValidator(
    'json',
    z.object({
      isPublic: z.boolean()
    })
  ),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const id = c.req.param('id')
      const { isPublic } = c.req.valid('json')
      const env = c.env

      console.log('🔄 [ADMIN-SCRIPTS] 切换剧本公开状态:', id, isPublic)

      // 更新公开状态
      const { updateScriptById } = await import('@/lib/db/queries/script')
      const updatedScriptResult = await updateScriptById(env, id, { isPublic })

      const updatedScript = updatedScriptResult[0]
      if (!updatedScript) {
        return c.json({ success: false, message: '剧本不存在' }, 404)
      }

      console.log('✅ [ADMIN-SCRIPTS] 剧本公开状态更新成功:', id)

      return c.json({
        success: true,
        message: isPublic ? '剧本已设为公开' : '剧本已设为私有'
      })
    } catch (error) {
      console.error('❌ [ADMIN-SCRIPTS] 切换剧本公开状态失败:', error)
      return c.json(
        {
          success: false,
          message: '公开状态更新失败'
        },
        500
      )
    }
  }
)

// ==================== 剧本内容管理 ====================
adminScripts.get('/:id/content', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('🔍 [ADMIN-SCRIPTS] 获取剧本内容:', id)

    // 查询剧本内容
    const { getScriptById } = await import('@/lib/db/queries/script')
    const script = await getScriptById(env, id)

    if (!script) {
      return c.json(
        {
          success: false,
          message: '剧本不存在'
        },
        404
      )
    }

    return c.json({
      success: true,
      data: script.content || { commands: [], stages: [] }
    })
  } catch (error) {
    console.error('❌ [ADMIN-SCRIPTS] 获取剧本内容失败:', error)
    return c.json(
      {
        success: false,
        message: '获取剧本内容失败'
      },
      500
    )
  }
})

// 更新剧本内容
const updateContentSchema = z.object({
  commands: z
    .array(
      z.object({
        command: z.string(),
        time: z.string()
      })
    )
    .default([]),
  stages: z
    .array(
      z.object({
        stage: z.number(),
        stageTitle: z.string(),
        pics: z
          .array(
            z.object({
              name: z.string(),
              pic: z.string()
            })
          )
          .default([]),
        intensity: z
          .record(
            z.object({
              thrust: z.number(),
              suction: z.number(),
              vibrate: z.number()
            })
          )
          .default({})
      })
    )
    .default([])
})

adminScripts.put(
  '/:id/content',
  authMiddleware,
  zValidator('json', updateContentSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const id = c.req.param('id')
      const content = c.req.valid('json')
      const env = c.env

      console.log('📝 [ADMIN-SCRIPTS] 更新剧本内容:', id)

      // 更新剧本内容
      const { updateScriptById } = await import('@/lib/db/queries/script')
      const updatedScriptResult = await updateScriptById(env, id, { content })

      const updatedScript = updatedScriptResult[0]
      if (!updatedScript) {
        return c.json({ success: false, message: '剧本不存在' }, 404)
      }

      console.log('✅ [ADMIN-SCRIPTS] 剧本内容更新成功:', id)

      return c.json({
        success: true,
        message: '剧本内容更新成功'
      })
    } catch (error) {
      console.error('❌ [ADMIN-SCRIPTS] 更新剧本内容失败:', error)
      return c.json(
        {
          success: false,
          message: '更新剧本内容失败'
        },
        500
      )
    }
  }
)

export default adminScripts
