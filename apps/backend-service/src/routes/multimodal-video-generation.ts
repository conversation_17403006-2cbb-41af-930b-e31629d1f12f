import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import type { Env } from '@/types/env'
import type { VideoGenerationTask } from '@/types/video'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import { permissionMiddleware, PermissionType } from '@/middleware/permission'
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository';
import { getCachedDbUserId } from '@/lib/cache/cache-utils'
import { generateUUID } from '@/lib/utils'
import { createMediaGeneration } from '@/lib/db/queries/media-generation'
import { createServicePointsManager } from '@/lib/membership/service-points'
import { getMessageById } from '@/modules/app/chat/repositories/message.repository'

const app = new Hono<{ Bindings: Env }>()

// 多模态视频生成请求验证 - 使用通用错误消息，在路由中处理国际化
const multimodalVideoGenerationSchema = z.object({
  prompt: z.string().min(1).max(2000),
  characterAvatar: z.string().url().optional(),
  chatId: z.string().uuid().optional(),
  messageId: z.string().uuid().optional(),
  characterId: z.string().optional(), // 添加 characterId 参数
  metadata: z
    .object({
      width: z.number().int().min(256).max(1920).optional(),
      height: z.number().int().min(256).max(1440).optional(), // 提高到1440支持竖屏视频
      duration: z.number().int().min(1).max(10).optional(), // 视频时长(秒)
      fps: z.number().int().min(15).max(60).optional() // 帧率
    })
    .optional()
})

/**
 * POST /api/multimodal-video-generation
 * 多模态视频生成API - 先生图，再生视频
 */
app.post(
  '/',
  authMiddleware,
  languageMiddleware,
  permissionMiddleware(PermissionType.VIDEO_GENERATION),
  zValidator('json', multimodalVideoGenerationSchema),
  async c => {
    try {
      const { prompt, characterAvatar, chatId, messageId, characterId, metadata } =
        c.req.valid('json')
      const user = c.get('user')
      const t = c.get('t')

      if (!user?.id) {
        return c.json({ error: t('video-generation.user_not_logged_in') }, 401)
      }

      // 获取数据库用户ID（带缓存）
      const dbUserId = await getCachedDbUserId(c.env, user.id)
      if (!dbUserId) {
        return c.json({ error: t('video-generation.user_not_exist') }, 404)
      }

      console.log('🎬 接收多模态视频生成请求:', {
        messageId,
        chatId,
        promptLength: prompt.length,
        hasCharacterAvatar: !!characterAvatar,
        userId: dbUserId
      })

      // 积分管理
      const pointsManager = createServicePointsManager(c.env)
      const generationId = `multimodal-video-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 11)}`

      // 多模态视频生成消费更多积分（图片10 + 视频30 = 40积分）
      const pointsResult = await pointsManager.consumeVideoGenerationPoints(dbUserId, {
        duration: metadata?.duration || 3,
        quality: 'standard',
        generationId,
        customDescription: `多模态视频生成 - 提示词: ${prompt.substring(0, 50)}...`
      })

      if (!pointsResult.success) {
        return c.json(
          {
            error: t('video-generation.insufficient_points'),
            data: {
              required: pointsResult.pointsConsumed || 40,
              available: pointsResult.remainingPoints || 0
            }
          },
          400
        )
      }

      // 获取实际消费的积分数量
      const totalPoints = pointsResult.pointsConsumed || 40

      // 生成任务ID
      const taskId = generateUUID()
      const finalMessageId = messageId || generateUUID()

      // 创建媒体生成记录
      const mediaGenerationRecord = await createMediaGeneration(c.env, {
        userId: dbUserId,
        characterId, // 直接使用前端传递的 characterId
        chatId,
        messageId: finalMessageId,
        mediaType: 'video',
        generationType: 'multimodal_chat', // 多模态聊天生成类型
        prompt,
        inputImageUrl: characterAvatar,
        pointsUsed: totalPoints,
        metadata: {
          taskId,
          insa3dEndpoint: c.env.INSA3D_API_ENDPOINT,
          stage: 'image_generation', // 初始阶段
          ...metadata
        }
      })

      // 创建多模态视频生成任务
      const queueTask: VideoGenerationTask = {
        taskId,
        userId: dbUserId,
        messageId: finalMessageId,
        prompt,
        characterAvatar,
        taskType: 'multimodal_video', // 标识为多模态视频任务
        metadata: {
          ...metadata,
          mediaGenerationId: mediaGenerationRecord.id,
          pointsUsed: totalPoints
        }
      }

      // 发送到视频生成队列（复用现有队列）
      await c.env.VIDEO_GENERATION_QUEUE.send(queueTask)

      console.log('🎬 多模态视频生成任务已入队:', {
        taskId,
        messageId: finalMessageId,
        pointsUsed: totalPoints,
        mediaGenerationId: mediaGenerationRecord.id
      })

      return c.json(
        {
          success: true,
          data: {
            taskId,
            messageId: finalMessageId,
            status: 'queued' as const,
            pointsUsed: totalPoints,
            remainingPoints: pointsResult.remainingPoints,
            estimatedTime: 120 // 多模态视频预计需要2分钟（图片30秒 + 视频90秒）
          },
          message: t('video-generation.task_started')
        },
        202
      )
    } catch (error) {
      const t = c.get('t')
      console.error('多模态视频生成失败:', error)
      return c.json(
        {
          error: t('video-generation.generation_failed'),
          message: error instanceof Error ? error.message : t('video-generation.unknown_error')
        },
        500
      )
    }
  }
)

/**
 * 查询多模态视频生成状态
 */
app.get('/status/:messageId', languageMiddleware, async c => {
  try {
    const messageId = c.req.param('messageId')
    const t = c.get('t')

    if (!messageId) {
      return c.json({ success: false, error: t('video-generation.missing_message_id') }, 400)
    }

    console.log('🔍 [STATUS] 查询多模态视频生成状态:', messageId)

    // 查询消息及其附件
    const message = await getMessageById(c.env, messageId)

    if (!message) {
      return c.json({ success: false, error: t('video-generation.message_not_exist') }, 404)
    }

    const attachments = (message.attachments as any[]) || []

    // 检查是否有完成的视频
    const completedVideoAttachment = attachments.find(
      (att: any) => att.contentType === 'video/mp4' || att.contentType === 'video/webm'
    )

    if (completedVideoAttachment) {
      console.log('✅ [STATUS] 发现完成的视频:', completedVideoAttachment.url)
      return c.json({
        success: true,
        data: {
          status: 'completed',
          progress: 100,
          stage: 'video_generation',
          message: t('video-generation.video_completed'),
          finalVideoUrl: completedVideoAttachment.url
        }
      })
    }

    // 检查生成状态附件（获取最新的）
    const generatingAttachments = attachments.filter(
      (att: any) => att.contentType?.includes('generating') && att.contentType?.includes('video')
    )

    if (generatingAttachments.length > 0) {
      // 获取最新的生成状态
      const latestGenerating = generatingAttachments.sort(
        (a: any, b: any) =>
          new Date(b.metadata?.timestamp || 0).getTime() -
          new Date(a.metadata?.timestamp || 0).getTime()
      )[0]

      const metadata = latestGenerating.metadata || {}

      console.log('🔄 [STATUS] 发现生成中状态:', {
        progress: metadata.progress,
        stage: metadata.stage,
        status: metadata.status
      })

      return c.json({
        success: true,
        data: {
          status: metadata.status || 'processing',
          progress: metadata.progress || 0,
          stage: metadata.stage || 'image_generation',
          message: latestGenerating.name || t('video-generation.generating_video'),
          intermediateImageUrl: metadata.intermediateImageUrl
        }
      })
    }

    // 没有找到任何相关状态
    console.log('❓ [STATUS] 未找到生成状态')
    return c.json({
      success: true,
      data: {
        status: 'idle',
        progress: 0,
        stage: 'image_generation',
        message: t('video-generation.waiting_to_start')
      }
    })
  } catch (error) {
    const t = c.get('t')
    console.error('❌ [STATUS] 查询状态失败:', error)
    return c.json(
      {
        success: false,
        error: t('video-generation.query_status_failed')
      },
      500
    )
  }
})

export default app
