import { Hono } from 'hono';
import { authMiddleware } from '@/middleware/auth';
import type { Env } from '@/types/env';
import {
  createMembershipActivationCode,
  createPointsActivationCode,
  createActivationCodesBatch,
  useActivationCode,
  getActivationCodes,
  getUserActivationHistory,
  getActivationCodeStats,
  disableActivationCode,
  enableActivationCode,
  validateActivationCode,
} from '@/lib/db/queries/activation-code';
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository';

const app = new Hono<{ Bindings: Env; Variables: { user: any } }>();

// 辅助函数：从Supabase用户上下文解析本地用户ID
async function resolveLocalUserId(c: any): Promise<string> {
  const supabaseUser = c.get('user');
  if (!supabaseUser) {
    throw new Error('用户未认证');
  }

  // 获取数据库用户信息
  const dbUser = await getUserBySupabaseId(c.env, supabaseUser.id);
  if (!dbUser) {
    throw new Error('用户数据不存在');
  }

  return dbUser.id;
}

// 检查管理员权限
async function checkAdminPermission(c: any): Promise<boolean> {
  try {
    const supabaseUser = c.get('user');
    if (!supabaseUser) {
      return false;
    }

    // 检查用户的 user_metadata 中是否有管理员标识
    // Supabase 可能使用 user_metadata 或 raw_user_meta_data
    const userMetadata = supabaseUser.user_metadata || supabaseUser.raw_user_meta_data || {};
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true;

    if (isAdmin) {
      return true;
    }

    // 备用检查：检查特定的管理员邮箱（可选）
    const adminEmails = [
      '<EMAIL>',
      // 在这里添加其他管理员邮箱
    ];

    if (adminEmails.includes(supabaseUser.email)) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('检查管理员权限失败:', error);
    return false;
  }
}

// ==================== 用户端 API ====================

/**
 * 验证激活码
 * GET /activation-codes/validate/:code
 */
app.get('/validate/:code', authMiddleware, async (c) => {
  try {
    const code = c.req.param('code');
    const validation = await validateActivationCode(c.env, code);

    if (!validation.valid) {
      return c.json(
        {
          success: false,
          message: validation.reason,
        },
        400
      );
    }

    const activationCode = validation.activationCode! as any;

    // 返回激活码信息（不包含敏感信息）
    return c.json({
      success: true,
      data: {
        type: activationCode.type,
        description: activationCode.description,
        membershipPlan: activationCode.membership_plan
          ? {
              name: activationCode.membership_plan.name,
              durationDays: activationCode.membership_plan.duration_days,
              pointsIncluded: activationCode.membership_plan.points_included,
            }
          : null,
        pointsPackage: activationCode.points_package
          ? {
              name: activationCode.points_package.name,
              points: activationCode.points_package.points,
              bonusPoints: activationCode.points_package.bonus_points,
            }
          : null,
      },
    });
  } catch (error) {
    console.error('验证激活码失败:', error);
    return c.json(
      {
        success: false,
        message: '验证失败，请稍后重试',
      },
      500
    );
  }
});

/**
 * 使用激活码
 * POST /activation-codes/use
 */
app.post('/use', authMiddleware, async (c) => {
  try {
    const { code } = await c.req.json();
    const userId = await resolveLocalUserId(c);
    const ipAddress =
      c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown';
    const userAgent = c.req.header('User-Agent') || 'unknown';

    if (!code) {
      return c.json(
        {
          success: false,
          message: '请输入激活码',
        },
        400
      );
    }

    const result = await useActivationCode(c.env, {
      code: code.trim().toUpperCase(),
      userId,
      ipAddress,
      userAgent,
    });

    return c.json(result, result.success ? 200 : 400);
  } catch (error) {
    console.error('使用激活码失败:', error);
    return c.json(
      {
        success: false,
        message: '激活失败，请稍后重试',
      },
      500
    );
  }
});

/**
 * 获取用户激活历史
 * GET /activation-codes/history
 */
app.get('/history', authMiddleware, async (c) => {
  try {
    const userId = await resolveLocalUserId(c);
    const limit = Number.parseInt(c.req.query('limit') || '20');
    const offset = Number.parseInt(c.req.query('offset') || '0');

    const history = await getUserActivationHistory(c.env, userId, limit, offset);

    return c.json({
      success: true,
      data: history,
    });
  } catch (error) {
    console.error('获取激活历史失败:', error);
    return c.json(
      {
        success: false,
        message: '获取历史记录失败',
      },
      500
    );
  }
});

// ==================== 管理员端 API ====================

/**
 * 创建会员激活码
 * POST /activation-codes/admin/membership
 */
app.post('/admin/membership', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const { membershipPlanId, description, expiresAt, count = 1 } = await c.req.json();
    const adminUserId = await resolveLocalUserId(c);

    if (!membershipPlanId) {
      return c.json(
        {
          success: false,
          message: '请选择会员套餐',
        },
        400
      );
    }

    let codes;
    if (count === 1) {
      // 创建单个激活码
      codes = [
        await createMembershipActivationCode(c.env, {
          membershipPlanId,
          description,
          expiresAt: expiresAt ? new Date(expiresAt) : undefined,
          createdBy: adminUserId,
        }),
      ];
    } else {
      // 批量创建激活码
      codes = await createActivationCodesBatch(c.env, {
        type: 'membership',
        membershipPlanId,
        count,
        description,
        expiresAt: expiresAt ? new Date(expiresAt) : undefined,
        createdBy: adminUserId,
      });
    }

    return c.json({
      success: true,
      data: codes,
    });
  } catch (error) {
    console.error('创建会员激活码失败:', error);
    return c.json(
      {
        success: false,
        message: '创建失败，请稍后重试',
      },
      500
    );
  }
});

/**
 * 创建积分包激活码
 * POST /activation-codes/admin/points
 */
app.post('/admin/points', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const { pointsPackageId, description, expiresAt, count = 1 } = await c.req.json();
    const adminUserId = await resolveLocalUserId(c);

    if (!pointsPackageId) {
      return c.json(
        {
          success: false,
          message: '请选择积分包',
        },
        400
      );
    }

    let codes;
    if (count === 1) {
      // 创建单个激活码
      codes = [
        await createPointsActivationCode(c.env, {
          pointsPackageId,
          description,
          expiresAt: expiresAt ? new Date(expiresAt) : undefined,
          createdBy: adminUserId,
        }),
      ];
    } else {
      // 批量创建激活码
      codes = await createActivationCodesBatch(c.env, {
        type: 'points',
        pointsPackageId,
        count,
        description,
        expiresAt: expiresAt ? new Date(expiresAt) : undefined,
        createdBy: adminUserId,
      });
    }

    return c.json({
      success: true,
      data: codes,
    });
  } catch (error) {
    console.error('创建积分包激活码失败:', error);
    return c.json(
      {
        success: false,
        message: '创建失败，请稍后重试',
      },
      500
    );
  }
});

/**
 * 获取激活码列表
 * GET /activation-codes/admin/list
 */
app.get('/admin/list', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const type = c.req.query('type') as 'membership' | 'points' | undefined;
    const isUsed =
      c.req.query('isUsed') === 'true'
        ? true
        : c.req.query('isUsed') === 'false'
          ? false
          : undefined;
    const isActive =
      c.req.query('isActive') === 'true'
        ? true
        : c.req.query('isActive') === 'false'
          ? false
          : undefined;
    const batchId = c.req.query('batchId');
    const limit = Number.parseInt(c.req.query('limit') || '50');
    const offset = Number.parseInt(c.req.query('offset') || '0');

    const codes = await getActivationCodes(c.env, {
      type,
      isUsed,
      isActive,
      batchId,
      limit,
      offset,
    });

    return c.json({
      success: true,
      data: codes,
    });
  } catch (error) {
    console.error('获取激活码列表失败:', error);
    return c.json(
      {
        success: false,
        message: '获取列表失败',
      },
      500
    );
  }
});

/**
 * 获取激活码统计
 * GET /activation-codes/admin/stats
 */
app.get('/admin/stats', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const type = c.req.query('type') as 'membership' | 'points' | undefined;
    const batchId = c.req.query('batchId');
    const dateFrom = c.req.query('dateFrom') ? new Date(c.req.query('dateFrom')!) : undefined;
    const dateTo = c.req.query('dateTo') ? new Date(c.req.query('dateTo')!) : undefined;

    const stats = await getActivationCodeStats(c.env, {
      type,
      batchId,
      dateFrom,
      dateTo,
    });

    return c.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('获取激活码统计失败:', error);
    return c.json(
      {
        success: false,
        message: '获取统计失败',
      },
      500
    );
  }
});

/**
 * 禁用激活码
 * POST /activation-codes/admin/:id/disable
 */
app.post('/admin/:id/disable', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const codeId = c.req.param('id');
    const adminUserId = await resolveLocalUserId(c);

    await disableActivationCode(c.env, codeId, adminUserId);

    return c.json({
      success: true,
      message: '激活码已禁用',
    });
  } catch (error) {
    console.error('禁用激活码失败:', error);
    return c.json(
      {
        success: false,
        message: '禁用失败',
      },
      500
    );
  }
});

/**
 * 启用激活码
 * POST /activation-codes/admin/:id/enable
 */
app.post('/admin/:id/enable', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const codeId = c.req.param('id');
    const adminUserId = await resolveLocalUserId(c);

    await enableActivationCode(c.env, codeId, adminUserId);

    return c.json({
      success: true,
      message: '激活码已启用',
    });
  } catch (error) {
    console.error('启用激活码失败:', error);
    return c.json(
      {
        success: false,
        message: '启用失败',
      },
      500
    );
  }
});

export default app;
