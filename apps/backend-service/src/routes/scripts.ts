import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import type { Env } from '@/types/env'
import type { ScriptContent } from '@/types/script'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import {
  globalErrorHandler,
  requestIdMiddleware,
  throwBusinessError
} from '@/middleware/global-error-handler'
import { ErrorCode, BusinessError } from '@/types/errors'
import { createSuccessResponse, createErrorResponse } from '@/types/responses'
import { getErrorMessage } from '@/i18n/messages/errors'
import type { SupportedLanguage } from '@/i18n/config'
import type { Context } from 'hono'
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository';
import {
  getPublicScripts,
  getScriptById,
  createScript,
  updateScriptById,
  deleteScriptById,
  incrementScriptUsage,
  getScriptCategories,
  createScriptUsage,
  getUserScriptUsageHistory,
  updateScriptRating
} from '@/lib/db/queries/script'
import {
  checkScriptPurchase,
  createScriptPurchase,
  getUserPurchasedScriptIds,
  markScriptDownloaded,
  purchaseScriptWithPoints
} from '@/lib/db/queries/script-purchase'

// 获取国际化函数的辅助函数
function getI18n(
  c: Context<{
    Bindings: Env
    Variables: {
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>
): (key: string, params?: Record<string, string | number>) => string {
  return c.get('t')
}

// 创建路由实例
const app = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
  }
}>()

// 应用全局中间件
app.use('*', requestIdMiddleware())
app.use('*', globalErrorHandler())

// 辅助函数：从Supabase用户上下文解析本地用户ID
async function resolveLocalUserId(c: any): Promise<string> {
  const supabaseUser = c.get('user')
  if (!supabaseUser) {
    throwBusinessError(ErrorCode.AUTH_UNAUTHORIZED)
  }

  // 获取数据库用户信息
  const dbUser = await getUserBySupabaseId(c.env, supabaseUser.id)
  if (!dbUser) {
    throwBusinessError(ErrorCode.USER_NOT_FOUND)
  }

  return dbUser.id
}

// ==================== 验证模式 ====================

// 获取剧本列表的查询参数验证
const getScriptsQuerySchema = z.object({
  limit: z.coerce.number().optional().default(20),
  offset: z.coerce.number().optional().default(0),
  category: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['latest', 'popular', 'rating']).optional().default('latest')
})

// 创建剧本的请求体验证 - 静态版本
const createScriptSchema = z.object({
  title: z.string().min(1).max(100),
  description: z.string().min(1),
  coverImage: z.string().url(),
  duration: z.string().min(1),
  tags: z.array(z.string()).optional().default([]),
  category: z.string().optional(),
  content: z.any().optional(),
  isPublic: z.boolean().optional().default(true),
  isPremium: z.boolean().optional().default(false)
})

// 更新剧本的请求体验证
const updateScriptSchema = z.object({
  title: z.string().min(1).max(100).optional(),
  description: z.string().min(1).optional(),
  coverImage: z.string().url().optional(),
  duration: z.string().min(1).optional(),
  tags: z.array(z.string()).optional(),
  category: z.string().optional(),
  content: z.any().optional(),
  isPublic: z.boolean().optional(),
  isPremium: z.boolean().optional()
})

// 评分的请求体验证
const ratingSchema = z.object({
  rating: z.number().min(1).max(5)
})

// ==================== 路由处理函数 ====================

/**
 * 获取公开剧本列表
 * GET /api/scripts
 */
app.get('/', zValidator('query', getScriptsQuerySchema), languageMiddleware, async c => {
  try {
    const query = c.req.valid('query')

    const scripts = await getPublicScripts(c.env, {
      limit: query.limit,
      offset: query.offset,
      category: query.category,
      search: query.search,
      sortBy: query.sortBy
    })

    // 移除敏感内容，列表页面不返回content和audioUrl
    const filteredScripts = scripts.map(script => ({
      id: script.id,
      title: script.title,
      description: script.description,
      coverImage: script.coverImage,
      duration: script.duration,
      tags: script.tags,
      category: script.category,
      pointsCost: script.pointsCost,
      isPublic: script.isPublic,
      isPremium: script.isPremium,
      usageCount: script.usageCount,
      rating: script.rating,
      stageCount: script.stageCount,
      totalDuration: script.totalDuration,
      createdAt: script.createdAt,
      updatedAt: script.updatedAt
      // 不返回 content 和 audioUrl
    }))

    const response = createSuccessResponse(filteredScripts)
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    console.error('获取剧本列表失败:', error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR)
  }
})

/**
 * 获取剧本分类
 * GET /api/scripts/categories
 */
app.get('/categories', languageMiddleware, async c => {
  try {
    const categories = await getScriptCategories(c.env)
    const response = createSuccessResponse(categories)
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    console.error('获取剧本分类失败:', error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR)
  }
})

/**
 * 获取用户已购买的剧本ID列表
 * GET /api/scripts/purchased
 */
app.get('/purchased', authMiddleware, languageMiddleware, async c => {
  try {
    const userId = await resolveLocalUserId(c)
    const scriptIds = await getUserPurchasedScriptIds(c.env, userId)

    const response = createSuccessResponse({ scriptIds })
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    console.error('获取已购买剧本失败:', error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR)
  }
})

/**
 * 创建剧本使用记录
 * POST /api/scripts/usage
 */
app.post('/usage', authMiddleware, languageMiddleware, async c => {
  try {
    const data = await c.req.json()
    const userId = await resolveLocalUserId(c)

    // 验证剧本ID
    if (!data.scriptId || data.scriptId.trim().length === 0) {
      throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'scriptId' })
    }

    const [usage] = await createScriptUsage(c.env, {
      ...data,
      userId
    })

    // 如果有评分，更新剧本的平均评分
    if (data.rating) {
      await updateScriptRating(c.env, data.scriptId)
    }

    const t = getI18n(c)
    const response = createSuccessResponse(usage, t('script_usage_created'))
    return c.json(response, 201)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    console.error('创建剧本使用记录失败:', error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR)
  }
})

/**
 * 获取用户的剧本使用历史
 * GET /api/scripts/usage/history
 */
app.get('/usage/history', authMiddleware, languageMiddleware, async c => {
  try {
    const userId = await resolveLocalUserId(c)

    const limit = Number.parseInt(c.req.query('limit') || '20', 10)
    const offset = Number.parseInt(c.req.query('offset') || '0', 10)

    const history = await getUserScriptUsageHistory(c.env, userId, {
      limit,
      offset
    })

    const response = createSuccessResponse({
      data: history,
      pagination: {
        limit,
        offset,
        total: history.length // 实际项目中应该返回总数
      }
    })
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    console.error('获取剧本使用历史失败:', error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR)
  }
})

/**
 * 创建新剧本
 * POST /api/scripts
 */
app.post(
  '/',
  authMiddleware,
  zValidator('json', createScriptSchema),
  languageMiddleware,
  async c => {
    try {
      const scriptData = c.req.valid('json')
      const userId = await resolveLocalUserId(c)

      const [script] = await createScript(c.env, {
        ...scriptData,
        createdBy: userId
      })

      const t = getI18n(c)
      const response = createSuccessResponse(script, t('script_created'))
      return c.json(response, 201)
    } catch (error) {
      if (error instanceof BusinessError) {
        throw error
      }
      console.error('创建剧本失败:', error)
      throwBusinessError(ErrorCode.SYSTEM_ERROR)
    }
  }
)

/**
 * 根据ID获取剧本详情
 * GET /api/scripts/:id
 */
app.get('/:id', languageMiddleware, async c => {
  try {
    const id = c.req.param('id')

    if (!id) {
      throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
    }

    const script = await getScriptById(c.env, id)

    if (!script) {
      throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId: id })
    }

    const response = createSuccessResponse(script)
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    console.error('获取剧本详情失败:', error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR)
  }
})

/**
 * 获取剧本详细内容（包含阶段、对话等）
 * GET /api/scripts/:id/content
 */
app.get('/:id/content', languageMiddleware, async c => {
  try {
    const id = c.req.param('id')

    if (!id) {
      throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
    }

    const script = await getScriptById(c.env, id)

    if (!script) {
      throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId: id })
    }

    // 返回剧本的详细内容
    const response = createSuccessResponse({
      id: script.id,
      title: script.title,
      content: script.content,
      audioUrl: script.audioUrl,
      totalDuration: script.totalDuration,
      stageCount: script.stageCount
    })
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    console.error('获取剧本内容失败:', error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR)
  }
})

/**
 * 获取剧本阶段列表
 * GET /api/scripts/:id/stages
 */
app.get('/:id/stages', languageMiddleware, async c => {
  try {
    const id = c.req.param('id')

    if (!id) {
      throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
    }

    const script = await getScriptById(c.env, id)

    if (!script) {
      throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId: id })
    }

    const content = script.content as ScriptContent
    if (!content || !content.stages) {
      throwBusinessError(ErrorCode.SCRIPT_INVALID_STAGE, { scriptId: id })
    }

    // 返回阶段列表（不包含详细对话，减少数据传输）
    const stages = content.stages.map(stage => ({
      stage: stage.stage,
      stageTitle: stage.stageTitle,
      duration: stage.duration,
      pics: stage.pics,
      dialogueCount: stage.dialogues?.length || 0
    }))

    const response = createSuccessResponse(stages)
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    console.error('获取剧本阶段失败:', error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR)
  }
})

/**
 * 更新剧本信息
 * PUT /api/scripts/:id
 */
app.put(
  '/:id',
  authMiddleware,
  zValidator('json', updateScriptSchema),
  languageMiddleware,
  async c => {
    try {
      const id = c.req.param('id')
      const updateData = c.req.valid('json')

      if (!id) {
        throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
      }

      const userId = await resolveLocalUserId(c)

      // 检查剧本是否存在
      const existingScript = await getScriptById(c.env, id)
      if (!existingScript) {
        throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId: id })
      }

      // 检查权限（只有创建者可以修改）
      if (existingScript.createdBy !== userId) {
        throwBusinessError(ErrorCode.SCRIPT_ACCESS_DENIED, { scriptId: id })
      }

      const [updatedScript] = await updateScriptById(c.env, id, updateData)

      const t = getI18n(c)
      const response = createSuccessResponse(updatedScript, t('script_updated'))
      return c.json(response)
    } catch (error) {
      if (error instanceof BusinessError) {
        throw error
      }
      console.error('更新剧本失败:', error)
      throwBusinessError(ErrorCode.SYSTEM_ERROR)
    }
  }
)

/**
 * 删除剧本
 * DELETE /api/scripts/:id
 */
app.delete('/:id', authMiddleware, languageMiddleware, async c => {
  try {
    const id = c.req.param('id')

    if (!id) {
      throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
    }

    const userId = await resolveLocalUserId(c)

    // 检查剧本是否存在
    const existingScript = await getScriptById(c.env, id)
    if (!existingScript) {
      throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId: id })
    }

    // 检查权限（只有创建者可以删除）
    if (existingScript.createdBy !== userId) {
      throwBusinessError(ErrorCode.SCRIPT_ACCESS_DENIED, { scriptId: id })
    }

    await deleteScriptById(c.env, id)

    const t = getI18n(c)
    const response = createSuccessResponse(null, t('script_deleted'))
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    console.error('删除剧本失败:', error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR)
  }
})

/**
 * 使用剧本（增加使用次数）
 * POST /api/scripts/:id/use
 */
app.post('/:id/use', authMiddleware, languageMiddleware, async c => {
  try {
    const scriptId = c.req.param('id')

    if (!scriptId) {
      throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
    }

    const userId = await resolveLocalUserId(c)

    // 检查剧本是否存在
    const script = await getScriptById(c.env, scriptId)
    if (!script) {
      throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId })
    }

    // 增加使用次数
    await incrementScriptUsage(c.env, scriptId)

    // 记录用户使用历史
    await createScriptUsage(c.env, {
      userId,
      scriptId
    })

    const t = getI18n(c)
    const response = createSuccessResponse(null, t('script_usage_updated'))
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    console.error('记录剧本使用失败:', error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR)
  }
})

/**
 * 为剧本评分
 * POST /api/scripts/:id/rating
 */
app.post(
  '/:id/rating',
  authMiddleware,
  zValidator('json', ratingSchema),
  languageMiddleware,
  async c => {
    try {
      const scriptId = c.req.param('id')
      const { rating } = c.req.valid('json')

      if (!scriptId) {
        throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'id' })
      }

      const userId = await resolveLocalUserId(c)

      // 检查剧本是否存在
      const script = await getScriptById(c.env, scriptId)
      if (!script) {
        throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId })
      }

      await updateScriptRating(c.env, scriptId)

      const t = getI18n(c)
      const response = createSuccessResponse(null, t('script_rating_success'))
      return c.json(response)
    } catch (error) {
      if (error instanceof BusinessError) {
        throw error
      }
      console.error('提交评分失败:', error)
      throwBusinessError(ErrorCode.SYSTEM_ERROR)
    }
  }
)

/**
 * 购买剧本
 * POST /api/scripts/:id/purchase
 */
app.post('/:id/purchase', authMiddleware, languageMiddleware, async c => {
  try {
    const scriptId = c.req.param('id')

    if (!scriptId) {
      throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'scriptId' })
    }

    const userId = await resolveLocalUserId(c)

    // 并行检查剧本存在性和购买状态
    const [script, existingPurchase] = await Promise.all([
      getScriptById(c.env, scriptId),
      checkScriptPurchase(c.env, userId, scriptId)
    ])

    if (!script) {
      throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId })
    }

    if (existingPurchase) {
      throwBusinessError(ErrorCode.SCRIPT_ALREADY_PURCHASED, { scriptId })
    }

    // 免费剧本直接创建购买记录（无需积分交易记录）
    if (script.pointsCost <= 0) {
      const [purchase] = await createScriptPurchase(c.env, {
        userId,
        scriptId,
        pointsCost: 0
        // 免费剧本不设置transactionId，避免外键约束问题
      })

      const t = getI18n(c)
      const response = createSuccessResponse(
        {
          purchase,
          script: {
            id: script.id,
            title: script.title,
            description: script.description,
            coverImage: script.coverImage
          }
        },
        t('script_free_success')
      )
      return c.json(response)
    }

    // 使用优化的购买函数（包含事务处理）
    const purchaseResult = await purchaseScriptWithPoints(c.env, {
      userId,
      scriptId,
      pointsCost: script.pointsCost,
      scriptTitle: script.title
    })

    if (!purchaseResult.success) {
      // 根据错误代码返回标准化的错误响应
      if (purchaseResult.errorCode === 'INSUFFICIENT_POINTS' && purchaseResult.errorData) {
        // 返回与前端兼容的错误响应格式
        return c.json(
          {
            success: false,
            error: getErrorMessage(ErrorCode.INSUFFICIENT_POINTS, c.get('language'), {
              required: purchaseResult.errorData.required,
              available: purchaseResult.errorData.available
            }),
            code: ErrorCode.INSUFFICIENT_POINTS,
            data: {
              requiredPoints: purchaseResult.errorData.required,
              availablePoints: purchaseResult.errorData.available,
              remainingPoints: purchaseResult.remainingPoints
            }
          },
          402 as any // 使用402表示积分不足
        )
      } else if (purchaseResult.errorCode === 'USER_POINTS_NOT_FOUND') {
        throwBusinessError(ErrorCode.USER_NOT_FOUND)
      } else {
        // 其他错误使用通用格式
        throwBusinessError(ErrorCode.SCRIPT_PURCHASE_FAILED, {
          scriptId,
          reason: purchaseResult.error
        })
      }
    }

    const t = getI18n(c)
    const response = createSuccessResponse(
      {
        purchase: purchaseResult.purchase,
        script: {
          id: script.id,
          title: script.title,
          description: script.description,
          coverImage: script.coverImage
        },
        remainingPoints: purchaseResult.remainingPoints
      },
      t('script_purchase_success')
    )
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    console.error('购买剧本失败:', error)
    throwBusinessError(ErrorCode.SCRIPT_PURCHASE_FAILED, { scriptId: c.req.param('id') })
  }
})

/**
 * 检查剧本购买状态
 * GET /api/scripts/:id/purchase-status
 */
app.get('/:id/purchase-status', authMiddleware, languageMiddleware, async c => {
  try {
    const scriptId = c.req.param('id')

    if (!scriptId) {
      throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'scriptId' })
    }

    const userId = await resolveLocalUserId(c)

    const purchase = await checkScriptPurchase(c.env, userId, scriptId)

    const response = createSuccessResponse({
      isPurchased: !!purchase,
      purchase: purchase || null
    })
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    console.error('检查购买状态失败:', error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR)
  }
})

/**
 * 下载剧本内容
 * POST /api/scripts/:id/download
 */
app.post('/:id/download', authMiddleware, languageMiddleware, async c => {
  try {
    const scriptId = c.req.param('id')

    if (!scriptId) {
      throwBusinessError(ErrorCode.INVALID_PARAMETER, { field: 'scriptId' })
    }

    const userId = await resolveLocalUserId(c)

    // 检查是否已购买
    const purchase = await checkScriptPurchase(c.env, userId, scriptId)
    if (!purchase) {
      throwBusinessError(ErrorCode.SCRIPT_ACCESS_DENIED, {
        scriptId,
        reason: 'Script not purchased'
      })
    }

    // 获取剧本详细内容
    const script = await getScriptById(c.env, scriptId)
    if (!script) {
      throwBusinessError(ErrorCode.SCRIPT_NOT_FOUND, { scriptId })
    }

    // 标记为已下载
    await markScriptDownloaded(c.env, userId, scriptId)

    const t = getI18n(c)
    const response = createSuccessResponse(
      {
        id: script.id,
        title: script.title,
        content: script.content,
        audioUrl: script.audioUrl,
        totalDuration: script.totalDuration,
        stageCount: script.stageCount
      },
      t('script_download_success')
    )
    return c.json(response)
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error
    }
    console.error('下载剧本内容失败:', error)
    throwBusinessError(ErrorCode.SYSTEM_ERROR)
  }
})

export default app
