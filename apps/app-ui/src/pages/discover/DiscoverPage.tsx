import { useState, useEffect, useCallback } from 'react'
import { useNavigate, useSearchParams } from 'react-router'
import { Skeleton } from '@heroui/react'
import type { DisplayRole } from '@/lib/types'
import { motion } from 'framer-motion'
import { getDescription } from '@/components/character-creator-v2/mapping'
import type { CharacterType } from '@/lib/types'
import { useUserCharactersStore, useUserCharactersData } from '@/stores/user-characters-store'
import { useSystemCharactersStore, useSystemCharactersData } from '@/stores/system-characters-store'
import { cn } from '@/lib/utils'
import EmptyStateCard from './components/EmptyStateCard'
import { useTranslation } from 'react-i18next'
import Clip from '@/components/common/clip'

// 角色卡片组件（按设计稿样式）
function RoleCard({
  role,
  onSelect
}: {
  role: DisplayRole
  onSelect: (roleId: string, roleData: DisplayRole) => void
}) {
  const handleClick = () => {
    // 立即触发导航，不等待动画
    onSelect(role.role, role)
  }

  return (
    <motion.div className="w-full" layoutId={`role-container-${role.role}`}>
      <Clip
        radius={40}
        continuous={true}
        ripple={true}
        onClick={handleClick}
        className="relative w-full h-[260px] mx-auto group"
      >
        {/* 背景图片 - 添加 layoutId 用于共享元素动画 */}
        <motion.img
          src={role.avatar || '/images/roles/default.jpg'}
          alt={role.character}
          className="absolute inset-0 w-full h-full object-cover"
          layoutId={`role-image-${role.role}`}
        />

        {/* 整体透明-黑色渐变遮罩 */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-black/20 to-black/10" />

        {/* 底部渐变遮罩 - 用于文字可读性 */}
        <div className="absolute inset-x-0 bottom-0 h-24 bg-gradient-to-t from-black/60 via-black/30 to-transparent" />

        {/* 角色信息 */}
        <motion.div
          className="absolute bottom-0 left-0 right-0 p-4 pb-3"
          layoutId={`role-info-${role.role}`}
        >
          <div className="flex items-center gap-3 mb-1">
            <motion.div
              className="text-white text-base font-semibold tracking-tight"
              style={{ fontFamily: "'PingFang SC', sans-serif" }}
              layoutId={`role-name-${role.role}`}
            >
              {role.character}
            </motion.div>
            <motion.div
              className="text-[#cccccc] text-base font-medium"
              style={{ fontFamily: "'PingFang SC', sans-serif" }}
              layoutId={`role-age-${role.role}`}
            >
              {role.age}
            </motion.div>
          </div>
          {role.description && (
            <motion.div
              className="text-[#b3b3b3] text-xs leading-relaxed line-clamp-2"
              style={{ fontFamily: "'PingFang SC', sans-serif" }}
              layoutId={`role-description-${role.role}`}
            >
              {role.description}
            </motion.div>
          )}
        </motion.div>
      </Clip>
    </motion.div>
  )
}

// 网格布局组件
function GridLayout({
  roles,
  onSelectRole
}: {
  roles: DisplayRole[]
  onSelectRole: (roleId: string, roleData: DisplayRole) => void
}) {
  return (
    <div className="grid grid-cols-2 gap-2 px-2">
      {roles.map(role => (
        <RoleCard key={role.role} role={role} onSelect={onSelectRole} />
      ))}
    </div>
  )
}

export default function DiscoverPage() {
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  const { t } = useTranslation('discover')

  // 从 URL 参数获取当前标签，默认为 'my-girls'
  const [activeTab, setActiveTab] = useState(() => {
    return searchParams.get('tab') || 'my-girls'
  })

  // 使用Zustand store获取用户角色数据
  const { userCharacters, isLoading: userLoading } = useUserCharactersData()
  const { fetchUserCharacters } = useUserCharactersStore()

  // 使用Zustand store获取系统角色数据
  const { systemCharacters, isLoading: systemLoading } = useSystemCharactersData()
  const { fetchSystemCharacters } = useSystemCharactersStore()

  // 格式化角色数据
  const formatUserRoles = useCallback((characters: CharacterType[]): DisplayRole[] => {
    return characters.map(char => ({
      role: char.id,
      character: char.name,
      description: getDescription(char as any),
      avatar: char.imageUrl || '/images/roles/default.jpg',
      age: char.age || '24',
      custom: true
    }))
  }, [])

  // 格式化系统角色数据
  const formatSystemRoles = useCallback((characters: any[]): DisplayRole[] => {
    return characters.map(char => ({
      role: char.id,
      character: char.name,
      description: getDescription(char as any),
      avatar: char.imageUrl || char.image_url || '/images/roles/default.jpg',
      age: char.age?.toString() || '24',
      isNew: true,
      system: true
    }))
  }, [])

  // 格式化后的角色数据
  const customRoles = formatUserRoles(userCharacters as any[])
  const systemRoles = formatSystemRoles(systemCharacters as any[])

  // 初始化加载数据
  useEffect(() => {
    fetchUserCharacters()
    fetchSystemCharacters()
  }, [fetchUserCharacters, fetchSystemCharacters])

  // 更新标签状态并同步到 URL
  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
    setSearchParams({ tab })
  }

  // 选择角色开始对话
  const handleSelectRole = async (roleId: string, roleData: DisplayRole) => {
    try {
      // 在导航时保持当前标签状态
      navigate(`/role-detail/${roleId}?from=discover&tab=${activeTab}`, {
        state: { role: roleData }
      })
    } catch (error) {
      console.error('设置角色失败:', error)
      navigate(`/role-detail/${roleId}?from=discover&tab=${activeTab}`, {
        state: { role: roleData }
      })
    }
  }

  return (
    <div className="min-h-screen bg-[#121521] relative w-screen overflow-x-hidden">
      {/* 背景装饰 - 修正为设计稿样式 */}
      <div className="absolute left-[170px] -top-[230px]">
        <div
          style={{
            width: 361,
            height: 361,
            background: 'rgba(137, 47, 255, 0.40)',
            boxShadow: '72px 72px 72px',
            borderRadius: 9999,
            filter: 'blur(36px)'
          }}
        />
      </div>

      {/* 毛玻璃顶部导航 */}
      <div className="sticky top-0 z-40">
        <div
          className="backdrop-blur-lg border-b border-white/10 h-20 pt-4 relative flex items-center gap-8 justify-center"
          style={{
            backgroundColor: 'rgba(18, 21, 33, 0.6)',
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)'
          }}
        >
          {/* 我的女孩标签 */}
          <div className="relative h-full">
            <button
              onClick={() => handleTabChange('my-girls')}
              className={cn(
                'px-6 h-full text-xl font-semibold transition-colors',
                activeTab === 'my-girls' ? 'text-white' : 'text-[#999999]'
              )}
            >
              {t('tab_my_girls')}
            </button>
            <img
              src="/images/decorate/decorate-1.svg"
              alt=""
              className={`absolute -bottom-0 left-1/2 transform -translate-x-1/2 pointer-events-none ${
                activeTab === 'my-girls' ? 'opacity-100' : 'opacity-0'
              }`}
            />
          </div>

          <div className="relative h-full">
            {/* 热门标签 */}
            <button
              onClick={() => handleTabChange('hot')}
              className={cn(
                'px-6  h-full text-xl font-semibold transition-colors',
                activeTab === 'hot' ? 'text-white' : 'text-[#999999]'
              )}
            >
              {t('tab_hot')}
            </button>

            <img
              src="/images/decorate/decorate-1.svg"
              alt=""
              className={`absolute -bottom-0 left-1/2 transform -translate-x-1/2 pointer-events-none ${
                activeTab === 'hot' ? 'opacity-100' : 'opacity-0'
              }`}
            />
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="pb-20 pt-4">
        {activeTab === 'my-girls' ? (
          // 我的女孩内容
          <div>
            {userLoading ? (
              <div className="grid grid-cols-2 gap-2 px-2">
                {[1, 2, 3, 4].map(placeholder => (
                  <div key={placeholder} className="flex justify-center">
                    <Skeleton className="rounded-2xl">
                      <div className=" w-full h-[260px] rounded-2xl bg-gray-800"></div>
                    </Skeleton>
                  </div>
                ))}
              </div>
            ) : customRoles.length > 0 ? (
              <GridLayout roles={customRoles} onSelectRole={handleSelectRole} />
            ) : (
              <EmptyStateCard
                title={t('empty_create_title')}
                buttonText={t('empty_create_btn')}
                onButtonClick={() => navigate('/roles/custom')}
                type="create"
              />
            )}
          </div>
        ) : (
          // 热门内容（系统角色）
          <div>
            {systemLoading ? (
              <div className="grid grid-cols-2 gap-2 px-2">
                {[1, 2, 3, 4, 5, 6].map(placeholder => (
                  <div key={placeholder} className="flex justify-center">
                    <Skeleton className="rounded-2xl">
                      <div className=" w-full h-[260px] rounded-2xl bg-gray-800"></div>
                    </Skeleton>
                  </div>
                ))}
              </div>
            ) : systemRoles.length > 0 ? (
              <GridLayout roles={systemRoles} onSelectRole={handleSelectRole} />
            ) : (
              <EmptyStateCard
                title={t('empty_hot_title')}
                buttonText={t('empty_hot_btn')}
                onButtonClick={() => fetchSystemCharacters(true)}
                type="hot"
              />
            )}
          </div>
        )}
      </div>
    </div>
  )
}
