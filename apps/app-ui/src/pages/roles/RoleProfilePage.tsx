import { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate, useLocation } from 'react-router'
import { Button, Skeleton, Tabs, Tab } from '@heroui/react'
import { Icon } from '@iconify/react'
import { useDeviceSafeArea } from '@/hooks/use-mobile-viewport'
import { motion, AnimatePresence } from 'framer-motion'
import { useRoleStore } from '@/stores/role-store'
import { useUserCharactersStore } from '@/stores/user-characters-store'
import { useSystemCharactersStore } from '@/stores/system-characters-store'
import { getDescription } from '@/components/character-creator-v2/mapping'
import { generateUUID } from '@/lib/utils'
import { apiService } from '@/api'
import type { DisplayRole } from '@/lib/types'
import { RoleHeader, RoleActions, GalleryTab, ImagesTab, VideosTab } from './components'

export default function RoleProfilePage() {
  const { roleId } = useParams<{ roleId: string }>()
  const navigate = useNavigate()
  const location = useLocation()
  const safeArea = useDeviceSafeArea()
  const [role, setRole] = useState<DisplayRole | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('gallery')
  const [isChatLoading, setIsChatLoading] = useState(false)
  const [isInteractiveLoading, setIsInteractiveLoading] = useState(false)
  const [isNewChatLoading, setIsNewChatLoading] = useState(false)
  const [hasExistingChat, setHasExistingChat] = useState(false)

  // 使用全局角色状态管理
  const { setRole: setGlobalRole } = useRoleStore()

  // 使用系统角色 store
  const { getSystemCharacterById, fetchSystemCharacters } = useSystemCharactersStore()

  // 获取角色数据和聊天记录
  useEffect(() => {
    fetchRoleData()
    checkExistingChat()
  }, [roleId])

  const fetchRoleData = async () => {
    if (!roleId) return

    try {
      // 确保系统角色数据已加载
      await fetchSystemCharacters()

      // 先从系统角色中查找
      const systemRole = getSystemCharacterById(roleId)

      if (systemRole) {
        setRole({
          id: systemRole.id,
          role: systemRole.id,
          character: systemRole.name,
          description: systemRole.description || systemRole.personality || '',
          avatar: systemRole.imageUrl,
          age: systemRole.age || '?',
          isNew: false
        })
        setIsLoading(false)
        return
      }

      // 如果不是系统角色，使用Zustand store查询用户自定义角色
      const userCharactersStore = useUserCharactersStore.getState()
      const characters = await userCharactersStore.fetchUserCharacters()
      const userRole = characters.find((char: any) => char.id === roleId)
      if (userRole) {
        setRole({
          id: userRole.id,
          role: userRole.id,
          character: userRole.name,
          description: getDescription(userRole as any),
          avatar: userRole.imageUrl || '/images/roles/default.jpg',
          age: userRole.age || '?',
          custom: true
        })
      }
    } catch (error) {
      console.error('获取角色详情失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // 检查是否有现有的聊天记录
  const checkExistingChat = async () => {
    if (!roleId) return

    try {
      const response = await apiService.history.getByRole(roleId, 1)
      setHasExistingChat(response.chats && response.chats.length > 0)
    } catch (error) {
      console.error('检查聊天记录失败:', error)
      setHasExistingChat(false)
    }
  }

  // 判断来源页面类型 - 更精确的判断
  const getSourceType = () => {
    // 检查 location.state 中的来源信息
    const stateFrom = (location.state as any)?.from
    if (stateFrom) {
      return stateFrom
    }

    // 检查当前路径
    const currentPath = location.pathname
    if (currentPath.includes('/interactive')) {
      return 'interactive'
    } else if (currentPath.includes('/chat')) {
      return 'chat'
    }

    // 检查 document.referrer
    if (typeof document !== 'undefined' && document.referrer) {
      const referrer = document.referrer
      if (referrer.includes('/interactive')) {
        return 'interactive'
      } else if (referrer.includes('/chat')) {
        return 'chat'
      }
    }

    return 'unknown'
  }

  const sourceType = getSourceType()
  const isFromInteractive = sourceType === 'interactive'
  const isFromChat = sourceType === 'chat'

  // 处理返回按钮点击
  const handleGoBack = () => {
    const stateFrom = (location.state as any)?.from
    const returnPath = (location.state as any)?.returnPath

    if (returnPath) {
      // 如果有明确的返回路径，直接导航到该路径
      navigate(returnPath)
    } else if (stateFrom === 'chat') {
      // 如果来源是聊天，返回到聊天页面
      navigate('/chat')
    } else if (stateFrom === 'interactive') {
      // 如果来源是互动，返回到互动页面
      navigate('/interactive')
    } else {
      // 默认返回到发现页面
      navigate('/discover')
    }
  }

  // 处理静谧私语按钮点击
  const handleChatMode = async () => {
    if (!roleId || isChatLoading) return

    setIsChatLoading(true)
    try {
      await setGlobalRole(roleId)

      // 如果有现有聊天记录，跳转到最近的聊天
      if (hasExistingChat) {
        const response = await apiService.history.getByRole(roleId, 1)
        if (response.chats && response.chats.length > 0) {
          navigate(`/chat/${response.chats[0].id}`)
          return
        }
      }

      // 否则跳转到新聊天
      navigate('/chat')
    } catch (error) {
      console.error('设置角色失败:', error)
    } finally {
      setIsChatLoading(false)
    }
  }

  // 处理激情互动按钮点击
  const handleInteractiveMode = async () => {
    if (!roleId || isInteractiveLoading) return

    setIsInteractiveLoading(true)
    try {
      await setGlobalRole(roleId)
      navigate('/interactive')
    } catch (error) {
      console.error('设置角色失败:', error)
    } finally {
      setIsInteractiveLoading(false)
    }
  }

  // 处理新对话按钮点击
  const handleNewChat = async () => {
    if (!roleId || isNewChatLoading) return

    setIsNewChatLoading(true)
    try {
      await setGlobalRole(roleId)
      const newChatId = generateUUID()

      // 🚀 智能导航策略：优化用户从聊天→角色页→新对话的体验
      if (isFromChat) {
        console.log('🔄 [RoleProfile] 从聊天页面来，重置导航历史避免返回到旧聊天')
        // 使用replace并传递特殊状态，指示这是一个"重置"的导航
        navigate(`/chat/${newChatId}`, {
          replace: true,
          state: { resetHistory: true, fromRoleProfile: true }
        })
      } else {
        // 从其他页面来的，正常导航
        console.log('✨ [RoleProfile] 正常导航到新聊天')
        navigate(`/chat/${newChatId}`)
      }
    } catch (error) {
      console.error('设置角色失败:', error)
      // 错误时也采用相同的导航策略
      const fallbackChatId = generateUUID()
      if (isFromChat) {
        navigate(`/chat/${fallbackChatId}`, {
          replace: true,
          state: { resetHistory: true, fromRoleProfile: true }
        })
      } else {
        navigate(`/chat/${fallbackChatId}`)
      }
    } finally {
      setIsNewChatLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex flex-col h-screen bg-black">
        {/* 顶部图片区域骨架 */}
        <div className="relative h-[60vh] w-full">
          <Skeleton className="w-full h-full">
            <div className="w-full h-full bg-gradient-to-b from-default-300 to-default-400" />
          </Skeleton>
        </div>

        {/* 底部内容区域骨架 */}
        <div className="flex-1 bg-background p-6">
          <Skeleton className="h-8 w-32 mb-4 rounded" />
          <div className="flex gap-2 mb-6">
            <Skeleton className="h-10 w-20 rounded" />
            <Skeleton className="h-10 w-20 rounded" />
            <Skeleton className="h-10 w-20 rounded" />
          </div>
          <Skeleton className="h-12 w-full rounded" />
        </div>
      </div>
    )
  }

  if (!role) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-background">
        <Icon icon="solar:user-circle-linear" className="w-16 h-16 text-default-400 mb-4" />
        <p className="text-default-500">角色不存在</p>
        <Button variant="light" onPress={() => navigate(-1)} className="mt-4">
          返回
        </Button>
      </div>
    )
  }

  return (
    <AnimatePresence>
      <motion.div
        className="flex flex-col h-screen bg-black overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.05 }} // 更快的背景淡入
        style={{ paddingTop: `${safeArea.top}px` }}
      >
        {/* 角色头部 */}
        <RoleHeader
          role={role}
          roleId={roleId}
          safeAreaTop={safeArea.top}
          onGoBack={handleGoBack}
        />

        {/* 底部内容区域 */}
        <motion.div
          className="flex-1 bg-background rounded-t-3xl -mt-6 relative z-10 overflow-y-auto"
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.25, duration: 0.5, ease: [0.4, 0.0, 0.2, 1] }}
        >
          <div className="p-6 pb-20">
            {/* 功能入口按钮 */}
            <RoleActions
              isFromChat={isFromChat}
              isFromInteractive={isFromInteractive}
              hasExistingChat={hasExistingChat}
              isChatLoading={isChatLoading}
              isInteractiveLoading={isInteractiveLoading}
              isNewChatLoading={isNewChatLoading}
              onChatMode={handleChatMode}
              onInteractiveMode={handleInteractiveMode}
              onNewChat={handleNewChat}
            />

            {/* Tab 切换 */}
            <Tabs
              selectedKey={activeTab}
              onSelectionChange={key => setActiveTab(key as string)}
              className="w-full"
              classNames={{
                tabList: 'w-full',
                tab: 'flex-1',
                tabContent: 'text-sm font-medium'
              }}
            >
              <Tab key="gallery" title="写真集">
                <GalleryTab roleId={roleId} role={role} />
              </Tab>
              <Tab key="images" title="图片">
                <ImagesTab roleId={roleId} role={role} />
              </Tab>
              <Tab key="videos" title="视频">
                <VideosTab roleId={roleId} role={role} />
              </Tab>
            </Tabs>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
