import React, { useState, useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router'
import { Button, Card, CardBody, CardHeader, Divider, Spinner } from '@heroui/react'
import { Icon } from '@iconify/react'
import { paymentService } from '@/api/services/payment'
import { membershipService } from '@/api/services/membership'
import type { MembershipPlan } from '@/api/services/membership'
import type { PaymentOrder, PaymentStatus } from '@/api/services/payment'

export default function PaymentPage() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const planId = searchParams.get('planId')

  const [plan, setPlan] = useState<MembershipPlan | null>(null)
  const [paymentOrder, setPaymentOrder] = useState<PaymentOrder | null>(null)
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [step, setStep] = useState<
    'loading' | 'plan' | 'payment' | 'processing' | 'success' | 'failed'
  >('loading')

  // 加载会员计划信息
  useEffect(() => {
    if (planId) {
      loadPlanInfo()
    }
  }, [planId])

  const loadPlanInfo = async () => {
    try {
      const response = await membershipService.getPlans()
      if (response.success) {
        const selectedPlan = response.data.find(p => p.id === planId)
        if (selectedPlan) {
          setPlan(selectedPlan)
          // 预创建订单以获取价格信息（包括升级差价）
          await preCreateOrder(selectedPlan)
        } else {
          setError('会员计划不存在')
        }
      } else {
        setError(response.error || '加载会员计划失败')
      }
    } catch (error) {
      console.error('加载会员计划失败:', error)
      setError('加载会员计划失败')
    }
  }

  // 预创建订单以获取价格信息
  const preCreateOrder = async (selectedPlan: MembershipPlan) => {
    try {
      const response = await paymentService.createOrder({
        planId: selectedPlan.id,
        description: `订阅${selectedPlan.name}会员`,
        metadata: {
          planName: selectedPlan.name,
          pointsIncluded: selectedPlan.pointsIncluded
        }
      })
      setPaymentOrder(response.data)
      setStep('plan')
    } catch (error: any) {
      console.error('预创建订单失败:', error)
      setError(error.message || '获取价格信息失败')
      setStep('plan')
    }
  }

  // 确认订单，进入支付步骤
  const handleCreateOrder = async () => {
    if (!paymentOrder) return
    setStep('payment')
  }

  // 模拟支付成功
  const handleSimulatePaymentSuccess = async () => {
    if (!paymentOrder) return

    setLoading(true)
    setStep('processing')

    try {
      const response = await paymentService.simulatePayment({
        paymentId: paymentOrder.paymentId,
        orderId: paymentOrder.orderId,
        action: 'success'
      })

      setPaymentStatus(response.data)
      setStep('success')

      // 3秒后跳转到会员中心
      setTimeout(() => {
        navigate('/membership')
      }, 3000)
    } catch (error: any) {
      console.error('模拟支付失败:', error)
      setError(error.message || '支付处理失败')
      setStep('failed')
    } finally {
      setLoading(false)
    }
  }

  // 模拟支付失败
  const handleSimulatePaymentFailure = async () => {
    if (!paymentOrder) return

    setLoading(true)
    setStep('processing')

    try {
      const response = await paymentService.simulatePayment({
        paymentId: paymentOrder.paymentId,
        orderId: paymentOrder.orderId,
        action: 'failed'
      })

      setPaymentStatus(response.data)
      setStep('failed')
    } catch (error: any) {
      console.error('模拟支付失败:', error)
      setError(error.message || '支付处理失败')
      setStep('failed')
    } finally {
      setLoading(false)
    }
  }

  // 返回会员中心
  const handleGoBack = () => {
    navigate('/membership')
  }

  if (!planId) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardBody className="text-center p-8">
            <Icon icon="solar:close-circle-bold" className="w-16 h-16 text-danger mx-auto mb-4" />
            <h2 className="text-xl font-bold mb-2">参数错误</h2>
            <p className="text-default-600 mb-4">未选择会员计划</p>
            <Button color="primary" onPress={handleGoBack}>
              返回会员中心
            </Button>
          </CardBody>
        </Card>
      </div>
    )
  }

  if (error && !plan) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardBody className="text-center p-8">
            <Icon icon="solar:close-circle-bold" className="w-16 h-16 text-danger mx-auto mb-4" />
            <h2 className="text-xl font-bold mb-2">加载失败</h2>
            <p className="text-default-600 mb-4">{error}</p>
            <Button color="primary" onPress={handleGoBack}>
              返回会员中心
            </Button>
          </CardBody>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-4 max-w-md">
      {/* 加载中 */}
      {step === 'loading' && (
        <Card>
          <CardBody className="text-center p-8">
            <Spinner size="lg" color="primary" className="mb-4" />
            <h2 className="text-xl font-bold mb-2">加载中...</h2>
            <p className="text-default-600">正在获取价格信息</p>
          </CardBody>
        </Card>
      )}

      {/* 计划确认步骤 */}
      {step === 'plan' && plan && (
        <Card>
          <CardHeader className="pb-0">
            <div className="flex items-center gap-2">
              <Icon icon="solar:crown-bold" className="w-6 h-6 text-warning" />
              <h1 className="text-xl font-bold">确认订阅</h1>
            </div>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              {/* 计划信息 */}
              <div className="bg-primary-50 rounded-lg p-4">
                <h3 className="font-semibold text-primary-700 mb-2">{plan.name}</h3>
                <p className="text-sm text-primary-600 mb-3">{plan.description}</p>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>有效期:</span>
                    <span className="font-medium">{plan.durationDays} 天</span>
                  </div>
                  <div className="flex justify-between">
                    <span>包含积分:</span>
                    <span className="font-medium">{plan.pointsIncluded} 积分</span>
                  </div>
                  <Divider className="my-2" />
                  {paymentOrder?.isUpgrade ? (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm text-default-600">
                        <span>原价:</span>
                        <span>¥{paymentOrder.originalAmount?.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-sm text-success-600">
                        <span>升级优惠:</span>
                        <span>-¥{paymentOrder.savings?.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-lg font-bold text-primary-700">
                        <span>实付金额:</span>
                        <span>¥{paymentOrder.amount.toFixed(2)}</span>
                      </div>
                    </div>
                  ) : (
                    <div className="flex justify-between text-lg font-bold text-primary-700">
                      <span>价格:</span>
                      <span>¥{paymentOrder?.amount.toFixed(2) || plan.price.toFixed(2)}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* 错误信息 */}
              {error && (
                <div className="bg-danger-50 border border-danger-200 rounded-lg p-3">
                  <p className="text-danger-700 text-sm">{error}</p>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex gap-3">
                <Button variant="light" onPress={handleGoBack} className="flex-1">
                  取消
                </Button>
                <Button
                  color="primary"
                  onPress={handleCreateOrder}
                  isLoading={loading}
                  className="flex-1"
                >
                  {paymentOrder?.isUpgrade ? '确认升级' : '确认订阅'}
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* 支付步骤 */}
      {step === 'payment' && paymentOrder && (
        <Card>
          <CardHeader className="pb-0">
            <div className="flex items-center gap-2">
              <Icon icon="solar:card-bold" className="w-6 h-6 text-primary" />
              <h1 className="text-xl font-bold">模拟支付</h1>
            </div>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              {/* 订单信息 */}
              <div className="bg-default-50 rounded-lg p-4">
                <h3 className="font-semibold mb-2">订单信息</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>订单号:</span>
                    <span className="font-mono">{paymentOrder.orderId.slice(-8)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>商品:</span>
                    <span>{paymentOrder.planName}</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold">
                    <span>金额:</span>
                    <span>¥{paymentOrder.amount.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* 模拟支付说明 */}
              <div className="bg-warning-50 border border-warning-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <Icon icon="solar:info-circle-bold" className="w-5 h-5 text-warning-600 mt-0.5" />
                  <div>
                    <p className="text-warning-700 text-sm font-medium mb-1">开发环境模拟支付</p>
                    <p className="text-warning-600 text-xs">请选择支付结果进行测试</p>
                  </div>
                </div>
              </div>

              {/* 模拟支付按钮 */}
              <div className="space-y-3">
                <Button
                  color="success"
                  onPress={handleSimulatePaymentSuccess}
                  isLoading={loading}
                  className="w-full"
                  startContent={<Icon icon="solar:check-circle-bold" className="w-5 h-5" />}
                >
                  模拟支付成功
                </Button>
                <Button
                  color="danger"
                  variant="light"
                  onPress={handleSimulatePaymentFailure}
                  isLoading={loading}
                  className="w-full"
                  startContent={<Icon icon="solar:close-circle-bold" className="w-5 h-5" />}
                >
                  模拟支付失败
                </Button>
              </div>

              <Button variant="light" onPress={handleGoBack} className="w-full">
                取消支付
              </Button>
            </div>
          </CardBody>
        </Card>
      )}

      {/* 处理中步骤 */}
      {step === 'processing' && (
        <Card>
          <CardBody className="text-center p-8">
            <Spinner size="lg" color="primary" className="mb-4" />
            <h2 className="text-xl font-bold mb-2">处理中...</h2>
            <p className="text-default-600">请稍候，正在处理您的支付</p>
          </CardBody>
        </Card>
      )}

      {/* 成功步骤 */}
      {step === 'success' && (
        <Card>
          <CardBody className="text-center p-8">
            <Icon icon="solar:check-circle-bold" className="w-16 h-16 text-success mx-auto mb-4" />
            <h2 className="text-xl font-bold mb-2">支付成功！</h2>
            <p className="text-default-600 mb-4">恭喜您成功订阅会员</p>
            {paymentStatus && (
              <div className="bg-success-50 rounded-lg p-4 mb-4">
                <div className="text-sm space-y-1">
                  <p>支付金额: ¥{paymentStatus.amount.toFixed(2)}</p>
                  <p>
                    支付时间:{' '}
                    {paymentStatus.paidAt
                      ? new Date(paymentStatus.paidAt).toLocaleString()
                      : '刚刚'}
                  </p>
                </div>
              </div>
            )}
            <p className="text-sm text-default-500">3秒后自动跳转到会员中心...</p>
            <Button color="primary" onPress={handleGoBack} className="mt-4">
              立即查看
            </Button>
          </CardBody>
        </Card>
      )}

      {/* 失败步骤 */}
      {step === 'failed' && (
        <Card>
          <CardBody className="text-center p-8">
            <Icon icon="solar:close-circle-bold" className="w-16 h-16 text-danger mx-auto mb-4" />
            <h2 className="text-xl font-bold mb-2">支付失败</h2>
            <p className="text-default-600 mb-4">{error || '支付过程中发生错误，请重试'}</p>
            <div className="flex gap-3">
              <Button variant="light" onPress={handleGoBack} className="flex-1">
                返回会员中心
              </Button>
              <Button color="primary" onPress={() => setStep('payment')} className="flex-1">
                重新支付
              </Button>
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  )
}
