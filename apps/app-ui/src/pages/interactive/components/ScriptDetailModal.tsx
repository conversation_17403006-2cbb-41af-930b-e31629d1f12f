import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Modal, ModalContent, ModalBody, Button, Progress } from '@heroui/react'
import { Icon } from '@iconify/react'
import confetti from 'canvas-confetti'
import { useTranslation } from 'react-i18next'
import {
  motion,
  useMotionValue,
  PanInfo,
  useTransform,
  animate,
  AnimatePresence
} from 'framer-motion'
import { addToast } from '@heroui/react'
import { type Script } from '@/api/services/scripts'
import { scriptPurchaseService } from '@/api/services/script-purchase'
import { useScriptDownloadStore } from '@/stores/script-download-store'
import { useDeviceStore } from '@/stores/device-store'
import { useRetryDownload } from '@/hooks/use-retry-download'
import { PurchaseConfirmModal } from '@/components/ui/PurchaseConfirmModal'
import { EnhancedPermissionGuard } from '@/components/permission/EnhancedPermissionGuard'
import { useNavigate } from 'react-router'

interface ScriptDetailModalProps {
  isOpen: boolean
  onOpenChange: () => void
  scripts: Script[]
  selectedIndex: number
  onScriptSelect: (scriptId: string) => void
  onIndexChange?: (index: number) => void
  shouldCloseOnSelect?: boolean // 新增：是否在选择后关闭模态框
  purchasedScriptIds?: Set<string> // 新增：已购买的剧本ID集合
  onPurchaseSuccess?: (scriptId: string) => void // 新增：购买成功回调
}

/**
 * 剧本详情模态框组件
 * 支持共享元素过渡动画和左右滑动切换
 */
export const ScriptDetailModal: React.FC<ScriptDetailModalProps> = ({
  isOpen,
  onOpenChange,
  scripts,
  selectedIndex,
  onScriptSelect,
  onIndexChange,
  shouldCloseOnSelect = true, // 默认为 true，保持向后兼容
  purchasedScriptIds = new Set(),
  onPurchaseSuccess
}) => {
  const { t } = useTranslation('interactive')
  const navigate = useNavigate()
  const [currentIndex, setCurrentIndex] = useState(selectedIndex)
  const [layoutScript, setLayoutScript] = useState<Script | null>(null) // 用于layoutId的剧本
  const [initialIndex, setInitialIndex] = useState(selectedIndex) // 初始索引，用于判断是否使用layoutId
  const [isDragging, setIsDragging] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const x = useMotionValue(0)
  const constraintsRef = useRef<HTMLDivElement>(null)

  // 购买相关状态
  const [showPurchaseConfirm, setShowPurchaseConfirm] = useState(false)
  const [purchasingScriptId, setPurchasingScriptId] = useState<string | null>(null)
  // 注意：不再使用购买成功弹窗，改为内联显示购买成功状态
  const [justPurchasedScripts, setJustPurchasedScripts] = useState<Set<string>>(new Set()) // 在详情弹窗中刚购买成功的剧本

  // 移除升级引导相关状态 - 由EnhancedPermissionGuard自动处理

  const { downloadScript, isScriptDownloaded, getDownloadProgress } = useScriptDownloadStore()

  // 获取设备连接状态
  const { connectedDevice } = useDeviceStore()

  // 使用重试下载hook
  const { handleRetryDownload: retryDownloadScript } = useRetryDownload()

  // 触发纸屑特效
  const triggerConfetti = useCallback(() => {
    // 从屏幕中央发射纸屑
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 },
      colors: ['#ff2d97', '#892fff', '#ffd700', '#ff6b35', '#00d4ff']
    })

    // 延迟一点再发射第二波
    setTimeout(() => {
      confetti({
        particleCount: 50,
        spread: 60,
        origin: { y: 0.7 },
        colors: ['#ff2d97', '#892fff', '#ffd700', '#ff6b35', '#00d4ff']
      })
    }, 250)
  }, [])

  // 所有的 useTransform 必须在组件顶层调用
  const imageOffset = useTransform(x, [-375, 0, 375], [375, 0, -375])
  const prevImageOffset = useTransform(x, [-375, 0, 375], [0, -375, -750])
  const nextImageOffset = useTransform(x, [-375, 0, 375], [750, 375, 0])

  // 当模态框打开时，设置layout剧本和当前索引
  useEffect(() => {
    if (isOpen) {
      setCurrentIndex(selectedIndex)
      setInitialIndex(selectedIndex)
      setLayoutScript(scripts[selectedIndex]) // 设置用于layoutId的剧本
      x.set(0) // 重置位置
    } else {
      // 当弹窗关闭时，清理刚购买成功的状态
      setJustPurchasedScripts(new Set())
    }
  }, [selectedIndex, isOpen, scripts, x])

  // 获取当前剧本
  const currentScript = scripts[currentIndex]

  // 处理拖拽结束 - 修正滑动方向逻辑
  const handleDragEnd = async (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    if (isAnimating || purchasingScriptId) return // 购买中时也禁用拖拽

    setIsDragging(false)
    const threshold = 100 // 滑动阈值

    if (info.offset.x > threshold && currentIndex > 0) {
      // 向右滑动（从左向右），切换到上一个剧本
      setIsAnimating(true)
      const newIndex = currentIndex - 1

      // 当前图片向右滑出
      await animate(x, 375, { duration: 0.3, ease: 'easeOut' })

      // 更新索引
      setCurrentIndex(newIndex)
      onIndexChange?.(newIndex)

      // 新图片从左侧滑入
      x.set(-375)
      await animate(x, 0, { duration: 0.3, ease: 'easeOut' })
      setIsAnimating(false)
    } else if (info.offset.x < -threshold && currentIndex < scripts.length - 1) {
      // 向左滑动（从右向左），切换到下一个剧本
      setIsAnimating(true)
      const newIndex = currentIndex + 1

      // 当前图片向左滑出
      await animate(x, -375, { duration: 0.3, ease: 'easeOut' })

      // 更新索引
      setCurrentIndex(newIndex)
      onIndexChange?.(newIndex)

      // 新图片从右侧滑入
      x.set(375)
      await animate(x, 0, { duration: 0.3, ease: 'easeOut' })
      setIsAnimating(false)
    } else {
      // 没有达到阈值，弹回原位
      animate(x, 0, { duration: 0.3, ease: 'easeOut' })
    }
  }

  // 处理触摸开始 - 阻止页面滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
      return () => {
        document.body.style.overflow = 'unset'
      }
    }
  }, [isOpen])

  // 购买剧本 - 简化版，权限检查由EnhancedPermissionGuard处理
  const handlePurchaseScript = useCallback(
    async (script: Script) => {
      if (purchasingScriptId) return // 防止重复购买

      try {
        setPurchasingScriptId(script.id)

        const response = await scriptPurchaseService.purchaseScript(script.id)

        if (response.success) {
          // 在详情弹窗中购买成功，直接更新状态，不显示购买成功弹窗
          setJustPurchasedScripts(prev => new Set([...prev, script.id]))
          setShowPurchaseConfirm(false)

          // 触发纸屑特效
          triggerConfetti()

          // 通知父组件购买成功
          onPurchaseSuccess?.(script.id)

          addToast({
            title: t('toast.purchaseSuccess.title'),
            description: t('toast.purchaseSuccess.description', { scriptTitle: script.title }),
            color: 'success'
          })
        }
      } catch (err: any) {
        console.error(`${t('errors.purchaseScriptFailed')}`, err)

        // 权限错误由EnhancedPermissionGuard处理，这里只处理非权限相关的错误
        if (err?.code !== 'INSUFFICIENT_POINTS' && err?.code !== 'MEMBERSHIP_INSUFFICIENT_LEVEL') {
          const errorMessage = err?.message || t('errors.unknown')

          addToast({
            title: t('toast.purchaseFailed.title'),
            description: errorMessage,
            color: 'danger'
          })
        }

        // 购买失败时关闭确认弹窗
        setShowPurchaseConfirm(false)
      } finally {
        setPurchasingScriptId(null)
      }
    },
    [purchasingScriptId, onPurchaseSuccess]
  )

  // 开始剧情（下载内容并导航）
  const handleStartScript = useCallback(
    async (script: Script) => {
      try {
        // 检查是否已下载
        if (!(await isScriptDownloaded(script.id))) {
          // 下载剧本内容
          await downloadScript(script.id, script.title)
        }

        // 如果已连接设备，显示设备连接提示
        if (connectedDevice) {
          addToast({
            title: t('toast.deviceConnected.title'),
            description: t('toast.deviceConnected.description', {
              deviceName: connectedDevice.name,
              scriptTitle: script.title
            }),
            color: 'success'
          })
        }

        // 导航或调用回调
        onScriptSelect(script.id)

        // 根据 shouldCloseOnSelect 决定是否关闭模态框
        if (shouldCloseOnSelect) {
          onOpenChange()
        }
      } catch (err) {
        console.error('开始剧情失败:', err)
        addToast({
          title: t('toast.startScriptFailed.title'),
          description: err instanceof Error ? err.message : t('errors.unknown'),
          color: 'danger'
        })
      }
    },
    [
      isScriptDownloaded,
      downloadScript,
      onScriptSelect,
      shouldCloseOnSelect,
      onOpenChange,
      connectedDevice
    ]
  )

  // 判断剧本的按钮状态 - 优化：添加卡住检测
  const getScriptButtonState = useCallback(
    (script: Script) => {
      const isPurchased = purchasedScriptIds.has(script.id) || justPurchasedScripts.has(script.id)
      const isPurchasing = purchasingScriptId === script.id
      const downloadProgress = getDownloadProgress(script.id)
      const isDownloading =
        downloadProgress &&
        (downloadProgress.status === 'downloading' || downloadProgress.status === 'pending')
      const justPurchased = justPurchasedScripts.has(script.id)

      // 检测下载是否卡住（超过60秒没有变化且进度小于100%）
      const isStuck =
        downloadProgress &&
        downloadProgress.status === 'downloading' &&
        downloadProgress.progress < 100 &&
        (downloadProgress as any).lastUpdate &&
        Date.now() - (downloadProgress as any).lastUpdate > 60000

      if (script.pointsCost === 0) {
        // 免费剧本
        if (isDownloading && !isStuck) {
          const progressText = downloadProgress?.progress
            ? `${Math.round(downloadProgress.progress)}%`
            : t('scriptDetailModal.buttonText.downloading')
          return {
            type: 'downloading',
            text: progressText,
            disabled: true,
            progress: downloadProgress
          }
        } else if (isStuck) {
          // 下载卡住，允许重试
          return {
            type: 'retry',
            text: t('scriptDetailModal.buttonText.retryDownload'),
            disabled: false
          }
        }
        return {
          type: 'start',
          text: t('scriptDetailModal.buttonText.startScript'),
          disabled: false
        }
      }

      if (!isPurchased) {
        // 未购买的付费剧本
        if (isPurchasing) {
          return {
            type: 'purchasing',
            text: t('scriptDetailModal.buttonText.purchasing'),
            disabled: true
          }
        }
        return {
          type: 'purchase',
          text: t('scriptDetailModal.buttonText.pointsCost', { pointsCost: script.pointsCost }),
          disabled: false
        }
      }

      // 已购买的剧本
      if (justPurchased) {
        // 刚购买成功，显示特殊状态
        if (isDownloading && !isStuck) {
          const progressText = downloadProgress?.progress
            ? `${Math.round(downloadProgress.progress)}%`
            : t('scriptDetailModal.buttonText.downloading')
          return {
            type: 'downloading',
            text: progressText,
            disabled: true,
            progress: downloadProgress
          }
        } else if (isStuck) {
          return {
            type: 'retry',
            text: t('scriptDetailModal.buttonText.retryDownload'),
            disabled: false
          }
        }
        return {
          type: 'purchased',
          text: t('scriptDetailModal.buttonText.purchaseSuccess'),
          disabled: false
        }
      }

      if (isDownloading && !isStuck) {
        const progressText = downloadProgress?.progress
          ? `${Math.round(downloadProgress.progress)}%`
          : t('scriptDetailModal.buttonText.downloading')
        return {
          type: 'downloading',
          text: progressText,
          disabled: true,
          progress: downloadProgress
        }
      } else if (isStuck) {
        return {
          type: 'retry',
          text: t('scriptDetailModal.buttonText.retryDownload'),
          disabled: false
        }
      }
      return { type: 'start', text: t('scriptDetailModal.buttonText.startScript'), disabled: false }
    },
    [purchasedScriptIds, purchasingScriptId, getDownloadProgress, justPurchasedScripts]
  )

  // 处理按钮点击 - 优化：添加重试处理
  const handleButtonClick = useCallback(
    (script: Script) => {
      const buttonState = getScriptButtonState(script)

      if (buttonState.type === 'purchase') {
        // 显示购买确认弹窗
        setShowPurchaseConfirm(true)
      } else if (buttonState.type === 'start' || buttonState.type === 'purchased') {
        // 开始剧情（包括刚购买成功的剧本）
        handleStartScript(script)
      } else if (buttonState.type === 'retry') {
        // 下载卡住，重试下载
        retryDownloadScript(script, () => {
          // 重试成功后关闭模态框或执行其他操作
          if (shouldCloseOnSelect) {
            onOpenChange()
          }
        })
      }
    },
    [
      getScriptButtonState,
      handleStartScript,
      retryDownloadScript,
      shouldCloseOnSelect,
      onOpenChange
    ]
  )

  if (!currentScript || !layoutScript) return null

  // 判断当前图片是否应该使用layoutId（只有初始图片使用）
  const shouldUseLayoutId = currentIndex === initialIndex
  const buttonState = getScriptButtonState(currentScript)

  return (
    <>
      <Modal
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        size="full"
        hideCloseButton
        classNames={{
          base: 'bg-transparent',
          backdrop: 'bg-black/20 backdrop-blur-lg',
          wrapper: 'flex items-center justify-center p-0'
        }}
      >
        <ModalContent className="bg-transparent shadow-none w-full h-full m-0 max-w-none max-h-none">
          <ModalBody className="p-0 relative overflow-hidden bg-[#121521]">
            {/* 图片区域 - 只有这个区域可拖拽 */}
            <div
              ref={constraintsRef}
              className="absolute inset-0 w-full h-full pointer-events-none"
            >
              <motion.div
                className="absolute left-1/2 top-20 -translate-x-1/2 w-[375px] h-[500px] pointer-events-auto"
                drag="x"
                dragConstraints={constraintsRef}
                dragElastic={0.1}
                onDragStart={() => setIsDragging(true)}
                onDragEnd={handleDragEnd}
                style={{ x }}
                dragMomentum={false}
              >
                {/* 当前图片 - 只有初始图片使用layoutId */}
                <motion.div
                  key={`current-${currentIndex}`}
                  className="absolute left-1/2 top-0 -translate-x-1/2 w-[311px] h-[415px] rounded-2xl overflow-hidden bg-neutral-100"
                  layoutId={
                    shouldUseLayoutId && isOpen ? `script-image-${layoutScript.id}` : undefined
                  }
                  initial={isAnimating ? { opacity: 0 } : false}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <img
                    src={currentScript.coverImage}
                    alt={currentScript.title}
                    className="w-full h-full object-cover object-top"
                  />
                </motion.div>

                {/* 上一张图片 */}
                {currentIndex > 0 && (
                  <motion.div
                    className="absolute left-1/2 top-0 -translate-x-1/2 w-[311px] h-[415px] rounded-2xl overflow-hidden bg-neutral-100"
                    style={{ x: prevImageOffset }}
                  >
                    <img
                      src={scripts[currentIndex - 1].coverImage}
                      alt={scripts[currentIndex - 1].title}
                      className="w-full h-full object-cover object-top"
                    />
                  </motion.div>
                )}

                {/* 下一张图片 */}
                {currentIndex < scripts.length - 1 && (
                  <motion.div
                    className="absolute left-1/2 top-0 -translate-x-1/2 w-[311px] h-[415px] rounded-2xl overflow-hidden bg-neutral-100"
                    style={{ x: nextImageOffset }}
                  >
                    <img
                      src={scripts[currentIndex + 1].coverImage}
                      alt={scripts[currentIndex + 1].title}
                      className="w-full h-full object-cover object-top"
                    />
                  </motion.div>
                )}

                {/* 底部渐变遮罩 */}
                <div className="absolute left-1/2 top-[292px] -translate-x-1/2 w-[311px] h-[123px] pointer-events-none rounded-b-2xl overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-t from-black/90 via-black/50 to-transparent" />
                </div>
              </motion.div>

              {/* 右侧下一张图片预览（静态的，不参与拖拽） */}
              {currentIndex < scripts.length - 1 && !isDragging && !isAnimating && (
                <motion.div
                  key={`preview-${currentIndex + 1}`}
                  className="absolute left-[355px] top-[101px] w-[280px] h-[373px] rounded-2xl overflow-hidden opacity-50 bg-neutral-100"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 0.5, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                >
                  <img
                    src={scripts[currentIndex + 1].coverImage}
                    alt={scripts[currentIndex + 1].title}
                    className="w-full h-full object-cover object-top"
                  />
                </motion.div>
              )}
            </div>

            {/* 顶部指示器 - 固定位置，不参与拖拽 */}
            <motion.div
              className="absolute left-1/2 top-[59px] -translate-x-1/2 w-[69px] h-[9px] z-20"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.4 }}
            >
              <div className="flex items-center justify-center gap-2">
                {scripts.map((_, index) => (
                  <motion.div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === currentIndex ? 'bg-[#ff2d97]' : 'bg-white/30'
                    }`}
                    animate={{
                      scale: index === currentIndex ? 1.2 : 1
                    }}
                    transition={{ duration: 0.2 }}
                  />
                ))}
              </div>
            </motion.div>

            {/* 底部内容区域 - 固定位置，不参与拖拽 */}
            <motion.div
              className="absolute bottom-0 left-0 right-0 z-10 pb-[34px]"
              initial={{ y: 100, opacity: 0 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                delay: 0.4,
                duration: 0.5,
                type: 'spring',
                stiffness: 100
              }}
            >
              {/* 剧本信息 - 使用key确保正确更新 */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentScript.id}
                  className="px-8 mb-8"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {/* 标题 - 只有初始标题使用layoutId */}
                  <motion.h1
                    className="text-white text-[20px] font-semibold mb-2"
                    layoutId={
                      shouldUseLayoutId && isOpen ? `script-title-${layoutScript.id}` : undefined
                    }
                  >
                    {currentScript.title}
                  </motion.h1>

                  {/* 描述 */}
                  <p className="text-[#7c85b6] text-[13px] leading-normal mb-4 line-clamp-4">
                    {currentScript.description || t('scriptDetailModal.noDescription')}
                  </p>

                  {/* 积分成本显示 - 只有付费且未购买的剧本才显示 */}
                  {currentScript.pointsCost > 0 && !purchasedScriptIds.has(currentScript.id) && (
                    <div className="flex items-center gap-2 mb-4">
                      {justPurchasedScripts.has(currentScript.id) ? (
                        <>
                          <Icon
                            icon="solar:check-circle-bold"
                            width={16}
                            className="text-green-500"
                          />
                          <span className="text-green-500 text-[14px] font-medium">
                            {t('scriptDetailModal.purchaseSuccess')}
                          </span>
                        </>
                      ) : (
                        <>
                          <Icon icon="solar:star-bold" width={16} className="text-yellow-500" />
                          <span className="text-white text-[14px]">
                            {t('scriptDetailModal.pointsRequired', {
                              points: currentScript.pointsCost
                            })}
                          </span>
                          <span className="text-[#7c85b6] text-[12px]">
                            {t('scriptDetailModal.validityPeriod')}
                          </span>
                        </>
                      )}
                    </div>
                  )}

                  {/* 已购买剧本的有效期提示 */}
                  {currentScript.pointsCost > 0 &&
                    purchasedScriptIds.has(currentScript.id) &&
                    !justPurchasedScripts.has(currentScript.id) && (
                      <div className="flex items-center gap-2 mb-4">
                        <Icon
                          icon="solar:check-circle-bold"
                          width={16}
                          className="text-green-500"
                        />
                        <span className="text-green-500 text-[14px] font-medium">
                          {t('scriptDetailModal.purchased')}
                        </span>
                        <span className="text-[#7c85b6] text-[12px]">
                          {t('scriptDetailModal.validityPeriodShort')}
                        </span>
                      </div>
                    )}

                  {/* 标签 */}
                  <div className="flex items-center gap-1 mb-8">
                    {currentScript.tags?.slice(0, 3).map((tag: string) => (
                      <div key={tag} className="bg-[#33395c] rounded-xl px-2 py-px">
                        <span className="text-[#7c85b6] text-[12px] font-normal">{tag}</span>
                      </div>
                    ))}
                  </div>
                </motion.div>
              </AnimatePresence>

              {/* 开始/购买按钮 */}
              <div className="px-8">
                <div className="relative">
                  <Button
                    size="lg"
                    className="w-full h-12 rounded-[28px] text-white text-[16px] font-normal relative overflow-hidden"
                    style={{
                      background: '#ff2d97'
                    }}
                    startContent={
                      buttonState.type === 'purchase' ? (
                        <Icon icon="solar:star-bold" width={24} />
                      ) : buttonState.type === 'purchased' ? (
                        <Icon icon="solar:check-circle-bold" width={24} />
                      ) : buttonState.type === 'downloading' ? (
                        <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      ) : (
                        <Icon icon="solar:play-bold" width={24} />
                      )
                    }
                    onPress={() => handleButtonClick(currentScript)}
                    disabled={buttonState.disabled || isAnimating}
                  >
                    {buttonState.text}
                  </Button>

                  {/* 下载进度条 */}
                  {buttonState.type === 'downloading' && buttonState.progress && (
                    <Progress
                      value={buttonState.progress.progress || 0}
                      className="absolute bottom-0 left-0 w-full h-1"
                      classNames={{
                        base: 'max-w-none',
                        track: 'bg-transparent',
                        indicator: 'bg-white/40'
                      }}
                      radius="none"
                    />
                  )}

                  {/* 购买中的加载状态 */}
                  {buttonState.type === 'purchasing' && (
                    <div className="absolute inset-0 bg-white/10 rounded-[28px] flex items-center justify-center">
                      <div className="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    </div>
                  )}
                </div>
              </div>

              {/* 关闭按钮 */}
              <div className="flex justify-center mt-6">
                <button
                  className="w-12 h-12 rounded-full bg-transparent border border-white/20 flex items-center justify-center disabled:opacity-50"
                  onClick={onOpenChange}
                  disabled={isAnimating || !!purchasingScriptId} // 购买中时也禁用关闭
                >
                  <Icon icon="material-symbols:close" width={24} className="text-white" />
                </button>
              </div>

              {/* 底部指示器 */}
              <div className="absolute bottom-[9px] left-1/2 -translate-x-1/2 w-[134px] h-[5px] bg-white rounded-full" />
            </motion.div>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* 使用EnhancedPermissionGuard包装购买确认弹窗 */}
      <EnhancedPermissionGuard
        feature="script_purchase"
        uiStrategy="upgrade-modal"
        pointsCost={currentScript?.pointsCost}
        showLoadingOverlay={true}
      >
        {({ executeWithPermission }) => (
          <PurchaseConfirmModal
            isOpen={showPurchaseConfirm}
            onClose={() => {
              if (!purchasingScriptId) {
                setShowPurchaseConfirm(false)
              }
            }}
            onConfirm={async () => {
              if (currentScript && !purchasingScriptId) {
                const result = await executeWithPermission(() =>
                  handlePurchaseScript(currentScript)
                )
                if (!result.success) {
                  console.log('🚫 权限检查失败，停止购买流程')
                }
              }
            }}
            script={currentScript}
            isPurchasing={!!purchasingScriptId}
          />
        )}
      </EnhancedPermissionGuard>
    </>
  )
}
