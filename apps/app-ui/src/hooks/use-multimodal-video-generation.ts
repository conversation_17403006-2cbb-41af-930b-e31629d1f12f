import { useState, useCallback, useRef } from 'react'
import {
  multimodalVideoService,
  type MultimodalVideoGenerationRequest,
  type MultimodalVideoGenerationState
} from '@/api/services/multimodal-video'
import { usePermissionGuard } from './use-permission-guard'

interface UseMultimodalVideoGenerationOptions {
  onSuccess?: (videoUrl: string) => void
  onError?: (error: string) => void
  onProgress?: (progress: number, stage: string, status: string) => void
  onImageGenerated?: (imageUrl: string) => void // 图片生成完成回调
}

export function useMultimodalVideoGeneration(options: UseMultimodalVideoGenerationOptions = {}) {
  const [state, setState] = useState<MultimodalVideoGenerationState>({
    taskId: '',
    currentStage: 'image_generation',
    overallProgress: 0,
    stageProgress: 0,
    status: 'starting'
  })

  const [isGenerating, setIsGenerating] = useState(false)
  const currentPromptRef = useRef<string | null>(null)

  // 权限守卫 - 使用标准化权限检查，但不包含UI处理
  const permissionGuard = usePermissionGuard({
    feature: 'video_generation',
    onDenied: result => {
      console.log(
        '🚫 [PERMISSION] 视频生成权限不足:',
        result.reason,
        'ErrorCode:',
        result.errorCode
      )
    },
    onGranted: () => {
      console.log('✅ [PERMISSION] 视频生成权限验证通过')
    }
  })

  /**
   * 开始多模态视频生成
   */
  const generateVideo = useCallback(
    async (request: MultimodalVideoGenerationRequest) => {
      // 防止重复请求
      if (isGenerating || currentPromptRef.current === request.prompt) {
        console.log('🚫 [SKIP] 正在生成中或重复请求，跳过')
        return
      }

      try {
        // 检查权限
        const permissionResult = await permissionGuard.checkAccess()
        if (!permissionResult.hasPermission) {
          // 使用后端返回的标准化错误信息
          const error = permissionResult.reason || '您暂无视频生成权限'
          setState(prev => ({ ...prev, status: 'failed', error }))
          options.onError?.(error)
          return
        }

        // 记录当前请求
        currentPromptRef.current = request.prompt
        setIsGenerating(true)

        // 重置状态
        setState({
          taskId: '',
          currentStage: 'image_generation',
          overallProgress: 0,
          stageProgress: 0,
          status: 'starting',
          message: '正在启动多模态视频生成...'
        })

        console.log('🎬 [START] 开始多模态视频生成:', {
          prompt: request.prompt.substring(0, 50) + '...',
          hasCharacterAvatar: !!request.characterAvatar
        })

        // 调用API
        const result = await multimodalVideoService.generateVideo(request)

        if (result.success) {
          setState(prev => ({
            ...prev,
            taskId: result.data.taskId,
            status: 'processing',
            overallProgress: 5,
            message: '正在生成视频内容...'
          }))

          options.onProgress?.(5, 'image_generation', 'processing')

          console.log('✅ [SUCCESS] 多模态视频生成任务已创建:', {
            taskId: result.data.taskId,
            estimatedTime: result.data.estimatedTime
          })
        } else {
          throw new Error('多模态视频生成启动失败')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '多模态视频生成失败'
        console.error('❌ [ERROR] 多模态视频生成失败:', error)

        // 清除当前请求记录
        currentPromptRef.current = null
        setIsGenerating(false)

        setState(prev => ({
          ...prev,
          status: 'failed',
          error: errorMessage
        }))

        options.onError?.(errorMessage)
      }
    },
    [permissionGuard.checkAccess, options, isGenerating]
  )

  /**
   * 更新生成进度
   * 由外部组件调用，通常来自 Supabase Realtime 监听
   */
  const updateProgress = useCallback(
    (newState: Partial<MultimodalVideoGenerationState>) => {
      setState(prev => {
        // 保护 intermediateImageUrl，一旦设置就不会被清除（除非明确传入新值）
        const preservedIntermediateImageUrl =
          newState.intermediateImageUrl !== undefined
            ? newState.intermediateImageUrl
            : prev.intermediateImageUrl

        const updated = {
          ...prev,
          ...newState,
          intermediateImageUrl: preservedIntermediateImageUrl
        }

        // 触发回调
        if (newState.overallProgress !== undefined) {
          options.onProgress?.(
            newState.overallProgress,
            newState.currentStage || prev.currentStage,
            newState.status || prev.status
          )
        }

        // 图片生成完成回调
        if (newState.intermediateImageUrl && !prev.intermediateImageUrl) {
          options.onImageGenerated?.(newState.intermediateImageUrl)
        }

        // 视频生成完成回调
        if (newState.status === 'completed' && newState.finalVideoUrl) {
          setIsGenerating(false)
          currentPromptRef.current = null
          options.onSuccess?.(newState.finalVideoUrl)
        }

        // 生成失败
        if (newState.status === 'failed') {
          setIsGenerating(false)
          currentPromptRef.current = null
          if (newState.error) {
            options.onError?.(newState.error)
          }
        }

        return updated
      })
    },
    [options]
  )

  /**
   * 从消息附件解析状态
   */
  const parseStatusFromMessage = useCallback(
    (attachments: any[]) => {
      const parsedState = multimodalVideoService.parseGenerationStatusFromMessage(attachments)
      if (parsedState) {
        updateProgress(parsedState)
      }
    },
    [updateProgress]
  )

  /**
   * 取消生成
   */
  const cancelGeneration = useCallback(async () => {
    if (state.taskId) {
      const success = await multimodalVideoService.cancelGeneration(state.taskId)
      if (success) {
        setIsGenerating(false)
        currentPromptRef.current = null
        setState(prev => ({
          ...prev,
          status: 'starting',
          overallProgress: 0,
          stageProgress: 0
        }))
      }
      return success
    }
    return false
  }, [state.taskId])

  /**
   * 重置状态
   */
  const reset = useCallback(() => {
    setIsGenerating(false)
    currentPromptRef.current = null
    setState({
      taskId: '',
      currentStage: 'image_generation',
      overallProgress: 0,
      stageProgress: 0,
      status: 'starting'
    })
  }, [])

  /**
   * 获取当前阶段的描述
   */
  const getCurrentStageDescription = useCallback(() => {
    if (state.overallProgress < 30) {
      return '正在准备视频内容...'
    } else if (state.overallProgress < 70) {
      return '正在处理视频内容...'
    } else {
      return '正在渲染视频...'
    }
  }, [state.overallProgress])

  /**
   * 获取预计剩余时间
   */
  const getEstimatedRemainingTime = useCallback(() => {
    if (state.overallProgress === 0) return 120 // 初始预计2分钟

    const remainingProgress = 100 - state.overallProgress
    const timePerPercent = state.currentStage === 'image_generation' ? 0.6 : 0.9 // 图片阶段更快

    return Math.max(remainingProgress * timePerPercent, 10) // 至少10秒
  }, [state.overallProgress, state.currentStage])

  return {
    // 状态
    ...state,
    isGenerating,

    // 操作
    generateVideo,
    updateProgress,
    parseStatusFromMessage,
    cancelGeneration,
    reset,

    // 工具函数
    getCurrentStageDescription,
    getEstimatedRemainingTime,

    // 计算属性
    canGenerate: !isGenerating && state.status !== 'processing',
    isActive: isGenerating || state.status === 'processing',
    hasError: !!state.error,
    hasIntermediateImage: !!state.intermediateImageUrl,
    hasFinalVideo: !!state.finalVideoUrl,
    isImageStage: state.currentStage === 'image_generation',
    isVideoStage: state.currentStage === 'video_generation',

    // 进度相关
    imageProgress: state.currentStage === 'image_generation' ? state.overallProgress * 2 : 100, // 0-50% 映射到 0-100%
    videoProgress: state.currentStage === 'video_generation' ? (state.overallProgress - 50) * 2 : 0 // 50-100% 映射到 0-100%
  }
}
