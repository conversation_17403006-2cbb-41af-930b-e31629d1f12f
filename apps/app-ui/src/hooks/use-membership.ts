import { useState, useEffect, useCallback } from 'react'
import {
  membershipService,
  type MembershipPlan,
  type MembershipStatus,
  type UserPoints,
  type PointsTransaction
} from '@/api/services/membership'

// 会员计划 Hook
export function useMembershipPlans() {
  const [plans, setPlans] = useState<MembershipPlan[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchPlans = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      const response = await membershipService.getPlans()

      if (response.success) {
        setPlans(response.data)
      } else {
        setError(response.error || '获取会员计划失败')
      }
    } catch (err) {
      console.error('获取会员计划失败:', err)
      setError(err instanceof Error ? err.message : '获取会员计划失败')
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchPlans()
  }, [fetchPlans])

  return {
    plans,
    isLoading,
    error,
    refetch: fetchPlans
  }
}

// 会员状态 Hook
export function useMembershipStatus() {
  const [status, setStatus] = useState<MembershipStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchStatus = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      const response = await membershipService.getMembershipStatus()

      if (response.success) {
        setStatus(response.data)
      } else {
        setError(response.error || '获取会员状态失败')
      }
    } catch (err) {
      console.error('获取会员状态失败:', err)
      setError(err instanceof Error ? err.message : '获取会员状态失败')
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchStatus()
  }, [fetchStatus])

  return {
    status,
    isLoading,
    error,
    refetch: fetchStatus
  }
}

// 用户积分 Hook
export function useUserPoints() {
  const [points, setPoints] = useState<UserPoints | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchPoints = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      const response = await membershipService.getPoints()

      if (response.success) {
        setPoints(response.data)
      } else {
        setError(response.error || '获取积分信息失败')
      }
    } catch (err) {
      console.error('获取积分信息失败:', err)
      setError(err instanceof Error ? err.message : '获取积分信息失败')
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchPoints()
  }, [fetchPoints])

  // 消费积分
  const consumePoints = useCallback(
    async (feature: string, customAmount?: number, sourceId?: string, description?: string) => {
      try {
        const response = await membershipService.consumePoints({
          feature,
          customAmount,
          sourceId,
          description
        })

        if (response.success) {
          // 重新获取积分信息
          await fetchPoints()
          return response.data
        } else {
          throw new Error(response.error)
        }
      } catch (err) {
        console.error('积分消费失败:', err)
        const errorMessage = err instanceof Error ? err.message : '积分消费失败'
        throw err
      }
    },
    [fetchPoints]
  )

  return {
    points,
    isLoading,
    error,
    refetch: fetchPoints,
    consumePoints
  }
}

// 积分交易记录 Hook
export function usePointsTransactions(limit = 50) {
  const [transactions, setTransactions] = useState<PointsTransaction[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchTransactions = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      const response = await membershipService.getPointsTransactions(limit)

      if (response.success) {
        setTransactions(response.data)
      } else {
        setError(response.error || '获取交易记录失败')
      }
    } catch (err) {
      console.error('获取交易记录失败:', err)
      setError(err instanceof Error ? err.message : '获取交易记录失败')
    } finally {
      setIsLoading(false)
    }
  }, [limit])

  useEffect(() => {
    fetchTransactions()
  }, [fetchTransactions])

  return {
    transactions,
    isLoading,
    error,
    refetch: fetchTransactions
  }
}

// 订阅操作 Hook
export function useSubscription() {
  const [isSubscribing, setIsSubscribing] = useState(false)

  const subscribe = useCallback(async (planId: string, paymentId?: string) => {
    try {
      setIsSubscribing(true)
      const response = await membershipService.createSubscription({
        planId,
        paymentId
      })

      if (response.success) {
        return response.data
      } else {
        throw new Error(response.error)
      }
    } catch (err) {
      console.error('订阅失败:', err)
      const errorMessage = err instanceof Error ? err.message : '订阅失败'
      throw err
    } finally {
      setIsSubscribing(false)
    }
  }, [])

  return {
    subscribe,
    isSubscribing
  }
}

// 权限检查 Hook
export function usePermissionCheck() {
  const [isChecking, setIsChecking] = useState(false)

  const checkPermission = useCallback(
    async (
      permissionType:
        | 'text_chat'
        | 'character_create'
        | 'image_generation'
        | 'voice_generation'
        | 'script_purchase'
        | 'video_generation'
        | 'gallery_generation'
        | 'member_only',
      pointsCost?: number,
      requireMembership?: boolean
    ) => {
      try {
        setIsChecking(true)
        const response = await membershipService.checkPermission({
          permissionType,
          pointsCost,
          requireMembership
        })

        if (response.success) {
          return response.data
        } else {
          throw new Error(response.error)
        }
      } catch (err) {
        console.error('权限检查失败:', err)
        throw err
      } finally {
        setIsChecking(false)
      }
    },
    []
  )

  const verifyAccess = useCallback(async (feature: string) => {
    try {
      setIsChecking(true)
      const response = await membershipService.verifyAccess(feature)

      if (response.success) {
        return response.data
      } else {
        // 处理标准化错误响应
        const error = new Error(response.error) as any
        error.code = response.code
        error.data = response.data
        throw error
      }
    } catch (err) {
      console.error('功能访问验证失败:', err)
      throw err
    } finally {
      setIsChecking(false)
    }
  }, [])

  return {
    checkPermission,
    verifyAccess,
    isChecking
  }
}

// 综合会员数据 Hook（用于会员页面）
export function useMembershipData() {
  const { plans, isLoading: plansLoading, error: plansError } = useMembershipPlans()
  const {
    status,
    isLoading: statusLoading,
    error: statusError,
    refetch: refetchStatus
  } = useMembershipStatus()
  const {
    points,
    isLoading: pointsLoading,
    error: pointsError,
    refetch: refetchPoints
  } = useUserPoints()
  const { subscribe, isSubscribing } = useSubscription()

  const isLoading = plansLoading || statusLoading || pointsLoading
  const error = plansError || statusError || pointsError

  // 刷新所有数据
  const refreshAll = useCallback(async () => {
    await Promise.all([refetchStatus(), refetchPoints()])
  }, [refetchStatus, refetchPoints])

  // 处理订阅
  const handleSubscribe = useCallback(
    async (planId: string, paymentId?: string) => {
      try {
        const result = await subscribe(planId, paymentId)
        // 订阅成功后刷新数据
        await refreshAll()
        return result
      } catch (err) {
        throw err
      }
    },
    [subscribe, refreshAll]
  )

  return {
    // 数据
    plans,
    status,
    points,

    // 状态
    isLoading,
    error,
    isSubscribing,

    // 操作
    handleSubscribe,
    refreshAll
  }
}
