import { useState, useCallback } from 'react'
import { usePermissionCheck } from './use-membership'

// 权限检查结果
export interface PermissionResult {
  hasPermission: boolean
  reason?: string
  pointsRequired?: number
  pointsAvailable?: number
  isMember?: boolean
  userType?: string
  errorCode?: string // 新增错误码字段
  // 会员信息
  membershipInfo?: {
    planId: string
    planName: string
    endDate: Date
  }
  // 角色创建限制信息
  characterLimitInfo?: {
    current: number
    max: number
    canCreate: boolean
  }
}

// 功能类型枚举
export type FeatureType =
  | 'text_chat'
  | 'character_create'
  | 'image_generation'
  | 'voice_generation'
  | 'script_purchase'
  | 'video_generation'
  | 'gallery_generation'
  | 'member_only'

// 权限拦截策略
export interface PermissionStrategy {
  // 检查权限
  checkPermission: () => Promise<PermissionResult>
  // 权限不足时的处理
  onPermissionDenied?: (result: PermissionResult) => void
  // 权限通过时的处理
  onPermissionGranted?: (result: PermissionResult) => void
}

// 权限守卫配置
export interface PermissionGuardConfig {
  feature: FeatureType
  pointsCost?: number
  requireMembership?: boolean
  // UI呈现策略 (可选)
  showModal?: boolean
  showToast?: boolean
  showInline?: boolean
  // 自定义处理
  onDenied?: (result: PermissionResult) => void
  onGranted?: (result: PermissionResult) => void
}

/**
 * 权限守卫 Hook - 核心逻辑层
 * 职责：纯粹的权限检查逻辑，不涉及UI
 */
export function usePermissionGuard(config: PermissionGuardConfig) {
  const [isChecking, setIsChecking] = useState(false)
  const [lastResult, setLastResult] = useState<PermissionResult | null>(null)
  const { verifyAccess } = usePermissionCheck()

  /**
   * 重置权限状态
   */
  const resetPermission = useCallback(() => {
    setLastResult(null)
  }, [])

  /**
   * 执行权限检查
   */
  const checkAccess = useCallback(async (): Promise<PermissionResult> => {
    try {
      setIsChecking(true)

      // 使用 verifyAccess 获取更详细的信息
      const response = await verifyAccess(config.feature.toUpperCase())

      const result: PermissionResult = {
        hasPermission: response.canAccess,
        reason: response.reason,
        pointsRequired: response.pointsRequired,
        pointsAvailable: response.pointsAvailable,
        isMember: response.isMember,
        userType: response.userType
      }

      // 特殊处理角色创建功能，添加限制信息
      if (config.feature === 'character_create' && response.characterLimitInfo) {
        result.characterLimitInfo = {
          current: response.characterLimitInfo.current,
          max: response.characterLimitInfo.max,
          canCreate: response.characterLimitInfo.canCreate
        }
      }

      setLastResult(result)

      // 执行策略回调
      if (result.hasPermission) {
        config.onGranted?.(result)
      } else {
        config.onDenied?.(result)
      }

      return result
    } catch (error) {
      // 处理API错误，检查是否包含错误码
      let errorResult: PermissionResult = {
        hasPermission: false,
        reason: '权限检查失败，请稍后重试'
      }

      // 如果是API错误且包含错误码，提取相关信息
      if (error && typeof error === 'object' && 'code' in error) {
        const apiError = error as any
        errorResult = {
          hasPermission: false,
          reason: apiError.message || apiError.error || '权限不足',
          errorCode: apiError.code,
          pointsRequired: apiError.data?.pointsRequired,
          pointsAvailable: apiError.data?.pointsAvailable,
          userType: apiError.data?.userType || 'free',
          isMember: apiError.data?.membershipInfo ? true : false,
          membershipInfo: apiError.data?.membershipInfo
        }
      }

      setLastResult(errorResult)

      // 执行拒绝回调
      config.onDenied?.(errorResult)

      return errorResult
    } finally {
      setIsChecking(false)
    }
  }, [config, verifyAccess])

  /**
   * 带权限检查的执行器
   * 这是主要的对外接口
   */
  const executeWithPermission = useCallback(
    async <T>(
      action: () => Promise<T> | T
    ): Promise<{ success: boolean; result?: T; permission?: PermissionResult }> => {
      const permissionResult = await checkAccess()

      if (!permissionResult.hasPermission) {
        return {
          success: false,
          permission: permissionResult
        }
      }

      try {
        const result = await action()
        return {
          success: true,
          result,
          permission: permissionResult
        }
      } catch (error) {
        return {
          success: false,
          permission: permissionResult
        }
      }
    },
    [checkAccess]
  )

  return {
    // 状态
    isChecking,
    hasPermission: lastResult?.hasPermission ?? null,
    reason: lastResult?.reason,

    // 方法
    checkAccess,
    executeWithPermission,
    resetPermission, // 新增重置方法

    // 数据
    lastResult
  }
}

/**
 * 简化版权限检查 Hook
 * 用于简单的权限验证场景
 */
export function useFeatureAccess(feature: FeatureType) {
  const guard = usePermissionGuard({ feature })

  return {
    checkAccess: guard.checkAccess,
    isChecking: guard.isChecking,
    hasPermission: guard.hasPermission,
    reason: guard.reason,
    pointsRequired: guard.lastResult?.pointsRequired,
    pointsAvailable: guard.lastResult?.pointsAvailable
  }
}
