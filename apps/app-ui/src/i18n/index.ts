import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'
import { I18N_DEFAULTS, getAvailableLanguages } from '@/config/i18n'

// 导入翻译文件
import enTranslation from './locales/en/index'
import zhTranslation from './locales/zh/index'
import zhTWTranslation from './locales/zh-TW/index'
import esTranslation from './locales/es/index'
import jaTranslation from './locales/ja/index'

// 初始化i18next
i18n
  // 检测用户语言
  .use(LanguageDetector)
  // 将i18n实例传递给react-i18next
  .use(initReactI18next)
  // 初始化i18next
  .init({
    // 默认语言
    fallbackLng: I18N_DEFAULTS.DEFAULT_LANGUAGE,
    // 设置初始语言 - 确保国际版使用正确的默认语言
    lng: I18N_DEFAULTS.DEFAULT_LANGUAGE,
    // 不要在生产环境中使用
    debug: import.meta.env.DEV,
    // 命名空间
    ns: [
      'common',
      'login',
      'discover',
      'roleDetail',
      'register',
      'customRole',
      'character-creator-v2',
      'chat',
      'chat-v2',
      'chat-history',
      'device',
      'profile',
      'my',
      'photo-album',
      'interactive',
      'voice',
      'toast',
      'referral',
      'membership',
      'points'
    ],
    defaultNS: 'common',
    // 配置资源
    resources: {
      en: enTranslation,
      zh: zhTranslation,
      'zh-TW': zhTWTranslation,
      es: esTranslation,
      ja: jaTranslation
    },
    // 插值配置
    interpolation: {
      // 不需要在React中转义
      escapeValue: false
    },
    // 语言检测选项 - 确保优先使用我们设置的默认语言
    detection: {
      // 检测顺序：localStorage -> 默认语言
      order: ['localStorage', 'navigator'],
      // 缓存用户语言选择
      caches: ['localStorage']
    }
  })

// 在初始化完成后，检查并应用版本相关的语言设置
i18n.on('initialized', () => {
  const currentLanguage = i18n.language
  const availableLanguages = getAvailableLanguages()

  console.log('🌍 i18n 初始化完成:', {
    currentLanguage,
    defaultLanguage: I18N_DEFAULTS.DEFAULT_LANGUAGE,
    availableLanguages
  })

  // 如果当前语言不在可用语言列表中，切换到默认语言
  if (!availableLanguages.includes(currentLanguage)) {
    console.log('🔄 当前语言不可用，切换到默认语言:', I18N_DEFAULTS.DEFAULT_LANGUAGE)
    i18n.changeLanguage(I18N_DEFAULTS.DEFAULT_LANGUAGE)
  }
})

export interface Language {
  code: string
  name: string
  nativeName: string
}

// 完整的语言列表
export const allLanguages: Language[] = [
  {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文'
  },
  {
    code: 'zh-TW',
    name: 'Traditional Chinese',
    nativeName: '繁體中文'
  },
  {
    code: 'en',
    name: 'English',
    nativeName: 'English'
  },
  {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語'
  },
  {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español'
  }
]

// 根据当前版本过滤的语言列表
export const languages: Language[] = allLanguages.filter(lang => {
  const availableLanguages = getAvailableLanguages()
  return availableLanguages.includes(lang.code)
})

export default i18n
