{"message": {"ai_assistant": "AI助手"}, "multimodal_input": {"connected": "已连接", "stop_device_failed": "停止设备功能失败", "device_disconnected": "设备已断开连接", "disconnect_device": "断开设备", "recognizing_voice": "正在识别语音...", "message_sent": "消息已发送...", "input_message": "输入消息...", "stop_generating": "停止生成", "send_message": "发送消息", "connect_device": "连接设备"}, "image": {"using_local_image": "使用本地图片:", "localization_success": "图片本地化成功:", "localization_failed": "本地化失败:", "checking_attachments": "检查现有附件:", "found_local_image": "发现本地图片:", "found_remote_image": "发现远程图片，开始本地化:", "need_generation": "需要生成图片", "no_image_no_generation": "无图片且无需生成", "image_not_found": "未找到图片", "checking_failed": "检查附件失败:", "image_check_failed": "检查图片失败", "checking_image": "检查图片中...", "ai_generated_image": "AI生成的图片", "click_to_view": "点击查看大图", "generation_failed": "生成失败", "retry": "重试", "preview": "图片预览", "loading": "加载中...", "load_failed": "图片加载失败", "generated_image": "生成的图片", "download": "下载图片", "download_failed": "下载失败", "download_error": "下载出错", "upgrade_unlock": "升级解锁"}, "video": {"save_failed": "数据库保存失败，但本地文件已保存:", "localization_failed": "本地化失败:", "no_video": "无可显示的视频", "ai_generated_video": "AI生成视频", "processing": "处理中...", "thumbnail": "视频封面", "thumbnail_load_failed": "⚠️ 封面图片加载失败", "thumbnail_generation_failed_all_timepoints": "所有时间点的缩略图生成都失败了", "thumbnail_generation_success": "缩略图生成成功", "thumbnail_generation_black_frame": "检测到黑色帧，跳过时间点", "thumbnail_generation_error": "缩略图生成错误", "thumbnail_loading_failed": "缩略图加载失败", "loading": "加载视频中...", "loading_start": "开始加载视频", "loading_complete": "视频加载完成", "can_play_smoothly": "视频可以流畅播放", "load_failed": "视频加载失败", "loading_failed": "加载失败", "generated_video": "生成的视频", "playback": "视频播放", "playback_error": "播放错误", "play": "播放", "player_error": "播放器错误", "download_failed": "下载失败", "download_error": "下载错误", "download": "下载视频", "generation_failed": "视频生成失败", "retry": "重试", "try_again_later": "请稍后重试", "generation_preview": "生成预览", "generating": "正在生成视频...", "generation_complete": "视频生成完成", "generation_timeout": "视频生成超时", "checking_attachments": "检查现有附件", "found_completed_video": "发现已完成的视频", "found_generating_status": "发现生成中状态", "no_existing_status": "没有现有状态", "auto_start_generation": "自动开始生成", "polling_condition_check": "轮询条件检查", "start_polling": "开始轮询", "status_update": "状态更新", "generation_complete_stop_polling": "生成完成，停止轮询", "polling_failed": "轮询失败", "polling_timeout": "轮询超时", "stop_polling": "停止轮询", "generation_time_notice": "视频生成通常需要3-5分钟，请耐心等待", "upgrade_unlock": "升级解锁"}, "video_generation": {"success": "✅ 多模态视频生成完成:", "localization_complete": "✅ 视频本地化完成", "local_storage_failed": "⚠️ 本地存储失败:", "failed": "❌ 多模态视频生成失败:", "progress": "📊 多模态视频生成进度:", "image_complete": "🎨 图片生成完成:", "checking_attachments": "🔍 检查现有附件:", "completed_attachment_found": "检测到已完成的视频附件，跳过自动生成:", "generating_status_found": "检测到生成中状态，启动轮询监控", "generating": "正在生成视频...", "no_status_detected": "未检测到现有状态，准备开始生成", "using_local_image": "使用本地图片:", "querying_message_status": "查询数据库中的消息状态:", "query_failed": "查询消息失败，可能消息还未保存到数据库:", "message_found": "找到数据库中的消息:", "image_attachment_found": "数据库中已有图片附件:", "generating_status_attachment_found": "数据库中发现生成状态附件，已在生成中", "check_db_status_failed": "检查数据库状态失败", "check_message_status_failed": "检查消息状态失败", "permission_insufficient": "图片生成权限不足", "no_permission": "权限不足，无法生成图片", "permission_verified": "图片生成权限验证通过", "checking_permission": "正在检查权限...", "starting_generation": "正在启动图片生成...", "generation_call_success": "图片生成调用成功", "permission_verification_failed": "权限验证失败，停止生成", "edge_function_exception": "调用 Edge Function 异常", "generation_failed": "图片生成失败，请重试", "polling_condition_check": "轮询条件检查:", "start_polling": "开始轮询图片生成状态:", "status_update": "状态更新:", "generation_complete": "图片生成完成:", "local_storage_failed_using_remote": "本地存储失败，使用远程URL:", "generation_complete_short": "图片生成完成", "generation_failed_with_reason": "图片生成失败:", "generation_failed_short": "图片生成失败", "polling_failed": "轮询状态失败:", "generation_timeout": "图片生成超时，请重试", "video_generation_failed": "视频生成失败", "please_try_again": "请稍后重试", "generation_usually_takes": "视频生成通常需要3-5分钟，请耐心等待", "preview": "生成预览"}, "markdown": {"attachment": "附件", "loading_video_generator": "正在加载视频生成组件...", "video_generation_success": "✅ 视频生成完成:", "video_generation_failed": "❌ 视频生成失败:"}, "thinking_indicator": {"avatar": "角色头像"}, "device_connect": {"connect_device": "连接设备", "skip_connection": "跳过连接", "please_enter_device_code": "请输入设备码", "invalid_device_code": "设备码无效，请检查后重试", "connection_failed": "连接设备失败，请稍后重试", "connecting": "正在连接设备...", "device_code": "设备码", "enter_6_digit_code": "请输入6位设备码", "or": "或者", "loading_device_list": "正在加载设备列表...", "sample_device_codes": "示例设备码:", "scan_qr_code": "扫描设备二维码", "scanning": "扫描中...", "place_qr_code": "将设备二维码置于摄像头前进行扫描", "scanned_code_invalid": "扫描到的设备码无效", "no_available_devices": "暂无可用设备", "scan_failed": "扫描失败，请重试"}, "voice_recorder": {"get_mic_permission_failed": "获取麦克风权限失败:", "recording_failed": "录制失败", "recording_too_short": "录制时间太短", "operation_failed": "操作失败，请重试", "click_to_record": "点击录制或长按快速发送", "click_to_start_recording": "点击开始语音录制", "click_to_stop": "点击停止录制", "release_to_cancel": "松开取消录制", "release_to_send": "松开立即发送", "processing_audio": "正在处理录音...", "ready": "准备就绪", "cancel": "取消", "need_microphone_permission": "需要麦克风权限", "voice_recording_needs_mic": "语音录制功能需要访问您的麦克风", "privacy_protection": "隐私保护", "privacy_description": "我们不会保存您的录音内容，所有语音数据仅用于转换为文本", "allow_microphone_access": "允许访问麦克风"}, "device_control": {"device_control_panel": "设备控制台", "connected": "已连接", "bluetooth_error": "蓝牙异常", "bluetooth_ready": "蓝牙就绪", "bluetooth_initializing": "蓝牙初始化中...", "intensity": "强度", "off": "关闭", "level": "档", "classic_mode": "经典模式", "disconnect": "断开连接"}}