import { addToast } from '@heroui/react'
import type { PermissionResult } from '@/hooks/use-permission-guard'
import i18n from '@/i18n'

interface PermissionToastConfig {
  permission: PermissionResult
  featureName: string
  onActionClick?: () => void
}

/**
 * 权限Toast通知 - 函数式调用
 */
export function showPermissionToast({
  permission,
  featureName,
  onActionClick
}: PermissionToastConfig) {
  if (!permission.reason) {
    addToast({
      title: i18n.t('toast:permission.check_failed'),
      description: i18n.t('toast:permission.check_failed_description'),
      color: 'danger'
    })
    return
  }

  // 根据错误码而不是文本内容判断错误类型
  if (permission.errorCode === 'INSUFFICIENT_POINTS') {
    addToast({
      title: i18n.t('toast:permission.points_insufficient'),
      description: i18n.t('toast:permission.points_required', {
        featureName,
        points: permission.pointsRequired
      }),
      color: 'warning'
    })
    return
  }

  // 需要会员权限
  if (permission.errorCode === 'MEMBERSHIP_INSUFFICIENT_LEVEL') {
    addToast({
      title: i18n.t('toast:permission.member_only'),
      description: i18n.t('toast:permission.member_only_description', { featureName }),
      color: 'warning'
    })
    return
  }

  // 使用次数限制（暂时保留文本判断，因为可能没有对应的错误码）
  if (permission.reason.includes('使用次数') || permission.reason.includes('达到限制')) {
    addToast({
      title: i18n.t('toast:permission.usage_limit'),
      description: permission.reason,
      color: 'secondary'
    })
    return
  }

  // 通用权限不足
  // addToast({
  //   title: i18n.t('toast:permission.insufficient'),
  //   description: permission.reason,
  //   color: 'danger'
  // })
}
