import React, { ReactNode, useState } from 'react'
import { useNavigate } from 'react-router'
import {
  usePermissionGuard,
  type FeatureType,
  type PermissionResult
} from '@/hooks/use-permission-guard'
import { PermissionModal } from './PermissionModal'
import { showPermissionToast } from './PermissionToast'
import { UpgradeModal } from '@/components/ui/UpgradeModal'
import Loading from '@/components/common/loading'
import { useTranslation } from 'react-i18next'

// UI展示策略
type UIStrategy = 'modal' | 'toast' | 'upgrade-modal' | 'inline' | 'silent'

interface EnhancedPermissionGuardProps {
  // 权限配置
  feature: FeatureType
  pointsCost?: number
  requireMembership?: boolean

  // UI策略 - 默认使用upgrade-modal
  uiStrategy?: UIStrategy
  featureName?: string

  // Loading 配置
  showLoadingOverlay?: boolean // 是否显示全屏 Loading 遮罩

  // 自定义处理（可选）
  onPermissionDenied?: (result: PermissionResult) => void
  onPermissionGranted?: (result: PermissionResult) => void

  // 渲染策略
  children:
    | ReactNode
    | ((props: {
        hasPermission: boolean | null
        isChecking: boolean
        executeWithPermission: <T>(
          action: () => Promise<T> | T
        ) => Promise<{ success: boolean; result?: T }>
      }) => ReactNode)

  // 内联展示的回退内容
  fallback?: ReactNode
}

/**
 * 增强版权限守卫组件
 *
 * 特性：
 * 1. 集成 UpgradeModal，自动处理积分不足和会员权限问题
 * 2. 统一的错误处理，基于错误码而不是文本
 * 3. 支持多种UI展示策略
 * 4. 最小侵入式设计，可以包装任何组件
 *
 * 使用示例：
 * ```tsx
 * <EnhancedPermissionGuard feature="script_purchase" uiStrategy="upgrade-modal">
 *   {({ executeWithPermission }) => (
 *     <Button onPress={() => executeWithPermission(() => purchaseScript(script))}>
 *       购买剧本
 *     </Button>
 *   )}
 * </EnhancedPermissionGuard>
 * ```
 */
export const EnhancedPermissionGuard: React.FC<EnhancedPermissionGuardProps> = ({
  feature,
  pointsCost,
  requireMembership,
  uiStrategy = 'upgrade-modal',
  featureName,
  showLoadingOverlay = false,
  onPermissionDenied,
  onPermissionGranted,
  children,
  fallback
}) => {
  const { t } = useTranslation('permission')
  const navigate = useNavigate()

  // 升级模态框状态
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  const [upgradeModalData, setUpgradeModalData] = useState<{
    requiredPoints: number
    availablePoints: number
    remainingPoints: number
    errorCode: string
    userType: string
    isMember: boolean
    membershipLevel?: string // 当前会员等级
  } | null>(null)

  // 传统权限模态框状态
  const [modalOpen, setModalOpen] = useState(false)
  const [lastDeniedResult, setLastDeniedResult] = useState<PermissionResult | null>(null)

  // 获取功能名称
  const getFeatureName = () => {
    if (featureName) return featureName

    const names: Record<FeatureType, string> = {
      text_chat: t('text_chat'),
      character_create: t('character_create'),
      image_generation: t('image_generation'),
      voice_generation: t('voice_generation'),
      script_purchase: t('script_purchase'),
      video_generation: t('video_generation'),
      gallery_generation: t('gallery_generation'),
      member_only: t('member_only')
    }

    return names[feature] || t('this_feature')
  }

  // 统一的权限拒绝处理
  const handlePermissionDenied = (result: PermissionResult) => {
    console.log('🚫 [ENHANCED-PERMISSION] 权限被拒绝:', {
      feature,
      errorCode: result.errorCode,
      reason: result.reason
    })

    // 优先执行自定义处理
    if (onPermissionDenied) {
      onPermissionDenied(result)
      return
    }

    // 根据错误码和UI策略处理
    if (
      result.errorCode === 'INSUFFICIENT_POINTS' ||
      result.errorCode === 'MEMBERSHIP_INSUFFICIENT_LEVEL'
    ) {
      if (uiStrategy === 'upgrade-modal') {
        // 显示升级模态框，不显示 Toast
        setUpgradeModalData({
          requiredPoints: result.pointsRequired || 0,
          availablePoints: result.pointsAvailable || 0,
          remainingPoints: (result.pointsAvailable || 0) - (result.pointsRequired || 0),
          errorCode: result.errorCode,
          userType: result.userType || 'free',
          isMember: result.isMember || false,
          membershipLevel: result.membershipInfo?.planName || 'free'
        })
        setShowUpgradeModal(true)
        return // 直接返回，不执行后续的 Toast 逻辑
      }
    }

    // 其他UI策略的处理
    switch (uiStrategy) {
      case 'modal':
        setLastDeniedResult(result)
        setModalOpen(true)
        break
      case 'toast':
        showPermissionToast({
          permission: result,
          featureName: getFeatureName(),
          onActionClick: () => {
            if (result.errorCode === 'INSUFFICIENT_POINTS') {
              navigate('/points-store')
            } else if (result.errorCode === 'MEMBERSHIP_INSUFFICIENT_LEVEL') {
              navigate('/membership')
            } else {
              navigate('/membership')
            }
          }
        })
        break
      case 'silent':
        // 静默处理，不显示UI
        break
    }
  }

  // 权限通过处理
  const handlePermissionGranted = (result: PermissionResult) => {
    console.log('✅ [ENHANCED-PERMISSION] 权限验证通过:', { feature })
    if (onPermissionGranted) {
      onPermissionGranted(result)
    }
  }

  // 初始化权限守卫
  const guard = usePermissionGuard({
    feature,
    pointsCost,
    requireMembership,
    onDenied: handlePermissionDenied,
    onGranted: handlePermissionGranted
  })

  // 包装执行函数
  const executeWithPermission = async <T,>(action: () => Promise<T> | T) => {
    return guard.executeWithPermission(action)
  }

  // 渲染逻辑
  const renderContent = () => {
    if (typeof children === 'function') {
      return children({
        hasPermission: guard.hasPermission,
        isChecking: guard.isChecking,
        executeWithPermission
      })
    }

    // 如果权限检查失败且有fallback，显示fallback
    if (guard.hasPermission === false && fallback && uiStrategy === 'inline') {
      return fallback
    }

    return children
  }

  return (
    <>
      {renderContent()}

      {/* 权限检查 Loading 遮罩 */}
      {showLoadingOverlay && guard.isChecking && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center backdrop-blur-lg bg-black/50">
          <Loading size="lg" showText variant="primary" />
        </div>
      )}

      {/* 升级模态框 */}
      {showUpgradeModal && upgradeModalData && (
        <UpgradeModal
          isOpen={showUpgradeModal}
          onClose={() => {
            setShowUpgradeModal(false)
            setUpgradeModalData(null)
          }}
          onUpgrade={() => {
            setShowUpgradeModal(false)
            setUpgradeModalData(null)
            navigate('/membership')
          }}
          onPurchasePoints={() => {
            setShowUpgradeModal(false)
            setUpgradeModalData(null)
            navigate('/points-store')
          }}
          requiredPoints={upgradeModalData.requiredPoints}
          availablePoints={upgradeModalData.availablePoints}
          remainingPoints={upgradeModalData.remainingPoints}
          userType={upgradeModalData.userType}
          isMember={upgradeModalData.isMember}
          membershipLevel={upgradeModalData.membershipLevel}
          errorCode={upgradeModalData.errorCode}
        />
      )}

      {/* 传统权限模态框 */}
      {lastDeniedResult && (
        <PermissionModal
          isOpen={modalOpen}
          onClose={() => setModalOpen(false)}
          permission={lastDeniedResult}
          featureName={getFeatureName()}
          onUpgrade={() => {
            setModalOpen(false)
            navigate('/membership')
          }}
          onPurchasePoints={() => {
            setModalOpen(false)
            navigate('/points-store')
          }}
        />
      )}
    </>
  )
}
