import { ReactNode, useState } from 'react'
import { useNavigate } from 'react-router'
import {
  usePermissionGuard,
  type FeatureType,
  type PermissionResult
} from '@/hooks/use-permission-guard'
import { PermissionModal } from './PermissionModal'
import { showPermissionToast } from './PermissionToast'
import { useTranslation } from 'react-i18next'

// UI展示策略
type UIStrategy = 'modal' | 'toast' | 'inline' | 'silent'

interface PermissionGuardProps {
  // 权限配置
  feature: FeatureType
  pointsCost?: number
  requireMembership?: boolean

  // UI策略
  uiStrategy?: UIStrategy
  featureName?: string

  // 自定义处理
  onPermissionDenied?: (result: PermissionResult) => void
  onPermissionGranted?: (result: PermissionResult) => void

  // 自定义跳转
  onUpgrade?: () => void
  onPurchasePoints?: () => void

  // 渲染策略
  children:
    | ReactNode
    | ((props: {
        hasPermission: boolean | null
        isChecking: boolean
        checkAndExecute: <T>(
          action: () => Promise<T> | T
        ) => Promise<{ success: boolean; result?: T }>
      }) => ReactNode)

  // 内联展示的回退内容
  fallback?: ReactNode
}

/**
 * 权限守卫组件 - 最小侵入式设计
 *
 * 使用方式：
 * 1. 包装模式：<PermissionGuard feature="image_generation">{children}</PermissionGuard>
 * 2. 渲染函数模式：<PermissionGuard feature="image_generation">{({hasPermission, checkAndExecute}) => ...}</PermissionGuard>
 */
export function PermissionGuard({
  feature,
  pointsCost,
  requireMembership,
  uiStrategy = 'modal',
  featureName,
  onPermissionDenied,
  onPermissionGranted,
  onUpgrade,
  onPurchasePoints,
  children,
  fallback
}: PermissionGuardProps) {
  const navigate = useNavigate()
  const { t } = useTranslation('chat-v2')
  const [modalOpen, setModalOpen] = useState(false)
  const [lastDeniedResult, setLastDeniedResult] = useState<PermissionResult | null>(null)

  // 功能名称映射
  const getFeatureName = () => {
    if (featureName) return featureName

    const names: Record<FeatureType, string> = {
      text_chat: t('text_chat'),
      character_create: t('character_create'),
      image_generation: t('image_generation'),
      voice_generation: t('voice_generation'),
      script_purchase: t('script_purchase'),
      video_generation: t('video_generation'),
      gallery_generation: t('gallery_generation'),
      member_only: t('member_only')
    }

    return names[feature] || t('this_feature')
  }

  // 权限拒绝处理
  const handlePermissionDenied = (result: PermissionResult) => {
    setLastDeniedResult(result)

    // 自定义处理优先
    if (onPermissionDenied) {
      onPermissionDenied(result)
      return
    }

    // 根据UI策略展示
    switch (uiStrategy) {
      case 'modal':
        setModalOpen(true)
        break
      case 'toast':
        showPermissionToast({
          permission: result,
          featureName: getFeatureName(),
          onActionClick: () => {
            // 根据错误码而不是文本内容来判断错误类型
            if (result.errorCode === 'INSUFFICIENT_POINTS') {
              onPurchasePoints ? onPurchasePoints() : navigate('/points-store')
            } else if (result.errorCode === 'MEMBERSHIP_INSUFFICIENT_LEVEL') {
              onUpgrade ? onUpgrade() : navigate('/membership')
            } else {
              // 默认情况，可能是其他权限错误
              onUpgrade ? onUpgrade() : navigate('/membership')
            }
          }
        })
        break
      case 'inline':
        // 内联展示会在渲染时处理
        break
      case 'silent':
        // 静默处理，不显示UI
        break
    }
  }

  // 权限通过处理
  const handlePermissionGranted = (result: PermissionResult) => {
    if (onPermissionGranted) {
      onPermissionGranted(result)
    }
  }

  // 初始化权限守卫
  const guard = usePermissionGuard({
    feature,
    pointsCost,
    requireMembership,
    onDenied: handlePermissionDenied,
    onGranted: handlePermissionGranted
  })

  // 包装执行函数
  const checkAndExecute = async <T,>(action: () => Promise<T> | T) => {
    return guard.executeWithPermission(action)
  }

  // 渲染逻辑
  const renderContent = () => {
    // 渲染函数模式
    if (typeof children === 'function') {
      return children({
        hasPermission: guard.hasPermission,
        isChecking: guard.isChecking,
        checkAndExecute
      })
    }

    // 包装模式
    if (uiStrategy === 'inline' && guard.hasPermission === false) {
      return (
        fallback || (
          <div className="text-center p-4 text-default-500">
            <p>{guard.reason || t('permission_denied')}</p>
          </div>
        )
      )
    }

    return children
  }

  return (
    <>
      {renderContent()}

      {/* 权限弹窗 */}
      {uiStrategy === 'modal' && lastDeniedResult && modalOpen && (
        <PermissionModal
          isOpen={modalOpen}
          onClose={() => {
            setModalOpen(false)
            // 关闭弹窗时重置权限状态，让用户可以重新尝试
            guard.resetPermission()
          }}
          permission={lastDeniedResult}
          featureName={getFeatureName()}
          onUpgrade={onUpgrade || (() => navigate('/membership'))}
          onPurchasePoints={onPurchasePoints || (() => navigate('/points-store'))}
        />
      )}
    </>
  )
}
