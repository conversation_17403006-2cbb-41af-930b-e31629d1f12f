import { memo } from 'react'
import { useTranslation } from 'react-i18next'
import { cn } from '@/lib/utils'

interface MultimodalVideoPlaceholderProps {
  progress: number // 0-100
  status: 'starting' | 'processing' | 'completed' | 'failed'
  message?: string
  intermediateImageUrl?: string // 中间生成的图片
  showBlurredPreview?: boolean // 是否显示模糊预览
  onRetry?: () => void
  onUpgrade?: () => void // 新增：升级按钮回调
  showUpgradeButton?: boolean // 新增：是否显示升级按钮
}

export const MultimodalVideoPlaceholder = memo(
  ({
    progress,
    status,
    message,
    intermediateImageUrl,
    showBlurredPreview = false,
    onRetry,
    onUpgrade,
    showUpgradeButton = false
  }: MultimodalVideoPlaceholderProps) => {
    const { t } = useTranslation('chat-v2')
    message = message || t('video.generating')
    if (status === 'failed') {
      return (
        <div className="relative w-full max-w-md mx-auto">
          <div className="aspect-[5/7] bg-red-50 dark:bg-red-900/20 border-2 border-red-200 dark:border-red-800 rounded-lg flex flex-col items-center justify-center p-6">
            <div className="text-red-500 dark:text-red-400 text-center mb-4">
              <svg
                className="w-16 h-16 mx-auto mb-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
              <p className="text-lg font-medium">{t('video.generation_failed')}</p>
              <p className="text-sm mt-1 opacity-75">{message || t('video.try_again_later')}</p>
            </div>
            <div className="flex flex-col gap-2 w-full">
              {showUpgradeButton && onUpgrade ? (
                // 权限错误时，只显示升级按钮
                <button
                  onClick={onUpgrade}
                  className="px-6 py-2 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white text-sm rounded-lg transition-all duration-200 transform hover:scale-105"
                >
                  {t('video.upgrade_unlock')}
                </button>
              ) : (
                // 其他错误时，显示重试按钮
                onRetry && (
                  <button
                    onClick={onRetry}
                    className="px-6 py-2 bg-red-500 hover:bg-red-600 text-white text-sm rounded-lg transition-colors"
                  >
                    {t('video.retry')}
                  </button>
                )
              )}
            </div>
          </div>
        </div>
      )
    }

    return (
      <div className="relative w-full max-w-md mx-auto">
        <div className="aspect-[5/7] bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border-2 border-indigo-200 dark:border-indigo-800 rounded-lg overflow-hidden relative">
          {/* 背景图片（模糊预览） */}
          {showBlurredPreview && intermediateImageUrl && (
            <div className="absolute inset-0">
              <img
                src={intermediateImageUrl}
                alt={t('video.generation_preview')}
                className="w-full h-full object-cover"
                style={{
                  filter: 'blur(8px)',
                  opacity: 0.8
                }}
              />
              <div className="absolute inset-0 bg-white/20 dark:bg-black/20" />
            </div>
          )}

          {/* 骨架屏动画背景 */}
          <div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
            style={{
              background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
              animation: 'shimmer 2s infinite linear'
            }}
          />

          {/* 内容区域 */}
          <div className="relative h-full flex flex-col items-center justify-center p-6">
            {/* 视频图标 */}
            <div className="mb-6">
              <div className="relative">
                <div className="w-20 h-20 bg-white/90 dark:bg-gray-800/90 rounded-full flex items-center justify-center shadow-lg">
                  <svg
                    className="w-8 h-8 text-indigo-500 dark:text-indigo-400"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M8 5v14l11-7z" />
                  </svg>
                </div>

                {/* 进度环 */}
                {status === 'processing' && (
                  <svg className="absolute inset-0 w-20 h-20 -rotate-90" viewBox="0 0 80 80">
                    <circle cx="40" cy="40" r="36" fill="none" stroke="#e5e7eb" strokeWidth="4" />
                    <circle
                      cx="40"
                      cy="40"
                      r="36"
                      fill="none"
                      stroke="#6366f1"
                      strokeWidth="4"
                      strokeLinecap="round"
                      strokeDasharray={`${2 * Math.PI * 36}`}
                      strokeDashoffset={`${2 * Math.PI * 36 * (1 - progress / 100)}`}
                      style={{ transition: 'stroke-dashoffset 0.5s ease' }}
                    />
                  </svg>
                )}
              </div>
            </div>

            {/* 进度消息 */}
            {message && (
              <div className="mb-4 text-center">
                <p className="text-sm text-indigo-600 dark:text-indigo-300 font-medium">
                  {message}
                </p>
              </div>
            )}

            {/* 进度条 */}
            {status === 'processing' && progress > 0 && (
              <div className="w-full max-w-64 mb-4">
                <div className="w-full bg-indigo-200 dark:bg-indigo-800 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full transition-all duration-500 ease-out"
                    style={{ width: `${Math.min(progress, 100)}%` }}
                  />
                </div>
                <div className="flex justify-center text-xs text-indigo-600 dark:text-indigo-300 mt-2">
                  <span>{Math.round(progress)}%</span>
                </div>
              </div>
            )}

            {/* 加载动画点 */}
            <div className="flex space-x-2">
              {[0, 1, 2].map(i => (
                <div
                  key={i}
                  className={cn(
                    'w-3 h-3 bg-gradient-to-r from-indigo-400 to-purple-400 dark:from-indigo-300 dark:to-purple-300 rounded-full animate-bounce'
                  )}
                  style={{
                    animationDelay: `${i * 0.15}s`,
                    animationDuration: '0.8s'
                  }}
                />
              ))}
            </div>

            {/* 提示文字 */}
            {(status === 'starting' || status === 'processing') && (
              <div className="mt-4 text-xs text-indigo-500 dark:text-indigo-400 text-center max-w-xs opacity-75">
                {t('video.generation_time_notice')}
              </div>
            )}
          </div>
        </div>

        {/* 添加shimmer动画的CSS */}
        <style>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }
      `}</style>
      </div>
    )
  }
)

MultimodalVideoPlaceholder.displayName = 'MultimodalVideoPlaceholder'
