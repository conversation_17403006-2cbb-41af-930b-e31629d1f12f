import { memo, useEffect, useState, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router' // 添加导航 hook
import { useMultimodalVideoGeneration } from '@/hooks/use-multimodal-video-generation'
import { usePermissionGuard } from '@/hooks/use-permission-guard' // 添加权限守卫
import { MultimodalVideoPlaceholder } from './multimodal-video-placeholder'
import { multimodalVideoService } from '@/api/services/multimodal-video'
import { useRoleStore } from '@/stores/role-store'
import { VideoStorage } from '@/lib/media/video-storage'
import { VideoLocalizationService } from './video-localization.service'
import { VideoDisplay } from './video-display'
import { UpgradeModal } from '@/components/ui/UpgradeModal'

interface MultimodalVideoGenerationProps {
  messageId: string
  chatId: string
  prompt: string
  characterAvatar?: string
  onVideoGenerated?: (videoUrl: string) => void
  onError?: (error: string) => void
  existingAttachments?: Array<{
    url: string
    name?: string
    contentType?: string
    metadata?: {
      taskId?: string
      generatedAt?: string
    }
  }>
}

export const MultimodalVideoGeneration = memo(
  ({
    messageId,
    chatId,
    prompt,
    characterAvatar,
    onVideoGenerated,
    onError,
    existingAttachments
  }: MultimodalVideoGenerationProps) => {
    const { t } = useTranslation('chat-v2')
    const navigate = useNavigate() // 添加导航实例
    const { currentRole } = useRoleStore()
    const [hasStarted, setHasStarted] = useState(false)
    const [maxProgress, setMaxProgress] = useState(0) // 记录最高进度，防止回退

    // 升级弹窗相关状态
    const [showUpgradeModal, setShowUpgradeModal] = useState(false)
    const [upgradeModalData, setUpgradeModalData] = useState<{
      requiredPoints: number
      availablePoints: number
      remainingPoints: number
      errorCode: string
      userType: string
      isMember: boolean
      membershipLevel: string
    } | null>(null)

    // 独立的权限守卫，用于获取详细的权限错误信息
    const permissionGuard = usePermissionGuard({
      feature: 'video_generation',
      onDenied: result => {
        // 权限被拒绝时，静默处理，不自动弹出升级弹窗
        // 只记录权限错误信息，等待用户主动点击升级按钮
        console.log('🚫 [PERMISSION] 视频生成权限不足，静默处理:', result.reason)

        // 存储权限错误信息，供升级按钮点击时使用
        if (
          result.errorCode === 'INSUFFICIENT_POINTS' ||
          result.errorCode === 'MEMBERSHIP_INSUFFICIENT_LEVEL'
        ) {
          setUpgradeModalData({
            requiredPoints: result.pointsRequired || 0,
            availablePoints: result.pointsAvailable || 0,
            remainingPoints: (result.pointsAvailable || 0) - (result.pointsRequired || 0),
            errorCode: result.errorCode,
            userType: result.userType || 'free',
            isMember: result.isMember || false,
            membershipLevel: result.membershipInfo?.planName || 'free'
          })
        }
      },
      onGranted: () => {
        console.log('✅ [PERMISSION] 视频生成权限验证通过')
      }
    })

    const {
      // 状态
      overallProgress,
      status,
      intermediateImageUrl,
      finalVideoUrl,
      error,
      isActive,
      hasIntermediateImage,
      hasFinalVideo,

      // 操作
      generateVideo,
      updateProgress,
      getCurrentStageDescription,
      reset
    } = useMultimodalVideoGeneration({
      onSuccess: async videoUrl => {
        console.log(`✅ [SUCCESS] ${t('video.generation_complete')}:`, videoUrl)

        // 🔧 使用视频本地化服务，防重复下载
        if (messageId && videoUrl) {
          try {
            const videoLocalizationService = VideoLocalizationService.getInstance()

            // 使用本地化服务处理生成的视频
            const localizedUrl = await videoLocalizationService.localizeGeneratedVideo(
              videoUrl,
              messageId,
              prompt
            )
            console.log(`✅ [VideoGeneration] ${t('video.localization_complete')}`)

            // 回调使用本地化后的URL（如果本地化成功）
            onVideoGenerated?.(localizedUrl || videoUrl)
          } catch (storageError) {
            console.warn(`⚠️ [VideoGeneration] ${t('video.localization_failed')}:`, storageError)
            // 本地化失败时仍然回调原始URL
            onVideoGenerated?.(videoUrl)
          }
        } else {
          onVideoGenerated?.(videoUrl)
        }
      },
      onError: errorMsg => {
        console.error(`❌ [ERROR] ${t('video.generation_failed')}:`, errorMsg)
        onError?.(errorMsg)
      },
      onProgress: (progress, stage, currentStatus) => {
        console.log(`📊 [PROGRESS] ${t('video.generation_progress')}:`, {
          progress,
          stage,
          status: currentStatus
        })
      },
      onImageGenerated: imageUrl => {
        console.log(`🎨 [IMAGE] ${t('image.generation_complete')}:`, imageUrl)
      }
    })

    // 使用 ref 来避免 updateProgress 依赖导致的重复轮询
    const updateProgressRef = useRef(updateProgress)
    updateProgressRef.current = updateProgress

    // 直接检查现有附件，防止重复生成（最高优先级）
    useEffect(() => {
      console.log(`🔍 [INIT] ${t('video.checking_attachments')}:`, {
        messageId,
        existingAttachments
      })

      // 检查是否已有完成的视频附件
      const completedVideoAttachment = existingAttachments?.find(
        att => att.contentType === 'video/mp4' || att.contentType === 'video/webm'
      )

      if (completedVideoAttachment) {
        console.log(`🔍 [INIT] ${t('video.found_completed_video')}:`, completedVideoAttachment.url)
        setHasStarted(true)
        updateProgressRef.current({
          status: 'completed',
          overallProgress: 100,
          currentStage: 'video_generation',
          finalVideoUrl: completedVideoAttachment.url,
          message: t('video.generation_complete')
        })
        return
      }

      // 检查是否有生成中的状态附件
      const generatingAttachment = existingAttachments?.find(
        att => att.contentType?.includes('generating') && att.contentType?.includes('video')
      )

      if (generatingAttachment) {
        console.log(`🔍 [INIT] ${t('video.found_generating_status')}`)
        setHasStarted(true)

        // 设置生成中状态，触发轮询
        const metadata = (generatingAttachment.metadata as any) || {}
        updateProgressRef.current({
          status: 'processing',
          overallProgress: metadata.progress || 0,
          currentStage: metadata.stage || 'image_generation',
          message: generatingAttachment.name || t('video.generating'),
          intermediateImageUrl: metadata.intermediateImageUrl
        })
        return
      }

      console.log(`🔍 [INIT] ${t('video.no_existing_status')}`)
    }, [messageId, existingAttachments])

    // 自动开始生成
    useEffect(() => {
      if (!hasStarted && prompt && !isActive) {
        console.log(`🚀 [AUTO_START] ${t('video.auto_start_generation')}`)
        setHasStarted(true)

        // 先检查权限，确保权限错误能被正确处理
        const startGeneration = async () => {
          const permissionResult = await permissionGuard.checkAccess()
          if (!permissionResult.hasPermission) {
            // 权限检查失败，onDenied 回调会自动处理升级弹窗数据
            console.log('🚫 [AUTO_START] 权限检查失败，停止生成')
            return
          }

          // 权限通过，开始生成
          generateVideo({
            messageId,
            chatId,
            prompt,
            characterAvatar,
            characterId: currentRole?.id,
            metadata: {
              width: 720,
              height: 1280,
              duration: 3,
              fps: 24
            }
          })
        }

        startGeneration()
      }
    }, [
      hasStarted,
      prompt,
      isActive,
      generateVideo,
      messageId,
      chatId,
      characterAvatar,
      permissionGuard
    ])

    // 轮询状态更新
    useEffect(() => {
      console.log(`🔄 [POLLING] ${t('video.polling_condition_check')}:`, {
        messageId,
        isActive,
        status,
        shouldPoll: !!(messageId && isActive)
      })

      if (!messageId || !isActive) return

      console.log(`🔄 [POLLING] ${t('video.start_polling')}:`, messageId)

      const pollStatus = async () => {
        try {
          const result = await multimodalVideoService.getGenerationStatus(messageId)

          if (result.success && result.data) {
            const data = result.data as any

            // 进度平滑处理：确保进度只增不减
            const newProgress = data.progress || 0
            const smoothedProgress = Math.max(newProgress, maxProgress)

            console.log(`📊 [POLLING] ${t('video.status_update')}:`, {
              status: data.status,
              rawProgress: newProgress,
              smoothedProgress,
              stage: data.stage
            })

            // 更新最高进度
            if (smoothedProgress > maxProgress) {
              setMaxProgress(smoothedProgress)
            }

            updateProgressRef.current({
              status: data.status,
              overallProgress: smoothedProgress,
              currentStage: data.stage,
              message: data.message,
              ...(data.intermediateImageUrl && { intermediateImageUrl: data.intermediateImageUrl }),
              ...(data.finalVideoUrl && { finalVideoUrl: data.finalVideoUrl }),
              ...(data.error && { error: data.error })
            })

            // 如果完成或失败，停止轮询
            if (data.status === 'completed' || data.status === 'failed') {
              console.log(`✅ [POLLING] ${t('video.generation_complete_stop_polling')}`)
              clearInterval(intervalId)
            }
          }
        } catch (error) {
          console.error(`❌ [POLLING] ${t('video.polling_failed')}:`, error)
        }
      }

      // 立即查询一次
      pollStatus()

      // 每3秒轮询一次
      const intervalId = setInterval(pollStatus, 3000)

      // 10分钟超时
      const timeoutId = setTimeout(() => {
        console.warn(`⚠️ [POLLING] ${t('video.polling_timeout')}`)
        clearInterval(intervalId)
        updateProgressRef.current({
          status: 'failed',
          error: t('video.generation_timeout')
        })
      }, 600000) // 10分钟

      return () => {
        console.log(`🔄 [POLLING] ${t('video.stop_polling')}:`, messageId)
        clearInterval(intervalId)
        clearTimeout(timeoutId)
      }
    }, [messageId, isActive])

    // 处理升级按钮点击 - 只有在用户主动点击时才显示升级弹窗
    const handleUpgrade = () => {
      console.log('🎯 [USER_ACTION] 用户点击升级按钮')
      setShowUpgradeModal(true)
    }

    // 处理升级会员
    const handleUpgradeAction = () => {
      console.log('🎯 升级会员 - 跳转到会员中心')
      navigate('/membership')
      setShowUpgradeModal(false)
    }

    // 处理购买积分
    const handlePurchasePoints = () => {
      console.log('💰 购买积分 - 跳转到积分商城')
      navigate('/points-store')
      setShowUpgradeModal(false)
    }

    // 错误处理
    if (error) {
      // 使用 upgradeModalData 的存在来判断是否是权限相关错误
      const isPermissionError = upgradeModalData !== null

      return (
        <>
          <MultimodalVideoPlaceholder
            progress={0}
            status="failed"
            message={error}
            onRetry={reset}
            showUpgradeButton={isPermissionError}
            onUpgrade={handleUpgrade}
          />

          {/* 升级弹窗 - 使用真实的权限错误数据 */}
          {upgradeModalData && (
            <UpgradeModal
              isOpen={showUpgradeModal}
              onClose={() => setShowUpgradeModal(false)}
              onUpgrade={handleUpgradeAction}
              onPurchasePoints={handlePurchasePoints}
              requiredPoints={upgradeModalData.requiredPoints}
              availablePoints={upgradeModalData.availablePoints}
              remainingPoints={upgradeModalData.remainingPoints}
              userType={upgradeModalData.userType}
              isMember={upgradeModalData.isMember}
              membershipLevel={upgradeModalData.membershipLevel}
              errorCode={upgradeModalData.errorCode}
            />
          )}
        </>
      )
    }

    // 显示最终视频
    if (hasFinalVideo && finalVideoUrl) {
      return <VideoDisplay videoUrl={finalVideoUrl} title={t('video.ai_generated_video')} />
    }

    // 显示生成中的状态
    return (
      <MultimodalVideoPlaceholder
        progress={overallProgress}
        status={status}
        message={getCurrentStageDescription()}
        intermediateImageUrl={intermediateImageUrl}
        showBlurredPreview={hasIntermediateImage && overallProgress >= 30}
        onRetry={reset}
        showUpgradeButton={false} // 生成中状态不显示升级按钮
        onUpgrade={handleUpgrade}
      />
    )
  }
)

MultimodalVideoGeneration.displayName = 'MultimodalVideoGeneration'
