import { memo, useEffect, useState, lazy, Suspense, useRef } from 'react'
import ReactMarkdown, { type Components } from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Card, CardBody, Skeleton } from '@heroui/react'
import { EnhancedStreamAudioPlayer } from './enhanced-stream-audio-player'
import { useTranslation } from 'react-i18next'

// 🆕 使用统一的图片和视频容器
const ImageContainer = lazy(() =>
  import('./image').then(module => ({ default: module.ImageContainer }))
)
const VideoContainer = lazy(() =>
  import('./video/video-container').then(module => ({ default: module.VideoContainer }))
)

const components: Partial<Components> = {
  pre: ({ children }) => <>{children}</>,
  ol: ({ node, children, ...props }) => {
    return (
      <ol className="list-decimal list-outside ml-4" {...props}>
        {children}
      </ol>
    )
  },
  li: ({ node, children, ...props }) => {
    return (
      <li className="py-1" {...props}>
        {children}
      </li>
    )
  },
  ul: ({ node, children, ...props }) => {
    return (
      <ul className="list-decimal list-outside ml-4" {...props}>
        {children}
      </ul>
    )
  },
  strong: ({ node, children, ...props }) => {
    return (
      <span className="font-semibold" {...props}>
        {children}
      </span>
    )
  },
  a: ({ node, children, ...props }) => {
    return (
      <a className="text-primary hover:underline" target="_blank" rel="noreferrer" {...props}>
        {children}
      </a>
    )
  },
  h1: ({ node, children, ...props }) => {
    return (
      <h1 className="text-3xl font-semibold mt-6 mb-2" {...props}>
        {children}
      </h1>
    )
  },
  h2: ({ node, children, ...props }) => {
    return (
      <h2 className="text-2xl font-semibold mt-6 mb-2" {...props}>
        {children}
      </h2>
    )
  },
  h3: ({ node, children, ...props }) => {
    return (
      <h3 className="text-xl font-semibold mt-6 mb-2" {...props}>
        {children}
      </h3>
    )
  },
  h4: ({ node, children, ...props }) => {
    return (
      <h4 className="text-lg font-semibold mt-6 mb-2" {...props}>
        {children}
      </h4>
    )
  },
  h5: ({ node, children, ...props }) => {
    return (
      <h5 className="text-base font-semibold mt-6 mb-2" {...props}>
        {children}
      </h5>
    )
  },
  h6: ({ node, children, ...props }) => {
    return (
      <h6 className="text-sm font-semibold mt-6 mb-2" {...props}>
        {children}
      </h6>
    )
  }
}

const remarkPlugins = [remarkGfm]

const NonMemoizedMarkdownV2 = ({ children }: { children: string }) => {
  // 过滤掉设备信息标签
  const filteredContent = children.replace(/<device>.*?<\/device>/gs, '')

  return (
    <ReactMarkdown remarkPlugins={remarkPlugins} components={components}>
      {filteredContent}
    </ReactMarkdown>
  )
}

export const MarkdownV2 = memo(
  NonMemoizedMarkdownV2,
  (prevProps, nextProps) => prevProps.children === nextProps.children
)

// 为故事格式的样式定义CSS类
const storyStyles = `
  /* 打字机光标效果 */
  .typing-cursor-v2 {
    display: inline-block;
    width: 0.5em;
    height: 1em;
    background-color: currentColor;
    margin-left: 2px;
    animation: blinkV2 1s step-end infinite;
    vertical-align: text-bottom;
    box-shadow: 0 0 8px currentColor;
  }
  
  @keyframes blinkV2 {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
  }

  /* 流式输出时的容器动画 */
  .streaming-content-v2 {
    animation: fadeInUpV2 0.3s ease-out;
  }

  @keyframes fadeInUpV2 {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 气泡动画效果 */
  .bubble-enter-v2 {
    animation: bubbleSlideInV2 0.4s ease-out;
  }

  @keyframes bubbleSlideInV2 {
    from {
      opacity: 0;
      transform: translateY(10px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  /* 场景背景样式 - 为后续背景图预留 */
  .scene-background-v2 {
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
`

// 确保样式只注入一次
let stylesInjectedV2 = false

// 当检测到imagePrompt时的回调函数接口
type ImagePromptHandler = (imagePrompt: string) => void

// 当检测到场景时的回调函数接口
type SceneHandler = (sceneDescription: string) => void

/**
 * 解析内容的接口定义 - 新格式
 */
interface ParsedContent {
  scene?: string
  dialogues: string[]
  imagePrompt?: string
  videoPrompt?: string // 新增videoPrompt预留
  error?: string // 新增错误信息
  isComplete: {
    scene: boolean
    dialogue: boolean
    imagePrompt: boolean
    videoPrompt: boolean // 新增
    error: boolean // 新增错误完整性标记
  }
}

/**
 * 智能解析函数 - 新格式解析
 * 支持格式：
 * <scene>场景内容</scene>
 * <dialogue>对话内容(动作)</dialogue>
 * <imagePrompt>图片提示</imagePrompt>
 * <videoPrompt>视频提示</videoPrompt>
 * <error>错误信息key</error>
 */
const parseStreamingContent = (content: string, isStreaming: boolean = false): ParsedContent => {
  // 过滤掉设备信息标签
  const filteredContent = content.replace(/<device>.*?<\/device>/gs, '')

  const result: ParsedContent = {
    dialogues: [],
    isComplete: {
      scene: false,
      dialogue: false,
      imagePrompt: false,
      videoPrompt: false,
      error: false
    }
  }

  // 1. 解析场景描述 - 新格式 <scene>内容</scene>
  const completeSceneMatch = filteredContent.match(/<scene>([^<]+)<\/scene>/)
  if (completeSceneMatch) {
    result.scene = completeSceneMatch[1].trim()
    result.isComplete.scene = true
  } else if (isStreaming) {
    // 流式状态下，尝试匹配不完整的场景标签
    const incompleteSceneMatch = filteredContent.match(/<scene>([^<]*)$/)
    if (incompleteSceneMatch) {
      result.scene = incompleteSceneMatch[1].trim()
      result.isComplete.scene = false
    }
  }

  // 2. 解析对话内容 - 动作已融合在dialogue中
  const completeDialogueMatches = Array.from(
    filteredContent.matchAll(/<dialogue>([^<]+)<\/dialogue>/g)
  )
  if (completeDialogueMatches.length > 0) {
    result.dialogues = completeDialogueMatches.map(match => match[1].trim())
    result.isComplete.dialogue = true
  } else if (isStreaming) {
    // 流式状态下，尝试匹配不完整的对话标签
    const incompleteDialogueMatch = filteredContent.match(/<dialogue>([^<]*?)$/)
    if (incompleteDialogueMatch) {
      result.dialogues = [incompleteDialogueMatch[1].trim()]
      result.isComplete.dialogue = false
    }
  }

  // 3. 解析图片提示
  const completeImagePromptMatch = filteredContent.match(/<imagePrompt>([^<]+)<\/imagePrompt>/)
  if (completeImagePromptMatch) {
    result.imagePrompt = completeImagePromptMatch[1].trim()
    result.isComplete.imagePrompt = true
  } else if (isStreaming) {
    // 流式状态下，尝试匹配不完整的图片提示标签
    const incompleteImagePromptMatch = filteredContent.match(/<imagePrompt>([^<]*?)$/)
    if (incompleteImagePromptMatch) {
      result.imagePrompt = incompleteImagePromptMatch[1].trim()
      result.isComplete.imagePrompt = false
    }
  }

  // 4. 解析视频提示 - 新增预留
  const completeVideoPromptMatch = filteredContent.match(/<videoPrompt>([^<]+)<\/videoPrompt>/)
  if (completeVideoPromptMatch) {
    result.videoPrompt = completeVideoPromptMatch[1].trim()
    result.isComplete.videoPrompt = true
  } else if (isStreaming) {
    // 流式状态下，尝试匹配不完整的视频提示标签
    const incompleteVideoPromptMatch = filteredContent.match(/<videoPrompt>([^<]*?)$/)
    if (incompleteVideoPromptMatch) {
      result.videoPrompt = incompleteVideoPromptMatch[1].trim()
      result.isComplete.videoPrompt = false
    }
  }

  // 5. 解析错误信息 - 新增
  const completeErrorMatch = filteredContent.match(/<error>([^<]+)<\/error>/)
  if (completeErrorMatch) {
    result.error = completeErrorMatch[1].trim()
    result.isComplete.error = true
  } else if (isStreaming) {
    // 流式状态下，尝试匹配不完整的错误标签
    const incompleteErrorMatch = filteredContent.match(/<error>([^<]*?)$/)
    if (incompleteErrorMatch) {
      result.error = incompleteErrorMatch[1].trim()
      result.isComplete.error = false
    }
  }

  return result
}

/**
 * 故事格式解析组件 V2 - 新格式支持
 * 支持的标签格式：
 * <scene>场景描述</scene> - 场景描述，独立显示在顶部
 * <dialogue>对话内容(动作)</dialogue> - 对话内容，动作融合在内
 * <imagePrompt>图片提示</imagePrompt> - 图片生成提示
 * <videoPrompt>视频提示</videoPrompt> - 视频生成提示（预留）
 */
export const StoryMarkdownV2 = ({
  content,
  showCursor = false,
  onImagePrompt,
  onScene,
  characterAvatar,
  messageId,
  chatId,
  messageAttachments
}: {
  content: string
  showCursor?: boolean
  onImagePrompt?: ImagePromptHandler
  onScene?: SceneHandler
  characterAvatar?: string | null
  messageId?: string
  chatId?: string
  messageAttachments?: Array<{
    url: string
    name?: string
    contentType?: string
  }>
}) => {
  const { t } = useTranslation('chat-v2')
  // 跟踪上次触发的场景和图片提示，避免重复触发
  const lastSceneRef = useRef<string | null>(null)
  const lastImagePromptRef = useRef<string | null>(null)

  // 使用useEffect在组件挂载时注入样式
  useEffect(() => {
    if (!stylesInjectedV2) {
      const styleEl = document.createElement('style')
      styleEl.innerHTML = storyStyles
      document.head.appendChild(styleEl)
      stylesInjectedV2 = true
    }
  }, [])

  // 提取可播放的文本内容 - 过滤动作内容（括号部分）
  const getPlayableText = (parsed: ParsedContent): string => {
    // 改进的括号过滤逻辑，支持中英文括号混用和嵌套
    const filterBrackets = (text: string): string => {
      let result = text

      // 🔧 处理中英文括号混用情况，使用更强大的正则表达式
      // 匹配所有可能的圆括号组合：()、（）、(）、（)
      result = result.replace(/[\(（][^)\)）]*[\)）]/g, '')

      // 匹配所有可能的方括号组合：[]、［］、[］、［]
      result = result.replace(/[\[［][^\]］]*[\]］]/g, '')

      // 匹配所有可能的花括号组合：{}、｛｝、{｝、｛}
      result = result.replace(/[\{｛][^}｝]*[}｝]/g, '')

      // 匹配所有可能的尖括号组合（但保留HTML标签）
      result = result.replace(/(?<!<\/?)[\<＜][^>＞]*[>＞]/g, '')

      // 🔧 额外处理：清理可能遗留的单独括号
      result = result.replace(/[\(（\)）\[［\]］\{｛\}｝]/g, '')

      // 清理多余的空格、标点和换行
      result = result
        .replace(/\s+/g, ' ')
        .replace(/[,，。.!！?？;；:：]+\s*/g, '，')
        .trim()

      // 移除开头和结尾的标点符号
      result = result
        .replace(/^[,，。.!！?？;；:：\s]+/, '')
        .replace(/[,，。.!！?？;；:：\s]+$/, '')

      return result
    }

    // 过滤掉括号内的动作描述，只保留对话内容
    const filteredDialogues = parsed.dialogues
      .map(dialogue => filterBrackets(dialogue))
      .filter(text => text.length > 0)

    return filteredDialogues.join('。 ')
  }

  try {
    // 如果内容为空，直接返回空组件
    if (!content || content.trim() === '') {
      return <div className="story-content-v2"></div>
    }

    // 智能解析内容，支持流式和完整标签
    const parsed = parseStreamingContent(content, showCursor)

    // 检查是否已有图片附件
    const hasExistingImage = Boolean(
      messageAttachments &&
        messageAttachments.length > 0 &&
        messageAttachments.some(attachment => attachment.contentType?.startsWith('image/'))
    )

    // 检查是否已有视频附件
    const hasExistingVideo = Boolean(
      messageAttachments &&
        messageAttachments.length > 0 &&
        messageAttachments.some(
          attachment =>
            attachment.contentType === 'video/mp4' ||
            attachment.contentType === 'video/webm' ||
            attachment.contentType?.startsWith('video/')
        )
    )

    // 处理场景回调 - 无感生成背景图（增加防重复检查）
    if (parsed.scene && parsed.isComplete.scene && onScene && typeof onScene === 'function') {
      // 只有当场景内容与上次不同时才触发回调
      if (lastSceneRef.current !== parsed.scene) {
        lastSceneRef.current = parsed.scene
        onScene(parsed.scene)
      }
    }

    // 处理图片提示回调（增加防重复检查）
    if (
      parsed.imagePrompt &&
      parsed.isComplete.imagePrompt &&
      onImagePrompt &&
      typeof onImagePrompt === 'function'
    ) {
      // 只有当图片提示内容与上次不同时才触发回调
      if (lastImagePromptRef.current !== parsed.imagePrompt) {
        lastImagePromptRef.current = parsed.imagePrompt
        onImagePrompt(parsed.imagePrompt)
      }
    }

    // 优先处理错误信息
    if (parsed.error && parsed.isComplete.error) {
      return (
        <Card className="border-danger-200 bg-danger-50/50 dark:bg-danger-950/30">
          <CardBody className="space-y-2">
            <div className="flex items-center gap-2 text-danger-600 dark:text-danger-400">
              <svg
                className="w-5 h-5 flex-shrink-0"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
              <span className="font-medium text-sm">{parsed.error}</span>
            </div>
          </CardBody>
        </Card>
      )
    }

    // 如果没有解析到任何标签内容，回退到普通markdown
    const hasAnyContent =
      parsed.scene || parsed.dialogues.length > 0 || parsed.imagePrompt || parsed.videoPrompt

    if (!hasAnyContent) {
      // 可能是纯文本内容，使用普通markdown渲染
      return <MarkdownV2>{content + (showCursor ? ' ▋' : '')}</MarkdownV2>
    }

    return (
      <div className="story-content-v2 space-y-4">
        {/* 场景描述 - 独立显示在顶部，为后续背景图预留 */}
        {parsed.scene && (
          <div className="scene-background-v2 p-6 rounded-xl bg-gradient-to-br from-blue-50/80 via-indigo-50/60 to-purple-50/40 dark:from-blue-950/30 dark:via-indigo-950/20 dark:to-purple-950/10 border-l-4 border-blue-400/50 shadow-sm">
            <div className="text-blue-800 dark:text-blue-200 text-sm leading-relaxed font-medium italic">
              {parsed.scene}
              {showCursor && !parsed.isComplete.scene && (
                <span className="typing-cursor-v2 ml-1">▋</span>
              )}
            </div>
          </div>
        )}

        {/* 对话内容卡片 */}
        {parsed.dialogues.length > 0 && (
          <Card className="border-default-200/50 rounded-2xl rounded-tl-md">
            <CardBody className="space-y-3  bg-[#FF339A]">
              {parsed.dialogues.map((dialogue, index) => (
                <div
                  key={`dialogue-${index}`}
                  className="flex items-center gap-1 text-foreground text-base leading-relaxed"
                >
                  <div className="flex-1 font-medium">
                    {dialogue}
                    {showCursor &&
                      !parsed.isComplete.dialogue &&
                      index === parsed.dialogues.length - 1 && (
                        <span className="typing-cursor-v2 ml-1">▋</span>
                      )}
                  </div>
                  {/* 播放按钮 - 只在最后一个对话且不在流式状态时显示 */}
                  {!showCursor &&
                    messageId &&
                    index === parsed.dialogues.length - 1 &&
                    parsed.isComplete.dialogue && (
                      <EnhancedStreamAudioPlayer
                        text={getPlayableText(parsed)}
                        messageId={messageId}
                        chatId={chatId}
                        existingAttachments={messageAttachments?.map(att => {
                          return {
                            url: att.url,
                            name: att.name || t('markdown.attachment'),
                            contentType: att.contentType || 'application/octet-stream'
                          }
                        })}
                        className="opacity-70 hover:opacity-100 transition-opacity flex-shrink-0"
                      />
                    )}
                </div>
              ))}
            </CardBody>
          </Card>
        )}

        {/* 图片显示 - 只在完整时显示 */}
        {parsed.imagePrompt && parsed.isComplete.imagePrompt && (
          <div className="mt-4">
            {hasExistingImage && messageAttachments ? (
              <Suspense
                fallback={
                  <Skeleton className="w-full max-w-xs mx-auto aspect-[3/4] rounded-lg">
                    <div className="w-full h-full bg-default-200"></div>
                  </Skeleton>
                }
              >
                <ImageContainer
                  mode="display"
                  messageId={messageId!}
                  existingAttachments={messageAttachments?.map(att => ({
                    url: att.url,
                    name: att.name || '',
                    contentType: att.contentType || 'image/png'
                  }))}
                />
              </Suspense>
            ) : !hasExistingImage && characterAvatar ? (
              <Suspense
                fallback={
                  <Skeleton className="w-full max-w-xs mx-auto aspect-[3/4] rounded-lg">
                    <div className="w-full h-full bg-default-200"></div>
                  </Skeleton>
                }
              >
                <ImageContainer
                  key={`${messageId}-${parsed.imagePrompt.slice(0, 50)}`}
                  mode="generate"
                  prompt={parsed.imagePrompt}
                  characterAvatar={characterAvatar}
                  messageId={messageId!}
                  chatId={chatId}
                  existingAttachments={messageAttachments?.map(att => ({
                    url: att.url,
                    name: att.name || '',
                    contentType: att.contentType || 'image/png'
                  }))}
                />
              </Suspense>
            ) : null}
          </div>
        )}

        {/* 视频显示 - 优先显示现有视频，只有没有现有视频时才生成 */}
        {parsed.videoPrompt && parsed.isComplete.videoPrompt && messageId && chatId && (
          <div className="mt-4">
            {hasExistingVideo && messageAttachments ? (
              // 直接显示现有视频附件
              <Suspense
                fallback={
                  <Skeleton className="w-full aspect-video rounded-lg">
                    <div className="w-full h-full bg-default-200"></div>
                  </Skeleton>
                }
              >
                <VideoContainer
                  mode="display"
                  messageId={messageId}
                  existingAttachments={messageAttachments?.map(att => ({
                    url: att.url,
                    name: att.name,
                    contentType: att.contentType
                  }))}
                />
              </Suspense>
            ) : !hasExistingVideo ? (
              // 只有没有现有视频时才触发生成
              <Suspense
                fallback={
                  <Card className="border-dashed border-default-300">
                    <CardBody className="text-center text-default-500">
                      <Skeleton className="w-full h-32 rounded-lg" />
                      <div className="text-sm mt-2">正在加载视频生成组件...</div>
                    </CardBody>
                  </Card>
                }
              >
                <VideoContainer
                  key={`${messageId}-${parsed.videoPrompt.slice(0, 50)}`}
                  mode="generate"
                  prompt={parsed.videoPrompt}
                  characterAvatar={characterAvatar || undefined}
                  messageId={messageId}
                  chatId={chatId}
                  existingAttachments={messageAttachments}
                  onVideoGenerated={videoUrl => {
                    console.log(`✅ ${t('video.generation_complete')}:`, videoUrl)
                  }}
                  onError={error => {
                    console.error(`❌ ${t('video.generation_failed')}:`, error)
                  }}
                />
              </Suspense>
            ) : null}
          </div>
        )}
      </div>
    )
  } catch (error) {
    console.error('Error parsing story format:', error)
    return <MarkdownV2>{content + (showCursor ? ' ▋' : '')}</MarkdownV2>
  }
}
