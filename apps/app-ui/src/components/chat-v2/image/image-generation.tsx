import { memo, useEffect, useState, useRef } from 'react'
import { useNavigate } from 'react-router' // 添加导航 hook
import { ImagePlaceholder } from './image-placeholder'
import { ImageDisplay } from './image-display'
import { ImageLocalizationService } from './image-localization.service'
import { supabase } from '@/lib/supabase'
import { usePermissionGuard } from '@/hooks/use-permission-guard'
import { imageGenerationService } from '@/api/services/image-generation'
import { useRoleStore } from '@/stores/role-store'
import { UpgradeModal } from '@/components/ui/UpgradeModal'

interface ImageGenerationProps {
  prompt: string
  characterAvatar?: string | null
  messageId?: string
  chatId?: string
  existingAttachments?: Array<{
    url: string
    name: string
    contentType: string
    metadata?: {
      status?: string
      progress?: number
      timestamp?: string
    }
  }>
}

// 图片生成进度状态
interface GenerationProgress {
  messageId: string
  status: 'starting' | 'processing' | 'completed' | 'failed'
  progress: number
  estimatedSteps: number
  completedSteps: number
  message?: string
  error?: string
}

const PureImageGeneration = ({
  prompt,
  characterAvatar,
  messageId,
  chatId,
  existingAttachments
}: ImageGenerationProps) => {
  const navigate = useNavigate() // 添加导航实例
  const { currentRole } = useRoleStore()
  const [imageUrl, setImageUrl] = useState<string | null>(null)
  const [status, setStatus] = useState<'pending' | 'processing' | 'completed' | 'failed'>('pending')
  const [error, setError] = useState<string | null>(null)
  const [hasCheckedDatabase, setHasCheckedDatabase] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false) // 防重复生成标志

  // 进度状态
  const [progress, setProgress] = useState(0)
  const [estimatedSteps, setEstimatedSteps] = useState(100)
  const [completedSteps, setCompletedSteps] = useState(0)
  const [progressMessage, setProgressMessage] = useState<string>('')
  const [maxProgress, setMaxProgress] = useState(0) // 记录最高进度，防止回退

  // 轮询相关
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  // 🔧 新增：防重复本地化标记
  const hasLocalizedRef = useRef<boolean>(false)

  // 升级弹窗相关状态
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  const [upgradeModalData, setUpgradeModalData] = useState<{
    requiredPoints: number
    availablePoints: number
    remainingPoints: number
    errorCode: string
    userType: string
    isMember: boolean
    membershipLevel: string
  } | null>(null)

  // 🆕 获取本地化服务实例
  const localizationService = ImageLocalizationService.getInstance()

  // 🆕 处理已有图片的本地化（后台异步进行）
  const handleExistingImageLocalization = async (remoteImageUrl: string, messageId: string) => {
    try {
      const localImageUrl = await localizationService.localizeExistingImage(
        remoteImageUrl,
        messageId
      )
      if (localImageUrl && localImageUrl !== remoteImageUrl) {
        // 如果本地图片更优，更新显示
        setImageUrl(localImageUrl)
      }
    } catch (error) {
      console.warn('⚠️ [LOCALIZE] 图片本地化失败:', error)
    }
  }

  // 直接检查现有附件，防止重复生成（最高优先级）
  useEffect(() => {
    console.log('🔍 [INIT] 检查现有附件:', { messageId, existingAttachments })

    if (existingAttachments && existingAttachments.length > 0) {
      // 先检查是否有真实的图片附件
      const imageAttachment = existingAttachments.find(
        attachment =>
          attachment.contentType.startsWith('image/') &&
          !attachment.contentType.startsWith('image/generating')
      )
      if (imageAttachment) {
        console.log('🔍 [INIT] 检测到已完成的图片附件，跳过自动生成:', imageAttachment.url)

        // 🆕 处理已有图片的本地化
        handleExistingImageLocalization(imageAttachment.url, messageId!)

        setImageUrl(imageAttachment.url)
        setStatus('completed')
        setProgress(100)
        setCompletedSteps(100)
        setMaxProgress(100)
        setIsGenerating(false)
        return
      }

      // 检查是否有生成状态附件
      const generatingAttachment = existingAttachments.find(attachment =>
        attachment.contentType.startsWith('image/generating')
      )
      if (generatingAttachment) {
        console.log('🔍 [INIT] 检测到生成中状态，启动轮询监控:', {
          contentType: generatingAttachment.contentType,
          metadata: generatingAttachment.metadata
        })
        setStatus('processing')
        setIsGenerating(true)
        if (generatingAttachment.metadata) {
          const progress = generatingAttachment.metadata.progress || 0
          setProgress(progress)
          setMaxProgress(progress)
          setCompletedSteps(progress)
          setProgressMessage(generatingAttachment.name || '正在生成图片...')
          console.log('🔍 [INIT] 设置初始进度:', progress)
        }
        return
      }
    }

    // 如果没有现有图片且有提示词，检查数据库状态
    if (prompt && !imageUrl && messageId && !hasCheckedDatabase && !isGenerating) {
      console.log('🔍 [INIT] 未检测到现有状态，准备开始生成')
      checkDatabaseAndStartGeneration()
    }
  }, [existingAttachments, prompt, imageUrl, messageId, hasCheckedDatabase, isGenerating])

  // 检查数据库状态并智能启动图片生成
  const checkDatabaseAndStartGeneration = async () => {
    if (!messageId || !prompt || isGenerating) return

    setHasCheckedDatabase(true)

    try {
      // 🆕 优先检查本地图片文件
      const localImageUrl = await localizationService.checkLocalImage(messageId)
      if (localImageUrl) {
        console.log('🎉 [LOCAL] 使用本地图片:', localImageUrl)
        setImageUrl(localImageUrl)
        setStatus('completed')
        setProgress(100)
        setCompletedSteps(100)
        return
      }

      console.log('🔍 [DEBUG] 查询数据库中的消息状态:', messageId)

      // 查询数据库中的消息状态
      const { data: message, error: queryError } = await supabase
        .from('Message')
        .select('id, attachments, created_at')
        .eq('id', messageId)
        .single()

      if (queryError) {
        console.warn('⚠️ [WARN] 查询消息失败，可能消息还未保存到数据库:', queryError)
        // 消息可能还未保存到数据库，等待一下再重试
        setTimeout(() => {
          setHasCheckedDatabase(false) // 重置状态，允许重新检查
        }, 2000)
        return
      }

      console.log('✅ [SUCCESS] 找到数据库中的消息:', {
        messageId,
        hasAttachments: !!message.attachments,
        attachments: message.attachments,
        createdAt: message.created_at
      })

      // 检查是否有图片附件或生成状态附件
      if (message.attachments) {
        const attachments = Array.isArray(message.attachments)
          ? message.attachments
          : JSON.parse(message.attachments as string)

        // 检查真实图片附件
        const imageAttachment = attachments.find(
          (att: any) =>
            att.contentType?.startsWith('image/') &&
            !att.contentType?.startsWith('image/generating')
        )

        if (imageAttachment) {
          console.log('🎉 [SUCCESS] 数据库中已有图片附件:', imageAttachment.url)

          // 🆕 处理数据库中已有图片的本地化
          handleExistingImageLocalization(imageAttachment.url, messageId)

          setImageUrl(imageAttachment.url)
          setStatus('completed')
          setProgress(100)
          setCompletedSteps(100)
          return
        }

        // 检查生成状态附件
        const generatingAttachment = attachments.find((att: any) =>
          att.contentType?.startsWith('image/generating')
        )

        if (generatingAttachment) {
          console.log('🔄 [DEBUG] 数据库中发现生成状态附件，已在生成中')
          setStatus('processing')
          setIsGenerating(true)
          if (generatingAttachment.metadata) {
            setProgress(generatingAttachment.metadata.progress || 0)
            setProgressMessage(generatingAttachment.name || '正在生成图片...')
          }
          return
        }
      }

      // 没有图片附件且没有进度信息，启动生成
      await startImageGeneration()
    } catch (error) {
      console.error('❌ [ERROR] 检查数据库状态失败:', error)
      setStatus('failed')
      setError('检查消息状态失败')
      setIsGenerating(false)
    }
  }

  // 权限守卫
  const permissionGuard = usePermissionGuard({
    feature: 'image_generation',
    onDenied: result => {
      // 权限被拒绝时，静默处理，不自动弹出升级弹窗
      // 只记录权限错误信息，等待用户主动点击升级按钮
      console.log(
        '🚫 [PERMISSION] 图片生成权限不足，静默处理:',
        result.reason,
        'ErrorCode:',
        result.errorCode
      )

      // 存储权限错误信息，供升级按钮点击时使用
      if (
        result.errorCode === 'INSUFFICIENT_POINTS' ||
        result.errorCode === 'MEMBERSHIP_INSUFFICIENT_LEVEL'
      ) {
        setUpgradeModalData({
          requiredPoints: result.pointsRequired || 0,
          availablePoints: result.pointsAvailable || 0,
          remainingPoints: (result.pointsAvailable || 0) - (result.pointsRequired || 0),
          errorCode: result.errorCode,
          userType: result.userType || 'free',
          isMember: result.isMember || false,
          membershipLevel: result.membershipInfo?.planName || 'free'
        })
      }

      // 直接使用后端返回的标准化错误信息，显示在图片面板上
      const errorMessage = result.reason || '权限不足，无法生成图片'

      setStatus('failed')
      setError(errorMessage)
      setIsGenerating(false)
    },
    onGranted: () => {
      console.log('✅ [PERMISSION] 图片生成权限验证通过')
    }
  })

  // 启动图片生成
  const startImageGeneration = async () => {
    if (!messageId || !prompt || isGenerating || !chatId || !characterAvatar) return

    setIsGenerating(true) // 设置生成标志，防止重复调用
    setStatus('processing')
    setProgress(5)
    setCompletedSteps(5)
    setProgressMessage('正在检查权限...')

    try {
      // 权限检查
      const permissionResult = await permissionGuard.executeWithPermission(async () => {
        setProgressMessage('正在启动图片生成...')

        // 使用统一的图片生成服务（带降级机制）
        const result = await imageGenerationService.generateImage({
          messageId,
          chatId,
          prompt,
          characterAvatar,
          characterId: currentRole?.id, // 添加角色 ID
          metadata: {
            width: 1024,
            height: 1440
          }
        })

        console.log(`✅ [SUCCESS] 图片生成调用成功 (${result.source}):`, result.data)
        return result.data
      })

      if (!permissionResult.success) {
        console.log('🚫 [PERMISSION] 权限验证失败，停止生成')
        setStatus('failed')
        setError('权限不足，无法生成图片')
        setProgress(0)
        setCompletedSteps(0)
        setIsGenerating(false)
        return
      }

      // 保持 processing 状态，等待轮询更新
    } catch (error) {
      console.error('❌ [ERROR] 调用 Edge Function 异常:', error)
      setStatus('failed')
      setError('图片生成失败，请重试')
      setProgress(0)
      setCompletedSteps(0)
      setIsGenerating(false) // 重置生成标志
    }
  }

  // 处理升级按钮点击 - 只有在用户主动点击时才显示升级弹窗
  const handleUpgrade = () => {
    console.log('🎯 [USER_ACTION] 用户点击升级按钮')
    setShowUpgradeModal(true)
  }

  // 处理升级会员
  const handleUpgradeAction = () => {
    console.log('🎯 升级会员 - 跳转到会员中心')
    navigate('/membership')
    setShowUpgradeModal(false)
  }

  // 处理购买积分
  const handlePurchasePoints = () => {
    console.log('💰 购买积分 - 跳转到积分商城')
    navigate('/points-store')
    setShowUpgradeModal(false)
  }

  // 轮询状态更新 - 原始逻辑 + 本地存储增强
  useEffect(() => {
    console.log('🔄 [POLLING] 轮询条件检查:', {
      messageId,
      status,
      shouldPoll: !!(messageId && status === 'processing')
    })

    if (!messageId || status !== 'processing') return

    console.log('🔄 [POLLING] 开始轮询图片生成状态:', messageId)

    const pollStatus = async () => {
      try {
        const result = await imageGenerationService.getGenerationStatus(messageId)

        if (result.success && result.data) {
          const data = result.data

          // 进度平滑处理：确保进度只增不减
          const newProgress = data.progress || 0
          const smoothedProgress = Math.max(newProgress, maxProgress)

          console.log('📊 [POLLING] 状态更新:', {
            status: data.status,
            rawProgress: newProgress,
            smoothedProgress,
            message: data.message
          })

          // 更新最高进度
          if (smoothedProgress > maxProgress) {
            setMaxProgress(smoothedProgress)
          }

          // 更新状态
          setProgress(smoothedProgress)
          setCompletedSteps(smoothedProgress)
          setProgressMessage(data.message)

          // 如果完成，设置图片URL并停止轮询
          if (data.status === 'completed' && data.finalImageUrl) {
            console.log('✅ [POLLING] 图片生成完成:', data.finalImageUrl)

            // 🔧 修复：防重复本地化，只执行一次
            if (!hasLocalizedRef.current) {
              hasLocalizedRef.current = true

              // 🆕 新增：尝试保存到本地存储和数据库
              try {
                const localImageUrl = await localizationService.localizeGeneratedImage(
                  data.finalImageUrl,
                  messageId,
                  prompt
                )
                setImageUrl(localImageUrl || data.finalImageUrl)
              } catch (localError) {
                console.warn('⚠️ [POLLING] 本地存储失败，使用远程URL:', localError)
                setImageUrl(data.finalImageUrl)
              }
            } else {
              console.log('⚠️ [POLLING] 图片已本地化，跳过重复处理')
              // 如果已经本地化过，但没有设置imageUrl，则设置远程URL
              if (!imageUrl) {
                setImageUrl(data.finalImageUrl)
              }
            }

            setStatus('completed')
            setProgress(100)
            setCompletedSteps(100)
            setProgressMessage('图片生成完成')
            setError(null)
            setIsGenerating(false)

            // 清理轮询
            if (pollingIntervalRef.current) {
              clearInterval(pollingIntervalRef.current)
              pollingIntervalRef.current = null
            }
            if (timeoutRef.current) {
              clearTimeout(timeoutRef.current)
              timeoutRef.current = null
            }
            return
          }

          // 如果失败，停止轮询
          if (data.status === 'failed') {
            console.error('❌ [POLLING] 图片生成失败:', data.error)
            setStatus('failed')
            setError(data.error || '图片生成失败')
            setIsGenerating(false)

            // 清理轮询
            if (pollingIntervalRef.current) {
              clearInterval(pollingIntervalRef.current)
              pollingIntervalRef.current = null
            }
            if (timeoutRef.current) {
              clearTimeout(timeoutRef.current)
              timeoutRef.current = null
            }
          }
        }
      } catch (error) {
        console.error('❌ [POLLING] 轮询状态失败:', error)
      }
    }

    // 立即查询一次
    pollStatus()

    // 每3秒轮询一次
    pollingIntervalRef.current = setInterval(pollStatus, 3000)

    // 10分钟超时
    timeoutRef.current = setTimeout(() => {
      console.warn('⚠️ [POLLING] 图片生成超时')
      setStatus('failed')
      setError('图片生成超时，请重试')
      setIsGenerating(false)

      // 清理轮询
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
        pollingIntervalRef.current = null
      }
    }, 600000) // 10分钟

    // 清理函数
    return () => {
      console.log('🧹 [POLLING] 停止轮询图片生成状态:', messageId)
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
        pollingIntervalRef.current = null
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
    }
  }, [messageId, status, maxProgress, prompt])

  // 如果有图片，显示图片
  if (imageUrl) {
    return <ImageDisplay imageUrl={imageUrl} alt="AI生成的图片" />
  }

  // 显示占位图（只在非完成状态时显示）
  if (status !== 'completed') {
    const isPermissionError = upgradeModalData !== null

    return (
      <>
        <ImagePlaceholder
          status={status as 'pending' | 'processing' | 'failed'}
          progress={progress}
          estimatedSteps={estimatedSteps}
          completedSteps={completedSteps}
          error={error}
          message={progressMessage}
          showUpgradeButton={status === 'failed' && isPermissionError}
          onUpgrade={handleUpgrade}
        />

        {/* 升级弹窗 */}
        {upgradeModalData && (
          <UpgradeModal
            isOpen={showUpgradeModal}
            onClose={() => setShowUpgradeModal(false)}
            onUpgrade={handleUpgradeAction}
            onPurchasePoints={handlePurchasePoints}
            requiredPoints={upgradeModalData.requiredPoints}
            availablePoints={upgradeModalData.availablePoints}
            remainingPoints={upgradeModalData.remainingPoints}
            userType={upgradeModalData.userType}
            isMember={upgradeModalData.isMember}
            membershipLevel={upgradeModalData.membershipLevel}
            errorCode={upgradeModalData.errorCode}
          />
        )}
      </>
    )
  }

  // 如果状态是completed但没有图片URL，返回null
  return null
}

export const ImageGeneration = memo(PureImageGeneration, (prevProps, nextProps) => {
  // 返回true表示props相等，不需要重新渲染；返回false表示需要重新渲染
  const shouldNotRerender =
    prevProps.prompt === nextProps.prompt &&
    prevProps.characterAvatar === nextProps.characterAvatar &&
    prevProps.messageId === nextProps.messageId &&
    prevProps.chatId === nextProps.chatId &&
    JSON.stringify(prevProps.existingAttachments) === JSON.stringify(nextProps.existingAttachments)

  return shouldNotRerender
})
