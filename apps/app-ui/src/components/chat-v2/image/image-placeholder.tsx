import { memo } from 'react'
import { useTranslation } from 'react-i18next'
import { cn } from '@/lib/utils'

interface ImagePlaceholderProps {
  status: 'pending' | 'processing' | 'failed'
  progress?: number
  estimatedSteps?: number
  completedSteps?: number
  error?: string | null
  message?: string
  onRetry?: () => void
  onUpgrade?: () => void // 新增：升级按钮回调
  showUpgradeButton?: boolean // 新增：是否显示升级按钮
}

const PureImagePlaceholder = ({
  status,
  progress = 0,
  estimatedSteps = 0,
  completedSteps = 0,
  error,
  message,
  onRetry,
  onUpgrade,
  showUpgradeButton = false
}: ImagePlaceholderProps) => {
  const { t } = useTranslation('chat-v2')
  // 计算预估剩余时间（假设每步需要1秒）
  const remainingSteps = Math.max(0, estimatedSteps - completedSteps)
  const estimatedTimeLeft = remainingSteps * 1 // 秒

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return t('image.time_seconds', { seconds })
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return t('image.time_minutes_seconds', { minutes, seconds: remainingSeconds })
  }

  if (status === 'failed') {
    return (
      <div className="relative w-full max-w-xs mx-auto">
        <div className="aspect-[3/4] bg-red-50 dark:bg-red-900/20 border-2 border-red-200 dark:border-red-800 rounded-lg flex flex-col items-center justify-center p-4">
          <div className="text-red-500 dark:text-red-400 text-center mb-4">
            <svg
              className="w-12 h-12 mx-auto mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <p className="text-sm font-medium">{t('image.generation_failed')}</p>
            {error && <p className="text-xs mt-1 opacity-75">{error}</p>}
          </div>
          <div className="flex flex-col gap-2 w-full">
            {showUpgradeButton && onUpgrade ? (
              // 权限错误时，只显示升级按钮
              <button
                onClick={onUpgrade}
                className="px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white text-sm rounded-md transition-all duration-200 transform hover:scale-105"
              >
                {t('image.upgrade_unlock')}
              </button>
            ) : (
              // 其他错误时，显示重试按钮
              onRetry && (
                <button
                  onClick={onRetry}
                  className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white text-sm rounded-md transition-colors"
                >
                  {t('image.retry')}
                </button>
              )
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="relative w-full max-w-xs mx-auto">
      <div className="aspect-[3/4] bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border-2 border-purple-200 dark:border-purple-800 rounded-lg overflow-hidden relative">
        {/* 骨架屏动画背景 */}
        <div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"
          style={{
            background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
            animation: 'shimmer 2s infinite linear'
          }}
        />

        {/* 内容区域 */}
        <div className="relative h-full flex flex-col items-center justify-center p-6">
          {/* 图标 */}
          <div className="mb-4">
            <svg
              className="w-16 h-16 text-purple-400 dark:text-purple-300 animate-pulse"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>

          {/* 进度消息 */}
          {message && (
            <div className="mb-4 text-center">
              <p className="text-xs text-purple-600 dark:text-purple-300 font-medium">{message}</p>
            </div>
          )}

          {/* 进度条 */}
          {status === 'processing' && progress > 0 && (
            <div className="w-full max-w-48 mb-4">
              <div className="w-full bg-purple-200 dark:bg-purple-800 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${Math.min(progress, 100)}%` }}
                />
              </div>
              <div className="flex justify-between text-xs text-purple-600 dark:text-purple-300 mt-1">
                <span>{progress}%</span>
                {estimatedSteps > 0 && (
                  <span>
                    {completedSteps}/{estimatedSteps}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* 加载动画点 */}
          <div className="flex space-x-2">
            {[0, 1, 2].map(i => (
              <div
                key={i}
                className={cn(
                  'w-3 h-3 bg-gradient-to-r from-purple-400 to-blue-400 dark:from-purple-300 dark:to-blue-300 rounded-full animate-bounce'
                )}
                style={{
                  animationDelay: `${i * 0.15}s`,
                  animationDuration: '0.8s'
                }}
              />
            ))}
          </div>
        </div>
      </div>

      {/* 添加shimmer动画的CSS */}
      <style>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }
      `}</style>
    </div>
  )
}

export const ImagePlaceholder = memo(PureImagePlaceholder)
