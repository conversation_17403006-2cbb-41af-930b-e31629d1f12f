import { useState, useEffect, useRef } from 'react'
import { Button } from '@heroui/react'
import { useMediaSourceAudio } from '@/hooks/use-media-source-audio'
import { useRealStreamAudio } from '@/hooks/use-real-stream-audio'
import { EnhancedPermissionGuard } from '@/components/permission/EnhancedPermissionGuard'
import { useGlobalAudioStore } from '@/stores/global-audio-store'
import { AudioStorage } from '@/lib/media/audio-storage'
import { AudioLocalizationService } from './audio/audio-localization.service'
import { getGlobalChatDatabase } from '@/lib/chat-database'

interface EnhancedStreamAudioPlayerProps {
  text: string
  messageId?: string
  chatId?: string
  existingAttachments?: Array<{
    url: string
    name: string
    contentType: string
  }>
  className?: string
}

export const EnhancedStreamAudioPlayer = ({
  text,
  messageId,
  chatId,
  existingAttachments,
  className
}: EnhancedStreamAudioPlayerProps) => {
  // MediaSource 播放器（优先使用）
  const mediaSourcePlayer = useMediaSourceAudio()

  // 降级播放器（Blob URL）
  const fallbackPlayer = useRealStreamAudio()

  // 当前使用的播放器
  const [useMediaSource, setUseMediaSource] = useState(true)
  const [hasExistingAudio, setHasExistingAudio] = useState(false)
  const [existingAudioUrl, setExistingAudioUrl] = useState<string | null>(null)
  const [isGenerating, setIsGenerating] = useState(false) // 正在生成状态

  // 🔧 添加防重复生成的保护机制
  const isGeneratingRef = useRef(false)
  const generationPromiseRef = useRef<Promise<any> | null>(null)
  const hasInitializedRef = useRef<string | null>(null) // 🔧 改为string类型，存储初始化的唯一标识

  // 全局音频管理
  const globalAudio = useGlobalAudioStore()

  // 音频存储管理器
  const audioStorage = AudioStorage.getInstance()
  // 🔧 音频本地化服务，防重复下载
  const audioLocalizationService = AudioLocalizationService.getInstance()

  // 聊天数据库实例
  const chatDatabase = getGlobalChatDatabase()

  // 本地音频文件路径状态
  const [localAudioPath, setLocalAudioPath] = useState<string | null>(null)

  // 生成唯一的播放器ID
  const playerId = `${messageId || 'unknown'}_${text.substring(0, 20)}`

  // 检查当前是否在播放
  const isCurrentlyPlaying = globalAudio.isCurrentPlaying(playerId)

  // 选择当前播放器
  const currentPlayer = useMediaSource ? mediaSourcePlayer : fallbackPlayer

  // 🔧 检查是否已有本地音频文件（使用本地化服务）
  const checkLocalAudio = async (): Promise<string | null> => {
    if (!messageId) return null

    try {
      // 🔧 使用音频本地化服务检查本地音频
      const localAudioUrl = await audioLocalizationService.checkLocalAudio(messageId)
      if (localAudioUrl && localAudioUrl.startsWith('data:')) {
        // 如果返回的是data URL，获取实际文件路径
        const localPath = await audioStorage.findAudioByMessageId(messageId)
        return localPath
      }
      return localAudioUrl
    } catch (error) {
      console.warn('⚠️ 检查本地音频失败:', error)
      return null
    }
  }

  // 🔧 保存音频到本地并更新数据库（使用本地化服务防重复）
  const saveAudioToLocal = async (
    audioData: Uint8Array | string,
    isUrl: boolean = false
  ): Promise<string | null> => {
    if (!messageId) return null

    try {
      const textHash = text
        .substring(0, 50)
        .split('')
        .reduce((hash, char) => {
          return ((hash << 5) - hash + char.charCodeAt(0)) & 0xffffffff
        }, 0)

      // 🔧 使用音频本地化服务，防重复下载
      const localUrl = await audioLocalizationService.localizeGeneratedAudio(
        audioData,
        messageId,
        Math.abs(textHash).toString(),
        isUrl
      )

      if (localUrl && localUrl.startsWith('data:')) {
        // 如果返回的是data URL，提取本地路径用于状态管理
        // 这里我们需要从本地化服务获取实际的文件路径
        const localPath = await audioStorage.findAudioByMessageId(messageId)
        return localPath
      }

      return localUrl
    } catch (error) {
      console.error('❌ 保存音频到本地失败:', error)
      return null
    }
  }

  // 检查 MediaSource 支持并设置播放器
  useEffect(() => {
    const supported = mediaSourcePlayer.isMediaSourceSupported()
    if (!supported) {
      console.log('⚠️ 浏览器不支持 MediaSource，降级到 Blob URL 播放')
      setUseMediaSource(false)
    }
  }, [mediaSourcePlayer])

  // 🔧 修复：检查是否已有音频附件（包括本地缓存）
  useEffect(() => {
    const checkAudioAttachments = async () => {
      // 🔧 使用messageId作为唯一标识，确保每个消息独立初始化
      const initKey = `${messageId}_${text.substring(0, 20)}`

      // 🔧 防重复执行保护 - 但要确保每个消息独立
      if (hasInitializedRef.current === initKey) {
        console.log('🔧 当前消息已初始化，跳过重复检查')
        return
      }

      // 🔧 如果正在生成，不要更新状态，避免干扰
      if (isGeneratingRef.current) {
        console.log('🔧 音频正在生成中，跳过状态检查')
        return
      }

      // 🔧 移除isCurrentlyPlaying的检查，避免播放状态变化时重复触发
      console.log('🔍 [INIT] 开始检查音频附件状态，messageId:', messageId)

      try {
        // 首先检查本地音频（最高优先级）
        const localPath = await checkLocalAudio()
        if (localPath) {
          console.log('✅ 发现本地音频文件，直接使用')
          setLocalAudioPath(localPath)
          setExistingAudioUrl(await audioStorage.getAudioUrl(localPath))
          setHasExistingAudio(true)
          hasInitializedRef.current = initKey // 🔧 使用唯一标识
          return
        }

        // 如果没有本地文件，检查服务器附件
        let serverAudioUrl: string | null = null
        if (existingAttachments && existingAttachments.length > 0) {
          const audioAttachment = existingAttachments.find(attachment =>
            attachment.contentType.startsWith('audio/')
          )
          if (audioAttachment) {
            serverAudioUrl = audioAttachment.url
          }
        }

        // 使用服务器附件或清空状态
        if (serverAudioUrl) {
          console.log('✅ 发现服务器音频附件，使用远程URL')
          setExistingAudioUrl(serverAudioUrl)
          setHasExistingAudio(true)
          setLocalAudioPath(null)
        } else {
          console.log('ℹ️ 没有发现现有音频，等待生成')
          setHasExistingAudio(false)
          setExistingAudioUrl(null)
          setLocalAudioPath(null)
        }

        hasInitializedRef.current = initKey // 🔧 使用唯一标识
      } catch (error) {
        console.error('❌ 检查音频附件失败:', error)
        hasInitializedRef.current = initKey // 🔧 即使失败也要标记已初始化
      }
    }

    checkAudioAttachments()
  }, [existingAttachments, text, messageId]) // 🔧 移除 isCurrentlyPlaying 依赖，避免播放状态变化时重复触发

  // 🔧 组件卸载时清理状态
  useEffect(() => {
    return () => {
      // 清理当前组件的初始化状态
      if (hasInitializedRef.current) {
        console.log('🔧 组件卸载，清理初始化状态')
        hasInitializedRef.current = null
      }

      // 清理生成状态
      isGeneratingRef.current = false
      generationPromiseRef.current = null
    }
  }, [])

  // 播放现有音频
  const playExistingAudio = async () => {
    if (!existingAudioUrl) {
      console.warn('⚠️ 没有现有音频URL')
      return
    }

    try {
      const audio = new Audio()

      // 设置事件监听器
      const handleError = (e: Event) => {
        console.error('🎵 现有音频播放失败:', e)

        // 清理失效的本地文件记录
        if (localAudioPath) {
          setLocalAudioPath(null)
        }

        // 重置状态
        setHasExistingAudio(false)
        setExistingAudioUrl(null)

        // 清理全局状态
        globalAudio.clearCurrent()
      }

      const handleCanPlay = () => {
        // 音频准备就绪
      }

      // 🔧 移除 handlePlay 中的状态更新，改为在播放开始后异步处理下载
      const handleCanPlayThrough = async () => {
        // 检查是否为远程URL且没有本地文件，在音频准备就绪后开始后台下载
        const isRemoteUrl = existingAudioUrl.startsWith('http')
        if (isRemoteUrl && !localAudioPath && messageId) {
          // 🔧 使用 setTimeout 异步处理，避免在播放事件中更新状态
          setTimeout(async () => {
            try {
              // 检查是否已经在处理下载，防止重复
              if (audio.dataset.downloading === 'true') {
                return
              }

              // 标记正在下载
              audio.dataset.downloading = 'true'

              console.log('🎵 开始后台下载音频到本地')
              // 后台下载并保存音频
              const savedPath = await saveAudioToLocal(existingAudioUrl, true)
              if (savedPath) {
                console.log('🎵 音频下载完成，下次播放将使用本地文件')
                // 🔧 延迟更新状态，避免触发重新渲染
                setTimeout(() => {
                  setLocalAudioPath(savedPath)
                }, 1000)
              }
            } catch (downloadError) {
              console.warn('⚠️ 后台下载音频失败:', downloadError)
            } finally {
              // 清除下载标记
              audio.dataset.downloading = 'false'
            }
          }, 100)
        }
      }

      audio.addEventListener('error', handleError)
      audio.addEventListener('canplay', handleCanPlay)
      audio.addEventListener('canplaythrough', handleCanPlayThrough)

      // 设置音频源
      audio.src = existingAudioUrl

      // 注册到全局音频管理器
      globalAudio.setCurrentPlaying(playerId, audio)

      // 尝试播放
      await audio.play()
    } catch (error) {
      console.error('🎵 播放现有音频异常:', error)

      // 清理失效的本地文件记录
      if (localAudioPath) {
        setLocalAudioPath(null)
      }

      // 重置状态
      setHasExistingAudio(false)
      setExistingAudioUrl(null)

      // 清理全局状态
      globalAudio.clearCurrent()
    }
  }

  // 权限守卫 - 使用EnhancedPermissionGuard处理升级逻辑

  // 🔧 修复：生成新音频（添加防重复保护）- 不包含权限检查，由EnhancedPermissionGuard处理
  const handleGenerateNewAudio = async () => {
    // 🔧 多重防护检查
    if (isGeneratingRef.current || isGenerating) {
      console.log('🔧 音频正在生成中，跳过重复请求')
      return generationPromiseRef.current
    }

    if (hasExistingAudio) {
      console.log('🔧 已有现有音频，跳过生成')
      return
    }

    // 🔧 设置生成标识
    isGeneratingRef.current = true
    setIsGenerating(true)

    console.log('🎵 开始生成新音频')

    // 🔧 创建生成Promise并存储引用
    const generationPromise = (async () => {
      try {
        // 直接执行音频生成逻辑，权限检查由EnhancedPermissionGuard处理
        // 🔧 优先尝试MediaSource，失败时立即降级（不等待播放）
        let finalResult = null
        let usedMediaSource = false

        try {
          if (useMediaSource) {
            console.log('🎵 尝试使用 MediaSource 播放器')
            usedMediaSource = true
            finalResult = await currentPlayer.generateAndPlayStream(text, messageId, chatId)
          } else {
            console.log('🎵 使用降级播放器（Blob URL）')
            finalResult = await currentPlayer.generateAndPlayStream(text, messageId, chatId)
          }
        } catch (error) {
          // 🔧 只有在MediaSource生成阶段失败时才降级，播放阶段的错误不触发降级
          if (usedMediaSource && !finalResult) {
            console.warn('⚠️ MediaSource 生成失败，立即降级到 Blob URL:', error)
            setUseMediaSource(false)

            // 🔧 确保清理MediaSource播放器状态
            mediaSourcePlayer.reset()

            try {
              finalResult = await fallbackPlayer.generateAndPlayStream(text, messageId, chatId)
              console.log('✅ 降级播放器生成成功')
            } catch (fallbackError) {
              console.error('❌ 降级播放器也失败:', fallbackError)
              throw fallbackError
            }
          } else {
            // 非MediaSource错误或已有结果的情况，直接抛出
            throw error
          }
        }

        // 🔧 统一处理播放结果
        if (finalResult) {
          console.log('🎵 音频生成成功，处理播放结果')

          // 处理不同类型的返回结果
          if (typeof finalResult === 'object' && 'audioElement' in finalResult) {
            // MediaSource 播放器返回 { audioElement, audioChunks }
            const { audioElement, audioChunks } = finalResult
            if (audioElement && audioElement.src) {
              console.log('🎵 注册 MediaSource 音频到全局管理器')
              globalAudio.setCurrentPlaying(playerId, audioElement)

              // 🔧 延迟保存音频，避免在播放过程中更新状态
              setTimeout(async () => {
                try {
                  if (audioChunks && audioChunks.length > 0) {
                    // 合并所有音频块
                    const totalLength = audioChunks.reduce((acc, chunk) => acc + chunk.length, 0)
                    const audioData = new Uint8Array(totalLength)
                    let offset = 0

                    for (const chunk of audioChunks) {
                      audioData.set(chunk, offset)
                      offset += chunk.length
                    }

                    const localPath = await saveAudioToLocal(audioData)
                    if (localPath) {
                      setLocalAudioPath(localPath)
                      const localUrl = await audioStorage.getAudioUrl(localPath)
                      setExistingAudioUrl(localUrl)
                      setHasExistingAudio(true)
                    }
                  } else {
                    // 从音频元素的src保存
                    const localPath = await saveAudioToLocal(audioElement.src, true)
                    if (localPath) {
                      setLocalAudioPath(localPath)
                      const localUrl = await audioStorage.getAudioUrl(localPath)
                      setExistingAudioUrl(localUrl)
                      setHasExistingAudio(true)
                    }
                  }
                } catch (saveError) {
                  console.warn('⚠️ 延迟保存音频失败:', saveError)
                }
              }, 3000) // 延迟3秒保存，等播放稳定
            }
          } else if (finalResult && 'src' in finalResult && finalResult.src) {
            // 普通音频元素（降级播放器或MediaSource普通返回）
            console.log('🎵 注册普通音频元素到全局管理器')
            globalAudio.setCurrentPlaying(playerId, finalResult)

            // 🔧 延迟保存音频
            setTimeout(async () => {
              try {
                const localPath = await saveAudioToLocal(finalResult.src, true)
                if (localPath) {
                  setLocalAudioPath(localPath)
                  const localUrl = await audioStorage.getAudioUrl(localPath)
                  setExistingAudioUrl(localUrl || finalResult.src)
                  setHasExistingAudio(true)
                } else {
                  setExistingAudioUrl(finalResult.src)
                  setHasExistingAudio(true)
                }
              } catch (saveError) {
                console.warn('⚠️ 延迟保存音频失败:', saveError)
              }
            }, 3000)
          } else {
            console.warn('⚠️ 未知的播放结果格式:', finalResult)
          }
        } else {
          throw new Error('音频生成失败，没有返回结果')
        }
      } catch (error) {
        console.error('生成新音频失败:', error)
        throw error
      }
    })()

    // 存储Promise引用
    generationPromiseRef.current = generationPromise

    try {
      await generationPromise
    } finally {
      // 🔧 确保清理生成状态
      isGeneratingRef.current = false
      setIsGenerating(false)
      generationPromiseRef.current = null
      console.log('🎵 音频生成流程完成')
    }

    return generationPromise
  }

  // 处理按钮点击
  const handleClick = async () => {
    // 🔧 如果正在生成，严格拒绝
    if (isGenerating || isGeneratingRef.current) {
      console.log('🔧 音频正在生成中，忽略点击')
      return
    }

    // 如果有现有音频，优先处理现有音频逻辑
    if (hasExistingAudio && existingAudioUrl) {
      // 如果正在播放现有音频，暂停
      if (isCurrentlyPlaying) {
        console.log('🎵 暂停当前播放的音频')
        globalAudio.pauseCurrent()
        return
      }

      // 检查是否有暂停的音频可以恢复
      if (
        globalAudio.currentAudio &&
        globalAudio.currentPlayingId === playerId &&
        !globalAudio.isPlaying
      ) {
        console.log('🎵 恢复暂停的音频')
        globalAudio.resumeCurrent()
        return
      }

      // 如果不在播放中，开始播放现有音频
      console.log('🎵 开始播放现有音频')
      await playExistingAudio()
      return
    }

    // 如果正在播放流式音频，暂停
    if (currentPlayer.state.status === 'playing') {
      currentPlayer.pause()
      return
    }

    // 如果正在连接，取消
    if (currentPlayer.state.status === 'connecting') {
      currentPlayer.cancel()
      return
    }

    // 如果在缓冲状态但有音频元素，尝试恢复播放
    if (currentPlayer.state.status === 'buffering') {
      // 检查是否是暂停状态（有音频元素且有缓冲数据）
      const canPlay =
        'canPlay' in currentPlayer.state
          ? currentPlayer.state.canPlay
          : currentPlayer.state.bufferProgress > 50
      if (currentPlayer.state.bufferProgress > 0 && canPlay) {
        currentPlayer.play()
        return
      } else {
        // 真正的初始缓冲状态，取消
        currentPlayer.cancel()
        return
      }
    }

    // 如果是暂停状态，恢复播放
    if (currentPlayer.state.status === 'paused') {
      currentPlayer.play()
      return
    }

    // 生成新音频 - 这里会被EnhancedPermissionGuard处理权限检查
    await handleGenerateNewAudio()
  }

  // 处理需要权限检查的生成音频操作
  const handleGenerateWithPermission = async (executeWithPermission: any) => {
    const result = await executeWithPermission(() => handleGenerateNewAudio())
    if (!result.success) {
      console.log('🚫 权限检查失败，停止音频生成')
    }
  }

  // 重试
  const handleRetry = async () => {
    // 🔧 重试前清理所有状态
    isGeneratingRef.current = false
    generationPromiseRef.current = null
    setIsGenerating(false)

    // 🔧 重置初始化状态，允许重新检查
    hasInitializedRef.current = null

    currentPlayer.reset()
    globalAudio.clearCurrent()

    await handleGenerateNewAudio()
  }

  // 获取按钮状态
  const getButtonState = () => {
    // 如果正在生成音频
    if (isGenerating || isGeneratingRef.current) {
      return {
        icon: 'solar:play-circle-linear',
        loading: true,
        disabled: false,
        color: 'primary' as const,
        tooltip: '正在生成音频...'
      }
    }

    // 如果正在播放现有音频
    if (isCurrentlyPlaying) {
      return {
        icon: 'solar:pause-circle-linear',
        loading: false,
        disabled: false,
        color: 'success' as const,
        tooltip: '暂停播放（现有音频）'
      }
    }

    // 如果有现有音频且处于可播放状态
    if (
      hasExistingAudio &&
      (currentPlayer.state.status === 'idle' ||
        currentPlayer.state.status === 'completed' ||
        currentPlayer.state.status === 'failed')
    ) {
      return {
        icon: 'solar:play-circle-linear',
        tooltip: '播放语音（使用已生成）',
        loading: false,
        disabled: false,
        color: 'success' as const
      }
    }

    // 流式播放状态
    switch (currentPlayer.state.status) {
      case 'connecting':
        return {
          icon: 'solar:play-circle-linear',
          loading: true,
          disabled: false,
          color: 'primary' as const,
          tooltip: '正在连接...'
        }

      case 'buffering': {
        const canPlay =
          'canPlay' in currentPlayer.state
            ? currentPlayer.state.canPlay
            : currentPlayer.state.bufferProgress > 50
        return {
          icon: canPlay ? 'solar:pause-circle-linear' : 'solar:play-circle-linear',
          loading: !canPlay,
          disabled: false,
          color: 'primary' as const,
          tooltip: `缓冲中 ${Math.round(currentPlayer.state.bufferProgress)}%`
        }
      }

      case 'playing':
        return {
          icon: 'solar:pause-circle-linear',
          loading: false,
          disabled: false,
          color: 'warning' as const,
          tooltip: `播放中 ${Math.round(currentPlayer.state.playbackProgress)}%`
        }

      case 'paused':
        return {
          icon: 'solar:play-circle-linear',
          loading: false,
          disabled: false,
          color: 'warning' as const,
          tooltip: '继续播放'
        }

      case 'completed':
        return {
          icon: 'solar:play-circle-linear',
          loading: false,
          disabled: false,
          color: 'success' as const,
          tooltip: '重新播放'
        }

      case 'failed':
        return {
          icon: 'solar:refresh-linear',
          loading: false,
          disabled: false,
          color: 'danger' as const,
          tooltip: '重试'
        }

      default:
        return {
          icon: 'solar:play-circle-linear',
          loading: false,
          disabled: false,
          color: 'primary' as const,
          tooltip: useMediaSource ? '流式播放（MediaSource）' : '流式播放（降级模式）'
        }
    }
  }

  const buttonState = getButtonState()

  // 判断是否需要权限检查：只有在生成新音频时才需要
  const needsPermissionCheck = () => {
    // 如果正在生成，不需要权限检查
    if (isGenerating || isGeneratingRef.current) return false

    // 如果有现有音频，不需要权限检查
    if (hasExistingAudio && existingAudioUrl) return false

    // 如果是流式播放的其他状态，不需要权限检查
    if (
      currentPlayer.state.status === 'playing' ||
      currentPlayer.state.status === 'connecting' ||
      currentPlayer.state.status === 'buffering' ||
      currentPlayer.state.status === 'paused'
    )
      return false

    // 只有在需要生成新音频时才需要权限检查
    return true
  }

  // 处理按钮点击 - 区分是否需要权限检查
  const handleButtonClick = async (executeWithPermission?: any) => {
    if (currentPlayer.state.status === 'failed') {
      await handleRetry()
      return
    }

    if (needsPermissionCheck() && executeWithPermission) {
      // 需要权限检查的情况：生成新音频
      await handleGenerateWithPermission(executeWithPermission)
    } else {
      // 不需要权限检查的情况：播放现有音频、控制播放状态等
      await handleClick()
    }
  }

  return (
    <div className="flex items-center shrink-0">
      {needsPermissionCheck() ? (
        // 需要权限检查时，使用EnhancedPermissionGuard包装
        <EnhancedPermissionGuard
          feature="voice_generation"
          uiStrategy="upgrade-modal"
          pointsCost={5} // TTS语音生成的积分消耗
          showLoadingOverlay={false} // 不显示全屏loading，使用按钮内的loading
        >
          {({ executeWithPermission }) => (
            <Button
              isIconOnly
              size="md"
              variant="flat"
              onPress={() => handleButtonClick(executeWithPermission)}
              isLoading={buttonState.loading}
              isDisabled={buttonState.disabled}
              className={`${className} bg-transparent`}
              title={buttonState.tooltip}
            >
              {!buttonState.loading && (
                <img src="/images/voice.svg" className="w-10 h-10 text-white" />
              )}
            </Button>
          )}
        </EnhancedPermissionGuard>
      ) : (
        // 不需要权限检查时，直接渲染按钮
        <Button
          isIconOnly
          size="md"
          variant="flat"
          onPress={() => handleButtonClick()}
          isLoading={buttonState.loading}
          isDisabled={buttonState.disabled}
          className={`${className} bg-transparent`}
          title={buttonState.tooltip}
        >
          {!buttonState.loading && <img src="/images/voice.svg" className="w-10 h-10 text-white" />}
        </Button>
      )}

      {/* 🔧 开发调试信息 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="ml-2 text-xs text-gray-500">
          <div>状态: {currentPlayer.state.status}</div>
          <div>生成中: {isGenerating ? '是' : '否'}</div>
          <div>生成Ref: {isGeneratingRef.current ? '是' : '否'}</div>
          <div>现有音频: {hasExistingAudio ? '有' : '无'}</div>
          <div>播放中: {isCurrentlyPlaying ? '是' : '否'}</div>
          <div>需要权限: {needsPermissionCheck() ? '是' : '否'}</div>
        </div>
      )}
    </div>
  )
}
