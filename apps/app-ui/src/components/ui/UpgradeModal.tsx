import React from 'react'
import { Modal, ModalContent, ModalBody, Button } from '@heroui/react'
import { Icon } from '@iconify/react'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'

interface UpgradeModalProps {
  /** 是否显示弹窗 */
  isOpen: boolean
  /** 关闭弹窗回调 */
  onClose: () => void
  /** 升级会员回调 */
  onUpgrade: () => void
  /** 购买积分回调 */
  onPurchasePoints: () => void
  /** 需要的积分数 */
  requiredPoints: number
  /** 当前可用积分数 */
  availablePoints: number
  /** 剩余积分数 */
  remainingPoints: number
  /** 用户类型 */
  userType?: string
  /** 是否是会员 */
  isMember?: boolean
  /** 当前会员等级 */
  membershipLevel?: string
  /** 错误码 */
  errorCode?: string
}

/**
 * 升级引导弹窗组件
 * 从底部弹出，引导用户升级会员获取更多积分
 *
 * 业务逻辑说明：
 * - 积分是和会员挂钩的，只有会员才有积分
 * - 免费用户不应该显示积分购买选项，只能升级会员
 * - 如果用户已经是最高等级会员，不显示升级会员按钮
 * - 会员用户积分不足时，可以选择购买积分或升级到更高等级
 */
export const UpgradeModal: React.FC<UpgradeModalProps> = ({
  isOpen,
  onClose,
  onUpgrade,
  onPurchasePoints,
  requiredPoints,
  availablePoints,
  remainingPoints,
  userType = 'free',
  isMember = false,
  membershipLevel,
  errorCode
}) => {
  const { t } = useTranslation('interactive')

  // 判断是否是最高等级会员（这里需要根据实际业务逻辑调整）
  const isHighestLevel = membershipLevel === 'premium' || membershipLevel === 'ultimate'

  // 判断是否应该显示积分购买按钮（只有会员才能购买积分）
  const shouldShowPointsPurchase = isMember && errorCode === 'INSUFFICIENT_POINTS'

  // 判断是否应该显示升级会员按钮
  const shouldShowUpgrade = !isHighestLevel

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={onClose}
      placement="bottom"
      hideCloseButton
      classNames={{
        base: 'bg-transparent shadow-none',
        wrapper: 'bg-transparent justify-end items-end',
        backdrop: 'bg-black/60 backdrop-blur-sm'
      }}
      motionProps={{
        variants: {
          enter: {
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.3,
              ease: 'easeOut'
            }
          },
          exit: {
            y: '100%',
            opacity: 0,
            transition: {
              duration: 0.2,
              ease: 'easeIn'
            }
          }
        }
      }}
    >
      <ModalContent className="bg-transparent shadow-none m-0 rounded-t-3xl rounded-b-none max-h-[70vh] overflow-hidden w-full">
        <div className="relative">
          {/* 主要内容容器 */}
          <div className="relative z-10 backdrop-blur-xl rounded-t-3xl overflow-hidden bg-background">
            {/* 装饰背景 */}
            <div className="absolute top-0 left-0 size-full pointer-events-none z-1">
              <img
                src="/images/decorate/decorate-9.svg"
                alt=""
                className="w-full h-full opacity-80"
              />
            </div>

            {/* 顶部拖拽指示器 */}
            <div className="relative z-10 flex justify-center pt-4 pb-2">
              <div className="w-10 h-1 bg-white/30 rounded-full" />
            </div>

            <ModalBody className="relative z-10 px-6 pb-8">
              <div className="text-center">
                {/* 图标 */}
                <motion.div
                  className="w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-[#ff2d97] to-[#892fff] flex items-center justify-center"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
                >
                  <Icon icon="solar:star-bold" width={40} className="text-white" />
                </motion.div>

                {/* 标题 */}
                <motion.h3
                  className="text-white text-xl font-bold mb-2"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  {t('upgradeModal.title')}
                </motion.h3>

                {/* 副标题 */}
                <motion.p
                  className="text-gray-400 text-sm mb-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  {t('upgradeModal.subtitle')}
                </motion.p>

                {/* 积分对比卡片 */}
                <motion.div
                  className="bg-white/5 rounded-2xl p-4 mb-6 border border-white/10"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className="grid grid-cols-2 gap-4">
                    {/* 当前积分 */}
                    <div className="text-center">
                      <div className="text-gray-400 text-xs mb-1">
                        {t('upgradeModal.currentPoints')}
                      </div>
                      <div className="text-white text-2xl font-bold">{availablePoints}</div>
                      <div className="text-gray-500 text-xs">{t('upgradeModal.pointsUnit')}</div>
                    </div>

                    {/* 需要积分 */}
                    <div className="text-center">
                      <div className="text-gray-400 text-xs mb-1">
                        {t('upgradeModal.requiredPoints')}
                      </div>
                      <div className="text-[#ff2d97] text-2xl font-bold">{requiredPoints}</div>
                      <div className="text-gray-500 text-xs">{t('upgradeModal.pointsUnit')}</div>
                    </div>
                  </div>

                  {/* 差额提示 */}
                  <div className="mt-4 pt-4 border-t border-white/10">
                    <div className="text-center">
                      <span className="text-gray-400 text-sm">{t('upgradeModal.shortfall')}：</span>
                      <span className="text-[#ff2d97] font-semibold ml-1">
                        {requiredPoints - availablePoints} {t('upgradeModal.pointsUnit')}
                      </span>
                    </div>
                  </div>
                </motion.div>

                {/* 按钮组 */}
                <motion.div
                  className="space-y-3"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  {/* 升级会员按钮 - 只有非最高等级才显示 */}
                  {shouldShowUpgrade && (
                    <Button
                      onPress={onUpgrade}
                      className="w-full text-white font-medium transition-all bg-button-primary h-12 rounded-2xl"
                    >
                      <Icon icon="solar:crown-bold" width={20} />
                      {isMember
                        ? t('upgradeModal.upgradeToHigherButton')
                        : t('upgradeModal.upgradeButton')}
                    </Button>
                  )}

                  {/* 购买积分按钮 - 只有会员且积分不足时才显示 */}
                  {shouldShowPointsPurchase && (
                    <Button
                      variant="bordered"
                      onPress={onPurchasePoints}
                      className="w-full border-white/20 text-white hover:bg-white/10 transition-colors h-12 rounded-2xl"
                    >
                      <Icon icon="solar:wallet-bold" width={20} />
                      {t('upgradeModal.purchasePointsButton')}
                    </Button>
                  )}

                  {/* 如果是最高等级会员且积分不足，显示特殊提示 */}
                  {isHighestLevel && errorCode === 'INSUFFICIENT_POINTS' && (
                    <div className="text-center p-4 bg-white/5 rounded-2xl border border-white/10">
                      <Icon
                        icon="solar:crown-bold"
                        width={24}
                        className="text-yellow-500 mx-auto mb-2"
                      />
                      <p className="text-white text-sm font-medium mb-1">
                        {t('upgradeModal.alreadyHighestLevel')}
                      </p>
                      <p className="text-gray-400 text-xs">
                        {t('upgradeModal.contactSupportForMorePoints')}
                      </p>
                    </div>
                  )}

                  {/* 稍后再说按钮 */}
                  <Button
                    variant="light"
                    onPress={onClose}
                    className="w-full text-gray-400 hover:text-white transition-colors h-10"
                  >
                    {t('upgradeModal.laterButton')}
                  </Button>
                </motion.div>
              </div>
            </ModalBody>
          </div>
        </div>
      </ModalContent>
    </Modal>
  )
}
