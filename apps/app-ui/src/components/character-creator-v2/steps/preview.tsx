import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react'
import { useNavigate } from 'react-router'
import { addToast } from '@heroui/react'
import type { CharacterDataV2 } from '../index'
import {
  relationshipOptions,
  ageOptions,
  ethnicityOptions,
  eyeColorOptions,
  hairStyleOptions,
  hairColorOptions,
  faceShapeOptions,
  bodyTypeOptions,
  breastSizeOptions,
  buttSizeOptions,
  femalePersonalityOptions,
  malePersonalityOptions,
  clothingOptions
} from '../mapping'
import { useTranslation } from 'react-i18next'
import {
  generateCharacterImage,
  completeGeneration,
  initialGenerationState,
  type GenerationState
} from '../services/generation-service'
import { CharacterBirthAnimation } from '../components/character-birth-animation'
import { Icon } from '@iconify/react'
import { getTranslatedLabel } from '../mapping-utils'

interface PreviewProps {
  data: CharacterDataV2
}

export interface PreviewRef {
  handleGenerate: () => Promise<void>
  isGenerating: boolean
  missingFields: string[]
}

// 使用工具函数简化显示标签获取
const getDisplayLabel = (key: string, value: string, gender: string, t: any): string => {
  const fallbackOption = (() => {
    switch (key) {
      case 'relationship':
        return relationshipOptions.find(option => option.value === value)?.label
      case 'age':
        return ageOptions.find(option => option.value === value)?.label
      case 'ethnicity':
        const currentEthnicityOptions =
          ethnicityOptions[gender as keyof typeof ethnicityOptions] || ethnicityOptions.female
        return currentEthnicityOptions.find(option => option.value === value)?.label
      case 'eyeColor':
        return eyeColorOptions.find(option => option.value === value)?.label
      case 'hairStyle':
        const currentHairStyleOptions =
          hairStyleOptions[gender as keyof typeof hairStyleOptions] || hairStyleOptions.female
        return currentHairStyleOptions.find(option => option.value === value)?.label
      case 'hairColor':
        return hairColorOptions.find(option => option.value === value)?.label
      case 'faceShape':
        const currentFaceShapeOptions =
          faceShapeOptions[gender as keyof typeof faceShapeOptions] || faceShapeOptions.female
        return currentFaceShapeOptions.find(option => option.value === value)?.label
      case 'bodyType':
        const currentBodyTypeOptions =
          bodyTypeOptions[gender as keyof typeof bodyTypeOptions] || bodyTypeOptions.female
        return currentBodyTypeOptions.find(option => option.value === value)?.label
      case 'breastSize':
        return breastSizeOptions.find(option => option.value === value)?.label
      case 'buttSize':
        return buttSizeOptions.find(option => option.value === value)?.label
      case 'personality':
        const personalityOptions =
          gender === 'male' ? malePersonalityOptions : femalePersonalityOptions
        return personalityOptions.find(option => option.value === value)?.label
      case 'clothing':
        const currentClothingOptions =
          clothingOptions[gender as keyof typeof clothingOptions] || clothingOptions.female
        return currentClothingOptions.find(option => option.value === value)?.label
      default:
        return value
    }
  })()

  return getTranslatedLabel(t, key, value, gender, fallbackOption)
}

const Preview = forwardRef<PreviewRef, PreviewProps>(({ data }, ref) => {
  const navigate = useNavigate()
  const { t } = useTranslation('character-creator-v2')

  // 生成状态管理
  const [generationState, setGenerationState] = useState<GenerationState>(initialGenerationState)

  // 更新生成状态的辅助函数
  const updateGenerationState = (updates: Partial<GenerationState>) => {
    setGenerationState(prev => ({ ...prev, ...updates }))
  }

  // 检查是否有缺失的必要信息
  const missingFields = [
    !data.name && t('steps.basic_info.name_placeholder'),
    !data.relationship && t('steps.basic_info.relationship_label'),
    !data.ethnicity && t('steps.appearance.ethnicity_label'),
    !data.age && t('steps.appearance.age_label'),
    !data.eyeColor && t('steps.appearance.eye_color_label'),
    !data.hairStyle && t('steps.body.hair_style_label'),
    !data.hairColor && t('steps.body.hair_color_label'),
    !data.faceShape && t('steps.appearance.face_shape_label'),
    !data.bodyType && t('steps.body.body_type_label'),
    !data.personality && t('steps.personality.personality_label'),
    !data.clothing && t('steps.personality.clothing_label'),
    !data.voiceModelId && t('steps.personality.voice_label')
  ].filter(Boolean)

  // 监听生成完成状态
  useEffect(() => {
    if (
      generationState.generatedImage &&
      generationState.isGenerationComplete &&
      generationState.generationKeywords &&
      generationState.generationPrompt
    ) {
      completeGeneration(
        data,
        generationState.generatedImage,
        generationState.generationKeywords,
        generationState.generationPrompt,
        () => {
          // 不自动跳转，让用户手动选择
        },
        updateGenerationState
      )
    }
  }, [
    generationState.generatedImage,
    generationState.isGenerationComplete,
    generationState.generationKeywords,
    generationState.generationPrompt,
    data,
    navigate
  ])

  // 生成角色图像
  const handleGenerate = async () => {
    // 检查是否有缺失字段
    if (missingFields.length > 0) {
      addToast({
        title: t('validation.complete_all_fields'),
        color: 'danger'
      })
      return
    }

    try {
      await generateCharacterImage(data, updateGenerationState)
    } catch (error) {
      console.error('生成失败', error)
    }
  }

  // 暴露生成函数给父组件
  useImperativeHandle(
    ref,
    () => ({
      handleGenerate,
      isGenerating: generationState.generating,
      missingFields: missingFields as string[]
    }),
    [handleGenerate, generationState.generating, missingFields]
  )

  const relationshipDisplay =
    data.relationship === 'other' && data.customRelationship
      ? data.customRelationship
      : getDisplayLabel('relationship', data.relationship, data.gender, t)

  const personalityDisplay =
    data.personality === 'custom' && data.customPersonality
      ? data.customPersonality
      : getDisplayLabel('personality', data.personality, data.gender, t)

  const clothingDisplay =
    data.clothing === 'custom' && data.customClothing
      ? data.customClothing
      : getDisplayLabel('clothing', data.clothing, data.gender, t)

  // 根据性别选择形象，如果有生成的图片则使用生成的图片
  const characterImage =
    generationState.generatedImage ||
    (data.gender === 'male' ? '/images/character/male.png' : '/images/character/female.png')

  return (
    <>
      {/* 显示角色诞生动画 */}
      <CharacterBirthAnimation
        isVisible={generationState.showLoadingScreen}
        characterName={data.name || '未命名角色'}
        characterGender={data.gender as 'male' | 'female'}
        generatedImage={generationState.generatedImage}
        characterId={generationState.createdCharacterId}
        onComplete={() => {
          // 动画完成后的回调
        }}
        onClose={() => {
          // 关闭动画，重置状态
          updateGenerationState({
            showLoadingScreen: false,
            generatedImage: null,
            createdCharacterId: null
          })
        }}
      />

      {/* 只有在不显示动画时才显示预览内容 */}
      {!generationState.showLoadingScreen && (
        <div className="bg-gradient-to-br from-[#1a1b2e] via-[#16213e] to-[#0f3460] relative mt-10 rounded-3xl">
          {/* 角色形象 */}
          <div className="w-48 h-auto absolute right-6 -top-12 z-10">
            <img
              src={characterImage}
              alt={t('steps.preview.character_image')}
              className="size-full object-contain"
              style={{ transform: 'scaleX(-1)' }}
            />
          </div>

          {/* 底部装饰渐变 */}
          <div className="absolute right-0 top-0 z-0">
            <img
              src="/images/character/decorate.svg"
              alt={t('common.decoration')}
              className="w-full h-auto opacity-80"
            />
          </div>

          <div className="relative z-10 flex flex-col items-center justify-between p-4 overflow-hidden rounded-3xl">
            {/* 左侧角色信息 */}
            <div className="flex-1 max-w-md space-y-6 w-full">
              {/* 角色基本信息 */}
              <div className="space-y-4">
                <div className="text-sm text-gray-400 font-medium">
                  {t('steps.preview.character_details')}
                </div>

                <div className="space-y-2">
                  <h1 className="text-4xl font-bold text-white flex items-center gap-3">
                    {data.name || t('steps.preview.unnamed_character')}
                    <span
                      className={`${
                        data.gender === 'male' ? 'bg-blue-500' : 'bg-pink-500'
                      } p-2 rounded-full`}
                    >
                      <Icon
                        icon={data.gender === 'male' ? 'solar:men-broken' : 'solar:women-broken'}
                        className="text-sm text-white"
                      />
                    </span>
                  </h1>
                  <p className="text-xl text-gray-300">
                    {getDisplayLabel('age', data.age, data.gender, t)}
                  </p>
                </div>
              </div>

              {/* 属性网格 */}
              <div className="grid grid-cols-3 gap-3 w-full">
                {/* 人种 */}
                <div className="bg-background/50 rounded-2xl px-4 py-3 border border-gray-700/50 backdrop-blur-sm">
                  <div className="text-xs text-gray-400 mb-2">
                    {t('steps.appearance.ethnicity_label')}
                  </div>
                  <div className="text-white  text-sm">
                    {getDisplayLabel('ethnicity', data.ethnicity, data.gender, t)}
                  </div>
                </div>

                {/* 瞳孔颜色 */}
                <div className="bg-background/50 rounded-2xl px-4 py-3 border border-gray-700/50 backdrop-blur-sm">
                  <div className="text-xs text-gray-400 mb-2">
                    {t('steps.appearance.eye_color_label')}
                  </div>
                  <div className="text-white  text-sm">
                    {getDisplayLabel('eyeColor', data.eyeColor, data.gender, t)}
                  </div>
                </div>

                {/* 发色 */}
                <div className="bg-background/50 rounded-2xl px-4 py-3 border border-gray-700/50 backdrop-blur-sm">
                  <div className="text-xs text-gray-400 mb-2">
                    {t('steps.body.hair_color_label')}
                  </div>
                  <div className="text-white  text-sm">
                    {getDisplayLabel('hairColor', data.hairColor, data.gender, t)}
                  </div>
                </div>

                {/* 体型 */}
                <div className="bg-background/50 rounded-2xl px-4 py-3 border border-gray-700/50 backdrop-blur-sm">
                  <div className="text-xs text-gray-400 mb-2">
                    {t('steps.body.body_type_label')}
                  </div>
                  <div className="text-white  text-sm">
                    {getDisplayLabel('bodyType', data.bodyType, data.gender, t)}
                  </div>
                </div>

                {/* 胸部尺寸（仅女性） */}
                {data.gender === 'female' && (
                  <div className="bg-background/50 rounded-2xl px-4 py-3 border border-gray-700/50 backdrop-blur-sm">
                    <div className="text-xs text-gray-400 mb-2">
                      {t('steps.body.breast_size_label')}
                    </div>
                    <div className="text-white  text-sm">
                      {getDisplayLabel('breastSize', data.breastSize || '', data.gender, t)}
                    </div>
                  </div>
                )}

                {/* 臀部尺寸（仅女性） */}
                {data.gender === 'female' && (
                  <div className="bg-background/50 rounded-2xl px-4 py-3 border border-gray-700/50 backdrop-blur-sm">
                    <div className="text-xs text-gray-400 mb-2">
                      {t('steps.body.butt_size_label')}
                    </div>
                    <div className="text-white  text-sm">
                      {getDisplayLabel('buttSize', data.buttSize || '', data.gender, t)}
                    </div>
                  </div>
                )}

                {/* 脸型 */}
                <div className="bg-background/50 rounded-2xl px-4 py-3 border border-gray-700/50 backdrop-blur-sm">
                  <div className="text-xs text-gray-400 mb-2">
                    {t('steps.appearance.face_shape_label')}
                  </div>
                  <div className="text-white  text-sm">
                    {getDisplayLabel('faceShape', data.faceShape, data.gender, t)}
                  </div>
                </div>

                {/* 性格 */}
                <div className="bg-background/50 rounded-2xl px-4 py-3 border border-gray-700/50 backdrop-blur-sm">
                  <div className="text-xs text-gray-400 mb-2">
                    {t('steps.personality.personality_label')}
                  </div>
                  <div className="text-white  text-sm">{personalityDisplay}</div>
                </div>

                {/* 服装 */}
                <div className="bg-background/50 rounded-2xl px-4 py-3 border border-gray-700/50 backdrop-blur-sm">
                  <div className="text-xs text-gray-400 mb-2">
                    {t('steps.personality.clothing_label')}
                  </div>
                  <div className="text-white  text-sm">{clothingDisplay}</div>
                </div>

                {/* 声音 */}
                <div className="bg-background/50 rounded-2xl px-4 py-3 border border-gray-700/50 backdrop-blur-sm">
                  <div className="text-xs text-gray-400 mb-2">
                    {t('steps.personality.voice_label')}
                  </div>
                  <div className="text-white  text-sm">
                    {data.voice || t('common.not_set', '未设置')}
                  </div>
                </div>

                {/* 关系 */}
                <div className="bg-background/50 rounded-2xl px-4 py-3 border border-gray-700/50 backdrop-blur-sm">
                  <div className="text-xs text-gray-400 mb-2">
                    {t('steps.basic_info.relationship_label')}
                  </div>
                  <div className="text-white  text-sm">{relationshipDisplay}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
})

Preview.displayName = 'Preview'

export default Preview
