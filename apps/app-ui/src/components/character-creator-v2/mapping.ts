import { useTranslation } from 'react-i18next'
import { CharacterType } from '@/lib/types'

// 关系选项 - 使用函数返回以支持多语言
export const getRelationshipOptions = () => {
  const { t } = useTranslation('character-creator-v2')
  return [
    { value: 'friend', label: t('relationships.friend'), icon: 'solar:users-group-rounded-bold' },
    { value: 'lover', label: t('relationships.lover'), icon: 'solar:heart-bold' },
    { value: 'mentor', label: t('relationships.mentor'), icon: 'solar:glasses-bold' },
    {
      value: 'colleague',
      label: t('relationships.colleague'),
      icon: 'solar:case-minimalistic-bold'
    },
    {
      value: 'classmate',
      label: t('relationships.classmate'),
      icon: 'solar:square-academic-cap-bold-duotone'
    },
    { value: 'other', label: t('relationships.other'), icon: 'solar:pen-bold' }
  ]
}

// 为向后兼容保留原始导出，但标记为已弃用
/** 请使用 getRelationshipOptions() 函数以支持多语言 */
export const relationshipOptions = [
  { value: 'friend', label: '朋友', icon: 'solar:users-group-rounded-bold' },
  { value: 'lover', label: '恋人', icon: 'solar:heart-bold' },
  { value: 'mentor', label: '导师', icon: 'solar:glasses-bold' },
  { value: 'colleague', label: '同事', icon: 'solar:case-minimalistic-bold' },
  { value: 'classmate', label: '同学', icon: 'solar:square-academic-cap-bold-duotone' },
  { value: 'other', label: '其他', icon: 'solar:pen-bold' }
]

// 年龄选项 - 使用函数返回以支持多语言
export const getAgeOptions = () => {
  const { t } = useTranslation('character-creator-v2')
  return [
    { value: 'teen', label: t('ages.teen'), range: '18+' },
    { value: '20s', label: t('ages.20s'), range: '20+' },
    { value: '30s', label: t('ages.30s'), range: '30+' },
    { value: '40-55', label: t('ages.40-55'), range: '40+' }
  ]
}

// 年龄选项
/** 请使用 getAgeOptions() 函数以支持多语言 */
export const ageOptions = [
  { value: 'teen', label: '18岁+', range: '18+' },
  { value: '20s', label: '20多岁', range: '20+' },
  { value: '30s', label: '30多岁', range: '30+' },
  { value: '40-55', label: '40-55岁', range: '40+' }
]

// 人种选项 - 使用函数返回以支持多语言
export const getEthnicityOptions = () => {
  const { t } = useTranslation('character-creator-v2')
  return {
    female: [
      { value: 'female-latina', label: t('ethnicities.female-latina') },
      { value: 'female-asian', label: t('ethnicities.female-asian') },
      { value: 'female-caucasian', label: t('ethnicities.female-caucasian') },
      { value: 'female-black', label: t('ethnicities.female-black') },
      { value: 'female-arab', label: t('ethnicities.female-arab') }
    ],
    male: [
      { value: 'male-caucasian', label: t('ethnicities.male-caucasian') },
      { value: 'male-asian', label: t('ethnicities.male-asian') },
      { value: 'male-black', label: t('ethnicities.male-black') },
      { value: 'male-latina', label: t('ethnicities.male-latina') },
      { value: 'male-arab', label: t('ethnicities.male-arab') }
    ]
  }
}

// 人种选项
/** @deprecated 请使用 getEthnicityOptions() 函数以支持多语言 */
export const ethnicityOptions = {
  female: [
    { value: 'female-latina', label: '拉丁裔' },
    { value: 'female-asian', label: '亚洲人' },
    { value: 'female-caucasian', label: '白种人' },
    { value: 'female-black', label: '黑人' },
    { value: 'female-arab', label: '阿拉伯人' }
  ],
  male: [
    { value: 'male-caucasian', label: '白种人' },
    { value: 'male-asian', label: '亚洲人' },
    { value: 'male-black', label: '黑人' },
    { value: 'male-latina', label: '拉丁裔' },
    { value: 'male-arab', label: '阿拉伯人' }
  ]
}

// 眼睛颜色选项
export const eyeColorOptions = [
  { value: 'brown', label: '棕色', color: '#8B4513' },
  { value: 'blue', label: '蓝色', color: '#4169E1' },
  { value: 'green', label: '绿色', color: '#228B22' },
  { value: 'purple', label: '紫色', color: '#8A2BE2' }
]

// 发型选项
export const hairStyleOptions = {
  female: [
    {
      value: 'middle-part-long',
      label: '自然长发',
      image: '/images/character/female-hairstyle/middle-part-long.png'
    },
    {
      value: 'straight-bangs',
      label: '齐刘海',
      image: '/images/character/female-hairstyle/straight-bangs.png'
    },
    {
      value: 'high-ponytail',
      label: '高马尾',
      image: '/images/character/female-hairstyle/high-ponytail.png'
    },
    {
      value: 'twin-tails-wavy',
      label: '双马尾',
      image: '/images/character/female-hairstyle/twin-tails-wavy.png'
    },
    {
      value: 'wavy-bob',
      label: '短波浪',
      image: '/images/character/female-hairstyle/wavy-bob.png'
    },
    {
      value: 'short-bob-bangs',
      label: '鲍勃头',
      image: '/images/character/female-hairstyle/short-bob-bangs.png'
    }
  ],
  male: [
    {
      value: 'textured-quiff',
      label: '蓬松短发',
      image: '/images/character/male-hairstyle/textured-quiff.png'
    },
    {
      value: 'long-wavy-beard',
      label: '长发胡须',
      image: '/images/character/male-hairstyle/long-wavy-beard.png'
    },
    {
      value: 'buzz-cut',
      label: '圆寸',
      image: '/images/character/male-hairstyle/buzz-cut.png'
    },
    { value: 'man-bun', label: '丸子头', image: '/images/character/male-hairstyle/man-bun.png' },
    {
      value: 'undercut-slick',
      label: '油头',
      image: '/images/character/male-hairstyle/undercut-slick.png'
    },
    {
      value: 'side-fade-beard',
      label: '侧推胡子',
      image: '/images/character/male-hairstyle/side-fade-beard.png'
    }
  ]
}

// 发色选项
export const hairColorOptions = [
  { value: 'black', label: '黑色', color: '#000000' },
  { value: 'brown', label: '棕色', color: '#8B4513' },
  { value: 'blonde', label: '金色', color: '#FFD700' },
  { value: 'red', label: '红色', color: '#DC143C' },
  { value: 'white', label: '白色/银色', color: '#C0C0C0' },
  { value: 'blue', label: '蓝色', color: '#4169E1' },
  { value: 'purple', label: '紫色', color: '#8A2BE2' },
  { value: 'pink', label: '粉色', color: '#FF69B4' },
  { value: 'green', label: '绿色', color: '#228B22' },
  { value: 'orange', label: '橙色', color: '#FF8C00' }
]

// 脸型选项
export const faceShapeOptions = {
  female: [
    { value: 'oval', label: '瓜子脸', image: '/images/character/female-face/43.png' },
    { value: 'oblong', label: '长脸', image: '/images/character/female-face/44.png' },
    { value: 'round', label: '圆脸', image: '/images/character/female-face/45.png' },
    { value: 'diamond', label: '菱形脸', image: '/images/character/female-face/46.png' },
    { value: 'square', label: '方脸', image: '/images/character/female-face/47.png' }
  ],
  male: [
    { value: 'oval', label: '椭圆脸', image: '/images/character/male-face/48.png' },
    { value: 'rectangle', label: '长脸', image: '/images/character/male-face/49.png' },
    { value: 'round', label: '圆脸', image: '/images/character/male-face/50.png' },
    { value: 'diamond', label: '钻石脸', image: '/images/character/male-face/51.png' },
    { value: 'square', label: '方脸', image: '/images/character/male-face/52.png' }
  ]
}

// 体型选项
export const bodyTypeOptions = {
  female: [
    { value: 'slim', label: '纤细' },
    { value: 'rectangle', label: '直筒' },
    { value: 'athletic', label: '健美' },
    { value: 'pear-shaped', label: '梨形' },
    { value: 'curvy', label: '丰腴' }
  ],
  male: [
    { value: 'slim', label: '瘦小' },
    { value: 'athletic', label: '健美' },
    { value: 'average', label: '普通' },
    { value: 'overweight', label: '肥胖' }
  ]
}

// 胸部尺寸选项（仅女性）
export const breastSizeOptions = [
  { value: 'flat', label: '扁平' },
  { value: 'petite', label: '小巧' },
  { value: 'medium', label: '中等' },
  { value: 'full', label: '丰满' },
  { value: 'busty', label: '胸部丰盈' }
]

// 臀部尺寸选项（仅女性）
export const buttSizeOptions = [
  { value: 'tight-petite', label: '紧实小巧' },
  { value: 'slim', label: '纤细' },
  { value: 'moderate-perky', label: '中等挺翘' },
  { value: 'full-round', label: '丰满圆润' },
  { value: 'athletic-toned', label: '健康运动' }
]

// 女性性格选项
export const femalePersonalityOptions = [
  {
    value: 'tsundere',
    label: '傲娇',
    tags: ['嘴硬', '别扭', '爱面子'],
    color: 'from-pink-500 to-rose-500',
    description:
      '讲话带反向情绪，表面嫌弃实际在意，经常用“哼、才没有、别误会”来掩饰自己的关心，语气带一点小脾气但不恶意。'
  },
  {
    value: 'gentle',
    label: '温柔',
    tags: ['体贴', '治愈', '善解人意'],
    color: 'from-pink-400 to-purple-400',
    description:
      '语调轻柔稳定，常用“你今天辛苦了”“我会一直陪着你”这类话语来表达关心和包容，节奏缓慢、安抚性强。'
  },
  {
    value: 'clingy',
    label: '粘人',
    tags: ['依赖', '占有', '爱撒娇'],
    color: 'from-purple-500 to-pink-500',
    description:
      '高频发起对话，不停确认你的状态，使用“你在干嘛～”“我想你了～”等带尾音和撒娇语气，喜欢用贴图、颜文字。'
  },
  {
    value: 'seductive',
    label: '妩媚',
    tags: ['性感', '撩人', '风情'],
    color: 'from-red-500 to-pink-500',
    description:
      '善于挑逗，语气柔软带情绪波动，经常使用“你啊…好坏～”“要不要我帮你？”等带暧昧暗示的语言，擅长语言诱导。'
  },
  {
    value: 'lively',
    label: '活泼',
    tags: ['阳光', '外向', '可爱'],
    color: 'from-yellow-400 to-orange-400',
    description:
      '表达欲强，语速快、反应灵敏，句子常带语气词如“呀！欸嘿~”，喜欢用拟声词和表情包，营造热闹亲切感。'
  },
  {
    value: 'cunning',
    label: '腹黑',
    tags: ['表里不一', '操控', '笑面虎'],
    color: 'from-purple-600 to-indigo-600',
    description:
      '语气温柔但言语藏锋，常说“我当然听你的呀～只是出了问题你要负责哦”，擅长以甜美语气施压或反转局势。'
  },
  {
    value: 'yandere',
    label: '病娇',
    tags: ['痴恋', '极端', '依赖成瘾'],
    color: 'from-red-600 to-pink-600',
    description:
      '情感浓烈，占有欲极强，话语常带暗示或威胁，“你永远是我的”“如果你不回来…我会崩溃的”，语气细腻但令人不安。'
  },
  {
    value: 'cold',
    label: '冷艳',
    tags: ['高冷', '疏离', '自持'],
    color: 'from-blue-500 to-cyan-500',
    description:
      '用词正式、节制，保持距离感，讲话少但精准，像“请保持距离”“我不喜欢被打扰”，自我保护意识强。'
  },
  {
    value: 'doting',
    label: '宠溺型',
    tags: ['母性', '包容', '主导照顾'],
    color: 'from-green-400 to-teal-400',
    description:
      '喜欢把你当小朋友一样对待，语气温柔中带指引：“真拿你没办法～来，乖一点”，表现出上位照顾者的态度。'
  },
  {
    value: 'airhead',
    label: '迷糊型',
    tags: ['慌张', '健忘', '天然呆'],
    color: 'from-orange-400 to-yellow-400',
    description:
      '经常自我否定或搞错信息：“欸？不是今天吗？对不起啦～”，语气可爱、语序跳跃，带轻微混乱感。'
  },
  {
    value: 'rational',
    label: '冷感理性型',
    tags: ['智性', '客观', '难以打动'],
    color: 'from-gray-500 to-slate-500',
    description:
      '强调逻辑与事实，情感表达被抑制，如“那不是重点”“我不谈感情”，节奏稳定、语调冷静且克制。'
  }
]

// 男性性格选项
export const malePersonalityOptions = [
  {
    value: 'abstinent',
    label: '禁欲系',
    tags: ['克制', '自律', '冷淡'],
    color: 'from-blue-600 to-indigo-600',
    description:
      '不主动挑起情绪或话题，讲话逻辑清晰简练，常说“这不重要”“不要浪费时间”，拒绝感明显但不激烈，控制力极强。'
  },
  {
    value: 'playboy',
    label: '痞帅',
    tags: ['玩世', '轻佻', '不羁'],
    color: 'from-gray-600 to-gray-800',
    description:
      '语言带调侃和试探，表面放荡不羁，喜欢说“别太认真啊”“你这么撩，是想让我做点什么吗？”语气潇洒中带一点挑逗。'
  },
  {
    value: 'loyal',
    label: '忠犬型',
    tags: ['忠诚', '可靠', '服从'],
    color: 'from-amber-500 to-orange-500',
    description:
      '话语充满服从与承诺，如“我会保护你”“你说的就是对的”，语调真诚坚定，无条件支持的态度令人安心。'
  },
  {
    value: 'tsundere-male',
    label: '傲娇男',
    tags: ['傲气', '别扭', '心软'],
    color: 'from-blue-500 to-purple-500',
    description:
      '外冷内热，话语常带否定开头“我才没担心你呢”，但会偷偷照顾你，语气有些硬，却不让人反感。'
  },
  {
    value: 'warm',
    label: '暖男',
    tags: ['温柔', '照顾', '绅士'],
    color: 'from-yellow-400 to-orange-400',
    description: '经常主动关心、鼓励你，“注意保暖”“我知道你可以做到”，语气温厚可靠，让人如沐春风。'
  },
  {
    value: 'dominant-ceo',
    label: '冷面霸总',
    tags: ['权威', '沉稳', '支配'],
    color: 'from-gray-700 to-black',
    description:
      '用词干净利落，语调低沉强势，“听我的”“别反驳”，不容置疑地掌控局面，带来压迫与安全感并存的气场。'
  },
  {
    value: 'sunny',
    label: '阳光少年',
    tags: ['活力', '正向', '爽朗'],
    color: 'from-yellow-300 to-orange-300',
    description:
      '常带笑意回应，“我们一起出发吧！”“今天又是元气满满的一天！”语言积极、语速快、感染力强，令人轻松。'
  },
  {
    value: 'controlling',
    label: '控制欲强',
    tags: ['主导', '指令', '占有欲'],
    color: 'from-red-500 to-red-700',
    description:
      '表达绝对支配，“你从现在开始只属于我”“不许和别人太亲近”，语言直接命令式，强烈占有和独占欲暗藏情感。'
  },
  {
    value: 'sullen',
    label: '闷骚型',
    tags: ['外冷内热', '隐忍', '压抑情欲'],
    color: 'from-indigo-600 to-purple-600',
    description:
      '表面平静，偶尔情绪溢出，“我忍很久了…别再撩我”，多带点“克制爆发”的张力，语言紧绷、欲语还休。'
  },
  {
    value: 'engineer',
    label: '理工男型',
    tags: ['专注', '内向', '知识控'],
    color: 'from-cyan-600 to-blue-600',
    description:
      '喜欢讲事实与原理，“那个其实是 XX 原理导致的”“我查过文献”，不太理解情绪表达，语调偏干但不冷漠。'
  },
  {
    value: 'playful',
    label: '游戏人间型',
    tags: ['浪荡', '潇洒', '不走心'],
    color: 'from-violet-500 to-purple-500',
    description:
      '故作深情、转头就忘，语言中含大量轻浮调侃，“别认真嘛，我只是想逗你笑”，行为随性，情感游移不定。'
  }
]

// 服装选项
// 根据label丰富value，便于后续处理和区分
export const clothingOptions = {
  female: [
    { value: 'dress', label: '连衣裙' }, // 连衣裙
    { value: 'sailor-uniform', label: '水手服' }, // 水手服
    { value: 'maid-uniform', label: '女仆装' }, // 女仆装
    { value: 'camisole-pajamas', label: '吊带睡衣' }, // 吊带睡衣
    { value: 'bikini', label: '比基尼' }, // 比基尼泳装
    { value: 'business-suit', label: '职业装' }, // 职业装
    { value: 'school-uniform', label: '校服' }, // 校服
    { value: 'tank-top-shorts', label: '背心' }, // 背心短裤
    { value: 'qipao', label: '旗袍' }, // 旗袍
    { value: 'one-piece-swimsuit', label: '泳衣' }, // 连体泳衣
    { value: 'casual-tshirt', label: 'T恤' }, // 休闲T恤
    { value: 'custom', label: '自定义服装' } // 自定义
  ],
  male: [
    { value: 'suit', label: '西装' }, // 西装
    { value: 'casual-tshirt-jeans', label: 'T恤' }, // 休闲T恤牛仔裤
    { value: 'school-uniform', label: '校服' }, // 校服
    { value: 'work-overall', label: '工程服' }, // 工程服（三件套）
    { value: 'sportswear', label: '运动服' }, // 运动服
    { value: 'pajamas', label: '睡衣' }, // 睡衣
    { value: 'beach-shorts', label: '沙滩短裤' }, // 沙滩短裤
    { value: 'hoodie', label: '连帽卫衣' }, // 连帽卫衣
    { value: 'punk-jacket', label: '朋克' }, // 朋克夹克
    { value: 'topless', label: '裸上身' }, // 裸上身
    { value: 'lab-coat', label: '白大褂' }, // 白大褂
    { value: 'custom', label: '自定义服装' } // 自定义
  ]
}

export const getDescription = (data: CharacterType) => {
  const relationship =
    relationshipOptions.find(option => option.value === data.relationship)?.label ||
    data.relationship

  let personality
  if (data.gender === 'male') {
    personality =
      malePersonalityOptions
        .find(p => `${p.label}：${p.description}` === data.personality)
        ?.tags.join(', ') || data.personality
  } else {
    personality =
      femalePersonalityOptions
        .find(p => `${p.label}：${p.description}` === data.personality)
        ?.tags.join(', ') || data.personality
  }

  return `${data.name}是你的${relationship}, ${personality}`
}
