/**
 * 语音转文本 API 端点
 * 使用 ElevenLabs API 进行语音识别
 * 使用统一的 apiClient 处理认证和请求
 */

import { apiClient } from '../client'
import { validateAudioFile } from '../../lib/audio/audioUtils'
import { isCapacitorEnvironment } from '../../lib/utils'
import { getAppApiUrl } from '@/utils/check-version'

// API 响应类型定义（匹配 Fish Audio 后端接口）
export interface SpeechToTextResponse {
  success: boolean
  transcription: string
  metadata: {
    audioFileName: string
    audioFileType: string
    audioFileSize: number
    transcriptionLength: number
    processingTime: string
    language: string
    provider: 'elevenlabs' | 'fish-audio' | 'whisper' // 服务提供商
    duration?: number // 音频时长（秒）
    segmentsCount?: number // 分段数量
    processed: boolean // 是否经过后处理
    hasTranslation: boolean // 是否进行了翻译
    hasPunctuation: boolean // 是否添加了标点符号
  }
  segments?: Array<{
    text: string
    start: number
    end: number
  }> // 可选的分段信息
}

// 错误响应类型
export interface SpeechToTextError {
  error: string
  code?: string // 错误代码，如 'RATE_LIMITED', 'CAPACITY_EXCEEDED'
  retryAfter?: number // 建议重试间隔（秒）
  allowedTypes?: string[]
  maxSize?: string
  receivedSize?: string
  details?: string
  provider?: string // 服务提供商信息
}

// API 请求选项
export interface SpeechToTextOptions {
  language?: string // 语言代码，如 'zh', 'en'
  timeout?: number // 超时时间（毫秒）
  maxRetries?: number // 最大重试次数
}

// 上传进度回调
export type ProgressCallback = (progress: number) => void

// 默认配置
const DEFAULT_OPTIONS: Required<SpeechToTextOptions> = {
  language: 'zh', // 默认中文，ElevenLabs 支持多语言
  timeout: 60000, // 60秒，ElevenLabs 可能需要更长时间
  maxRetries: 3
}

/**
 * 语音转文本主函数
 */
export async function speechToText(
  audioBlob: Blob,
  options: SpeechToTextOptions = {},
  onProgress?: ProgressCallback
): Promise<SpeechToTextResponse | SpeechToTextError> {
  const config = { ...DEFAULT_OPTIONS, ...options }

  try {
    // 验证音频文件
    const validation = validateAudioFile(audioBlob)
    if (!validation.valid) {
      return {
        error: validation.error || '音频文件验证失败'
      }
    }

    // 实际的API调用
    console.log('🎤 开始 ElevenLabs 语音转文本处理:', {
      audioSize: audioBlob.size,
      audioDuration: Math.round(audioBlob.size / 16000), // 粗略估算
      options: config
    })

    // 调用实际API
    return await executeWithRetry(audioBlob, config, onProgress)
  } catch (error) {
    console.error('语音转文字失败:', error)
    return {
      error: error instanceof Error ? error.message : '未知错误'
    }
  }
}

/**
 * 带重试的执行函数
 */
async function executeWithRetry(
  audioBlob: Blob,
  options: Required<SpeechToTextOptions>,
  onProgress?: ProgressCallback,
  attempt: number = 1
): Promise<SpeechToTextResponse | SpeechToTextError> {
  try {
    return await performSpeechToText(audioBlob, options, onProgress)
  } catch (error) {
    // 如果还有重试机会且是网络错误
    if (attempt < options.maxRetries && isRetryableError(error)) {
      console.warn(`语音转文字失败，重试第 ${attempt + 1} 次:`, error)

      // 指数退避延迟
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000)
      await new Promise(resolve => setTimeout(resolve, delay))

      return executeWithRetry(audioBlob, options, onProgress, attempt + 1)
    }

    throw error
  }
}

/**
 * 判断错误是否可以重试
 */
function isRetryableError(error: any): boolean {
  if (!error) return false

  // 网络错误
  if (error.name === 'NetworkError' || error.name === 'TypeError') {
    return true
  }

  // HTTP 状态码错误
  if (error.status) {
    const retryableStatuses = [408, 429, 500, 502, 503, 504]
    return retryableStatuses.includes(error.status)
  }

  // ElevenLabs 特定错误码
  if (error.code) {
    const retryableCodes = ['RATE_LIMITED', 'QUOTA_EXCEEDED', 'CAPACITY_EXCEEDED']
    return retryableCodes.includes(error.code)
  }

  // 超时错误
  if (error.name === 'AbortError' || error.message?.includes('timeout')) {
    return true
  }

  return false
}

/**
 * 执行实际的语音转文字请求
 * 使用统一的 apiClient 但处理 FormData 上传
 */
async function performSpeechToText(
  audioBlob: Blob,
  options: Required<SpeechToTextOptions>,
  onProgress?: ProgressCallback
): Promise<SpeechToTextResponse | SpeechToTextError> {
  // 创建 FormData
  const formData = new FormData()
  formData.append('audio', audioBlob, 'audio.' + getFileExtension(audioBlob.type))

  // 添加语言参数
  if (options.language) {
    formData.append('language', options.language)
  }

  try {
    // 如果需要进度回调，使用自定义fetch
    if (onProgress) {
      return await performUploadWithProgress(formData, options, onProgress)
    }

    // 使用标准 apiClient 调用新的 ElevenLabs 语音转文本端点
    const response = await apiClient.request<SpeechToTextResponse>('/api/app/speech-to-text', {
      method: 'POST',
      headers: {}, // 不设置 Content-Type，让浏览器自动处理
      body: formData as any, // FormData类型兼容处理
      requireAuth: true
    })

    return response
  } catch (error) {
    console.error('语音转文字请求失败:', error)

    // 如果是API错误响应，直接返回错误信息
    if (error && typeof error === 'object' && 'error' in error) {
      return error as SpeechToTextError
    }

    throw error
  }
}

/**
 * 带进度的上传处理
 * 当需要进度回调时，使用自定义实现但保持认证
 */
async function performUploadWithProgress(
  formData: FormData,
  options: Required<SpeechToTextOptions>,
  onProgress: ProgressCallback
): Promise<SpeechToTextResponse | SpeechToTextError> {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()

    // 进度监听
    xhr.upload.addEventListener('progress', event => {
      if (event.lengthComputable) {
        const progress = (event.loaded / event.total) * 100
        onProgress(progress)
      }
    })

    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response: SpeechToTextResponse = JSON.parse(xhr.responseText)
          resolve(response)
        } catch (error) {
          reject(new Error('解析响应失败'))
        }
      } else {
        try {
          const errorData: SpeechToTextError = JSON.parse(xhr.responseText)
          resolve(errorData) // 返回错误响应而不是reject
        } catch {
          reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`))
        }
      }
    })

    xhr.addEventListener('error', () => {
      reject(new Error('网络请求失败'))
    })

    xhr.addEventListener('timeout', () => {
      reject(new Error('请求超时'))
    })

    xhr.addEventListener('abort', () => {
      reject(new Error('请求被取消'))
    })

    // 设置超时
    xhr.timeout = options.timeout

    // 构建完整的API URL，与 client.ts 保持一致
    const API_URL = isCapacitorEnvironment() ? getAppApiUrl() : import.meta.env.VITE_WEB_API_URL

    // 必须先调用 open() 再设置请求头
    xhr.open('POST', `${API_URL}/api/app/speech-to-text`)

    // 添加认证头 - 必须在 open() 之后
    const token = localStorage.getItem('access_token')
    if (token) {
      xhr.setRequestHeader('Authorization', `Bearer ${token}`)
    }

    xhr.send(formData)
  })
}

/**
 * 根据 MIME 类型获取文件扩展名 (ElevenLabs 支持的格式)
 */
function getFileExtension(mimeType: string): string {
  const typeMap: Record<string, string> = {
    'audio/webm': 'webm',
    'audio/mp4': 'mp4',
    'audio/wav': 'wav',
    'audio/mpeg': 'mp3',
    'audio/ogg': 'ogg',
    'audio/m4a': 'm4a',
    'audio/flac': 'flac'
  }

  return typeMap[mimeType] || 'webm'
}

/**
 * 预检查 API 可用性
 */
export async function checkApiAvailability(): Promise<{
  available: boolean
  error?: string
}> {
  try {
    await apiClient.get('/health')
    return { available: true }
  } catch (error) {
    return {
      available: false,
      error: error instanceof Error ? error.message : 'API 连接失败'
    }
  }
}

/**
 * 获取支持的语言列表 (ElevenLabs 格式)
 */
export function getSupportedLanguages(): Array<{
  code: string
  name: string
  nativeName: string
}> {
  return [
    { code: 'zh', name: 'Chinese', nativeName: '中文' },
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'ja', name: 'Japanese', nativeName: '日本語' },
    { code: 'ko', name: 'Korean', nativeName: '한국어' },
    { code: 'es', name: 'Spanish', nativeName: 'Español' },
    { code: 'fr', name: 'French', nativeName: 'Français' },
    { code: 'de', name: 'German', nativeName: 'Deutsch' },
    { code: 'it', name: 'Italian', nativeName: 'Italiano' },
    { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
    { code: 'ru', name: 'Russian', nativeName: 'Русский' },
    { code: 'ar', name: 'Arabic', nativeName: 'العربية' },
    { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी' },
    { code: 'th', name: 'Thai', nativeName: 'ไทย' },
    { code: 'vi', name: 'Vietnamese', nativeName: 'Tiếng Việt' }
  ]
}

/**
 * 估算转换时间（基于音频时长）
 */
export function estimateConversionTime(audioDurationSeconds: number): number {
  // 经验值：通常转换时间约为音频时长的 0.1-0.3 倍
  const baseTime = Math.max(audioDurationSeconds * 0.2, 2) // 最少2秒
  const maxTime = Math.min(baseTime, 30) // 最多30秒
  return Math.round(maxTime)
}
