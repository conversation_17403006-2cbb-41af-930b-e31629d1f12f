import { uploadFile } from '../endpoints/upload'
import { apiClient } from '../client'

// 上传文件响应接口 - 匹配后端服务返回格式
export interface UploadResponse {
  success: boolean
  data: {
    fileId: string
    fileName: string
    fileSize: number
    mimeType: string
    url: string
  }
}

// 简化的头像上传响应接口 - 用于组件
export interface AvatarUploadResponse {
  avatarUrl: string
}

// 文件上传类型
export type UploadType = 'avatar' | 'character' | 'image' | 'document' | 'audio'

// 上传进度回调类型
export type UploadProgressCallback = (progress: number) => void

// 文件上传 API 服务
export const uploadService = {
  // 上传头像
  async uploadAvatar(
    file: File,
    onProgress?: UploadProgressCallback
  ): Promise<AvatarUploadResponse> {
    // TODO: 实现进度回调支持
    const response = await uploadFile(file, 'avatar')
    return { avatarUrl: response.data.url }
  },

  // 上传角色图片
  async uploadCharacterImage(
    file: File,
    onProgress?: UploadProgressCallback
  ): Promise<AvatarUploadResponse> {
    // TODO: 实现进度回调支持
    const response = await uploadFile(file, 'character')
    return { avatarUrl: response.data.url }
  },

  // 上传通用图片
  async uploadImage(
    file: File,
    onProgress?: UploadProgressCallback
  ): Promise<AvatarUploadResponse> {
    // TODO: 实现通用图片上传端点
    const response = await uploadFile(file, 'character') // 暂时使用 character 端点
    return { avatarUrl: response.data.url }
  },

  // 上传文档
  async uploadDocument(
    file: File,
    onProgress?: UploadProgressCallback
  ): Promise<AvatarUploadResponse> {
    // TODO: 实现文档上传端点
    throw new Error('文档上传功能暂未实现')
  },

  // 上传音频
  async uploadAudio(
    file: File,
    onProgress?: UploadProgressCallback
  ): Promise<AvatarUploadResponse> {
    // TODO: 实现音频上传端点
    throw new Error('音频上传功能暂未实现')
  },

  // 批量上传文件
  async uploadMultiple(
    files: File[],
    type: UploadType,
    onProgress?: (fileIndex: number, progress: number) => void
  ): Promise<AvatarUploadResponse[]> {
    const uploadPromises = files.map((file, index) => {
      // 根据类型选择上传方法
      if (type === 'avatar') {
        return this.uploadAvatar(file)
      } else if (type === 'character' || type === 'image') {
        return this.uploadCharacterImage(file)
      } else {
        throw new Error(`不支持的批量上传类型: ${type}`)
      }
    })

    return Promise.all(uploadPromises)
  },

  // 验证文件类型和大小
  validateFile(file: File, type: UploadType): { valid: boolean; error?: string } {
    const maxSizes: Record<UploadType, number> = {
      avatar: 5 * 1024 * 1024, // 5MB
      character: 10 * 1024 * 1024, // 10MB
      image: 10 * 1024 * 1024, // 10MB
      document: 50 * 1024 * 1024, // 50MB
      audio: 100 * 1024 * 1024 // 100MB
    }

    const allowedTypes: Record<UploadType, string[]> = {
      avatar: ['image/jpeg', 'image/png', 'image/webp'],
      character: ['image/jpeg', 'image/png', 'image/webp'],
      image: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      document: [
        'application/pdf',
        'text/plain',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ],
      audio: ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp4']
    }

    // 检查文件大小
    if (file.size > maxSizes[type]) {
      return {
        valid: false,
        error: `文件大小不能超过 ${Math.round(maxSizes[type] / 1024 / 1024)}MB`
      }
    }

    // 检查文件类型
    if (!allowedTypes[type].includes(file.type)) {
      return {
        valid: false,
        error: `不支持的文件类型: ${file.type}`
      }
    }

    return { valid: true }
  },

  // 获取上传历史
  async getUploadHistory(params?: { type?: UploadType; limit?: number; offset?: number }) {
    const queryParams = new URLSearchParams()

    if (params?.type) {
      queryParams.append('type', params.type)
    }
    if (params?.limit) {
      queryParams.append('limit', params.limit.toString())
    }
    if (params?.offset) {
      queryParams.append('offset', params.offset.toString())
    }

    const endpoint = `/api/app/upload/history${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`

    // 使用 apiClient 替代直接的 fetch
    return apiClient.get(endpoint)
  },

  // 删除上传的文件
  async deleteUploadedFile(fileId: string) {
    // 使用 apiClient 替代直接的 fetch
    return apiClient.delete(`/api/app/upload/${fileId}`)
  }
}
