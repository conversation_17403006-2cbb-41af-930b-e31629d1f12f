import { apiClient } from '../client'

// 生成音频请求参数
export interface GenerateAudioRequest {
  text: string
  messageId?: string
  chatId?: string
  voiceModelId?: string // 声音模型ID
}

// 生成音频响应（同步版本）
export interface GenerateAudioResponse {
  success: boolean
  data: {
    audioUrl: string
    status: 'completed'
  }
  message?: string
}

/**
 * TTS 服务类
 */
class TTSService {
  /**
   * 生成音频（同步）
   */
  async generateAudio(params: GenerateAudioRequest): Promise<GenerateAudioResponse> {
    try {
      const response = await apiClient.post<GenerateAudioResponse>('/api/app/tts/generate', params)
      return response
    } catch (error: any) {
      console.error('生成音频失败:', error)
      throw error
    }
  }
}

// 创建服务实例
export const ttsService = new TTSService()
