import { apiClient } from '../client'

// 图像生成任务状态类型
export interface ImageGenerationTask {
  taskId: string
  status: string
  progress: number
  estimatedSteps: number
  completedSteps: number
  imageUrl: string | null
  errorMessage?: string
}

// 图像生成响应类型
export interface ImageGenerationResponse {
  success: boolean
  data: {
    type: 'completed' | 'async'
    taskId: string
    originalKeywords: string
    optimizedPrompt: string
    imageUrl?: string
    message?: string
  }
}

// 图像生成 API 服务
export const imageService = {
  // 优化Prompt
  async optimizePrompt(keywords: string) {
    return apiClient.post<{ prompt: string }>('/api/app/optimize-prompt', {
      keywords
    })
  },

  // 关键词优化 + 图片生成一体化接口
  async generateWithOptimization(data: {
    keywords: string
    generator?: 'yunwu' | 'insa3d-v2' | 'insa3d-v3'
    waitForCompletion?: boolean
  }) {
    return apiClient.post<ImageGenerationResponse>(
      '/api/app/image-generation/generate-with-optimization',
      {
        keywords: data.keywords,
        generator: data.generator || 'insa3d-v3',
        waitForCompletion: data.waitForCompletion || false
      }
    )
  },

  // 生成图片（原有接口）
  async generateImage(prompt: string) {
    return apiClient.post<{ imageUrl: string }>('/api/app/generate-image', {
      prompt
    })
  },

  // 新的图片生成接口（Insa3D v2）
  async generateImageV2(data: { prompt: string; imageUrl?: string }) {
    // 转换为后端期望的格式
    const inputs: Record<string, any> = {
      prompt: { title: 'prompt', value: data.prompt }
    }

    if (data.imageUrl) {
      inputs.image = { title: 'Load Image', value: data.imageUrl }
    }

    return apiClient.post<{
      success: boolean
      data: {
        taskId: string
        status: string
        estimatedSteps: number
        prompt: string
        imageUrl: string | null
      }
    }>('/api/app/generate-imagev2', { inputs })
  },

  // 查询图片生成任务状态（v2）
  async getImageGenerationStatus(taskId: string) {
    return apiClient.get<{
      success: boolean
      data: ImageGenerationTask
    }>(`/api/app/generate-imagev2?taskId=${taskId}`)
  },

  // 查询图片生成任务状态（v3）- 使用新的统一接口
  async getImageGenerationStatusV3(taskId: string) {
    return apiClient.get<{
      success: boolean
      data: ImageGenerationTask
    }>(`/api/app/image-generation/task/${taskId}`)
  },

  // 获取图片生成历史
  async getGenerationHistory(params?: { limit?: number; offset?: number }) {
    const queryParams = new URLSearchParams()

    if (params?.limit) {
      queryParams.append('limit', params.limit.toString())
    }
    if (params?.offset) {
      queryParams.append('offset', params.offset.toString())
    }

    const endpoint = `/api/app/image-generation/history${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`
    return apiClient.get<{
      success: boolean
      data: {
        tasks: ImageGenerationTask[]
        total: number
        hasMore: boolean
      }
    }>(endpoint)
  },

  // 删除图片生成任务
  async deleteGenerationTask(taskId: string) {
    return apiClient.delete<{ success: boolean }>(`/api/app/image-generation/task/${taskId}`)
  },

  // 新的图片生成 API v2 - 队列版本
  async generateImageV2Queue(data: {
    messageId: string
    chatId: string
    prompt: string
    characterAvatar?: string
    characterId?: string // 添加 characterId 参数
    metadata?: {
      width?: number
      height?: number
      steps?: number
      cfg?: number
      negativePrompt?: string
    }
  }) {
    return apiClient.post<{
      success: boolean
      taskId: string
      message: string
      data: {
        taskId: string
        pointsConsumed: number
        remainingPoints: number
        generation: {
          prompt: string
          characterAvatar?: string
          metadata: {
            width: number
            height: number
            steps: number
            cfg: number
          }
          createdAt: string
        }
      }
    }>('/api/app/image-generation-v2/generate', data)
  },

  // 查询图片生成 v2 任务状态
  async getImageGenerationV2Status(taskId: string) {
    return apiClient.get<{
      success: boolean
      data: {
        taskId: string
        status: string
        message: string
      }
    }>(`/api/app/image-generation-v2/status/${taskId}`)
  },

  // 检查图片生成 v2 服务健康状态
  async checkImageGenerationV2Health() {
    return apiClient.get<{
      success: boolean
      data: {
        service: string
        status: string
        insa3dApi: {
          endpoint: string
          configured: boolean
        }
        queue: {
          bound: boolean
          name: string
        }
        timestamp: string
      }
    }>('/api/app/image-generation-v2/health')
  },

  // RunPod 图片生成接口
  async generateImageRunpod(data: {
    messageId: string
    chatId: string
    prompt: string
    characterAvatar?: string
    characterId?: string
    metadata?: {
      width?: number
      height?: number
    }
  }) {
    return apiClient.post<{
      success: boolean
      taskId: string
      message: string
      data?: {
        pointsRefunded?: number
      }
    }>('/api/app/image-generation-runpod/generate', data)
  },

  // 查询 RunPod 图片生成状态
  async getImageGenerationRunpodStatus(taskId: string) {
    return apiClient.get<{
      success: boolean
      data: {
        taskId: string
        status: string
        message: string
      }
    }>(`/api/app/image-generation-runpod/status/${taskId}`)
  }
}
