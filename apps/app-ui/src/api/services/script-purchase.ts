import { apiClientV2, type ApiResponse } from '../clientv2'

// 剧本购买记录类型
export interface ScriptPurchase {
  id: string
  userId: string
  scriptId: string
  pointsCost: number
  transactionId: string | null
  status: 'completed' | 'refunded'
  expiresAt: string
  isDownloaded: boolean
  downloadedAt: string | null
  createdAt: string
  updatedAt: string
}

// 购买响应数据类型
export interface PurchaseData {
  purchase: ScriptPurchase
  script: {
    id: string
    title: string
    description: string
    coverImage: string
  }
  remainingPoints?: number
}

// 购买状态响应数据类型
export interface PurchaseStatusData {
  isPurchased: boolean
  purchase: ScriptPurchase | null
}

// 已购买剧本列表响应数据类型
export interface PurchasedScriptsData {
  scriptIds: string[]
}

// 剧本内容下载响应数据类型
export interface ScriptContentData {
  id: string
  title: string
  content: any
  audioUrl: string | null
  totalDuration: number | null
  stageCount: number | null
}

// 剧本购买API服务类
export class ScriptPurchaseService {
  /**
   * 购买剧本
   */
  static async purchaseScript(scriptId: string): Promise<ApiResponse<PurchaseData>> {
    return apiClientV2.post<PurchaseData>(`/api/app/scripts/${scriptId}/purchase`)
  }

  /**
   * 检查剧本购买状态
   */
  static async checkPurchaseStatus(scriptId: string): Promise<ApiResponse<PurchaseStatusData>> {
    return apiClientV2.get<PurchaseStatusData>(`/api/app/scripts/${scriptId}/purchase-status`)
  }

  /**
   * 获取用户已购买的剧本ID列表
   */
  static async getPurchasedScriptIds(): Promise<ApiResponse<PurchasedScriptsData>> {
    return apiClientV2.get<PurchasedScriptsData>('/api/app/scripts/purchased')
  }

  /**
   * 下载剧本内容
   */
  static async downloadScriptContent(scriptId: string): Promise<ApiResponse<ScriptContentData>> {
    return apiClientV2.post<ScriptContentData>(`/api/app/scripts/${scriptId}/download`)
  }
}

// 导出默认实例
export const scriptPurchaseService = ScriptPurchaseService
