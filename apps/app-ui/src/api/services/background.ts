import { apiClient } from '../client'

export interface BackgroundImageResponse {
  success: boolean
  data: {
    backgroundImageUrl: string | null
    backgroundSceneDescription?: string | null
    chatId: string
  }
}

/**
 * 为聊天生成背景图
 */
export const generateChatBackground = async (
  chatId: string,
  sceneDescription: string
): Promise<string> => {
  try {
    const response = await apiClient.post<BackgroundImageResponse>(
      `/api/app/background/generate/${chatId}`,
      {
        sceneDescription
      }
    )

    if (response.success && response.data.backgroundImageUrl) {
      return response.data.backgroundImageUrl
    } else {
      throw new Error('背景图生成失败')
    }
  } catch (error) {
    console.error('生成背景图失败:', error)
    throw error
  }
}

/**
 * 获取聊天背景图和场景描述
 */
export const getChatBackground = async (
  chatId: string
): Promise<{
  backgroundImageUrl: string | null
  backgroundSceneDescription: string | null
} | null> => {
  try {
    const response = await apiClient.get<BackgroundImageResponse>(`/api/app/background/${chatId}`)

    if (response.success) {
      return {
        backgroundImageUrl: response.data.backgroundImageUrl,
        backgroundSceneDescription: response.data.backgroundSceneDescription || null
      }
    } else {
      return null
    }
  } catch (error) {
    console.error('获取背景图失败:', error)
    return null
  }
}
