// TTS3 流式音频生成服务
import { apiClientV2, type ApiResponse } from '@/api/clientv2'

// TTS3 请求类型
export interface GenerateStreamAudioRequest {
  text: string
  messageId?: string
  chatId?: string
  voiceModelId?: string // 声音模型ID
}

// TTS3 错误响应类型
export interface TTS3ErrorResponse {
  success: false
  message: string
}

// TTS3 健康检查响应类型
export interface HealthResponse {
  success: boolean
  data: {
    service: string
    status: string
  }
  message?: string
}

// TTS3 服务类
class TTS3Service {
  /**
   * 生成流式音频
   * 返回 Response 对象，调用方需要处理流
   */
  async generateStreamAudio(
    request: GenerateStreamAudioRequest,
    signal?: AbortSignal
  ): Promise<Response> {
    try {
      // 使用封装的 streamPost 方法
      const response = await apiClientV2.streamPost('/api/app/tts3/generate', request, {
        signal
      })

      return response
    } catch (error) {
      console.error('TTS3 生成音频失败:', error)
      throw error
    }
  }

  /**
   * 生成音频并返回 Blob URL（简化版本）
   */
  async generateAudioBlob(
    request: GenerateStreamAudioRequest,
    onProgress?: (progress: number) => void,
    signal?: AbortSignal
  ): Promise<string> {
    const response = await this.generateStreamAudio(request, signal)

    if (!response.ok) {
      // 尝试解析错误响应
      const contentType = response.headers.get('content-type')
      if (contentType?.includes('application/json')) {
        const errorData = (await response.json()) as TTS3ErrorResponse
        throw new Error(errorData.message || `HTTP ${response.status}`)
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    }

    // 检查响应类型
    const contentType = response.headers.get('content-type')
    if (!contentType?.includes('audio/')) {
      throw new Error('服务器返回的不是音频数据')
    }

    // 处理流式响应
    if (!response.body) {
      throw new Error('响应体为空')
    }

    const reader = response.body.getReader()
    const chunks: Uint8Array[] = []
    let receivedLength = 0
    const contentLength = parseInt(response.headers.get('content-length') || '0')

    try {
      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        if (value) {
          chunks.push(value)
          receivedLength += value.length

          // 报告进度
          if (onProgress && contentLength > 0) {
            const progress = (receivedLength / contentLength) * 100
            onProgress(Math.min(progress, 100))
          }
        }
      }
    } finally {
      reader.releaseLock()
    }

    // 合并所有音频块
    const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0)
    const audioData = new Uint8Array(totalLength)
    let offset = 0

    for (const chunk of chunks) {
      audioData.set(chunk, offset)
      offset += chunk.length
    }

    // 创建 Blob 和 URL
    const audioBlob = new Blob([audioData], { type: 'audio/mpeg' })
    const audioUrl = URL.createObjectURL(audioBlob)

    return audioUrl
  }

  /**
   * 健康检查
   */
  async health(): Promise<ApiResponse<HealthResponse>> {
    try {
      return await apiClientV2.get<HealthResponse>('/app/tts3/health')
    } catch (error) {
      console.error('TTS3 健康检查失败:', error)
      throw error
    }
  }

  /**
   * 清理 Blob URL（避免内存泄漏）
   */
  static revokeBlobUrl(url: string): void {
    if (url.startsWith('blob:')) {
      URL.revokeObjectURL(url)
    }
  }
}

// 导出服务实例
export const tts3Service = new TTS3Service()
