/**
 * 写真集相关 API 服务
 */

import { apiClient } from '../client'

// 模板相关类型
export interface PhotoTemplate {
  id: string
  name: string
  description: string
  previewImage: string
  pointsCost: number
  isPremium: boolean
  category?: string
  hasAccess: boolean
  requiresMembership: boolean
  prompt?: string
  usageCount?: number
  isActive: boolean
}

// 用户会员信息
export interface UserMembership {
  isMember: boolean
  canAccessPremium: boolean
}

// 后端 API 响应格式
export interface ApiResponse<T> {
  success: boolean
  data: T
  error?: string
}

// 模板列表数据
export interface TemplatesData {
  templates: PhotoTemplate[]
  userMembership: UserMembership
}

// 模板列表响应
export interface TemplatesResponse extends ApiResponse<TemplatesData> {}

// 生成任务响应
export interface GenerationTaskResponse {
  taskId: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  estimatedSteps?: number
  completedSteps?: number
  pointsConsumed?: number
  remainingPoints?: number
  generationId?: string
  imageUrl?: string
  errorMessage?: string
}

// 生成请求参数
export interface GenerationRequest {
  templateId: string
  characterId: string
  characterName: string
  characterImageUrl: string
  waitForCompletion?: boolean
  // 角色详细信息
  gender?: 'male' | 'female'
  ethnicity?: string
  age?: string | number
  eyeColor?: string
  hairStyle?: string
  hairColor?: string
  faceShape?: string // 添加脸型字段
  bodyType?: string
  breastSize?: string
  buttSize?: string
}

// 历史记录类型
export interface PhotoHistory {
  id: string
  templateId: string
  templateName: string
  originalImageUrl: string
  generatedImageUrl?: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  createdAt: string
  updatedAt: string
  errorMessage?: string
  pointsUsed: number
  metadata?: {
    characterId: string
    characterName: string
    taskId?: string
    generationId?: string
  }
}

/**
 * 写真集 API 服务
 */
export const photoAlbumService = {
  /**
   * 获取模板列表
   */
  async getTemplates(params?: {
    characterId?: string
    gender?: 'male' | 'female'
    category?: string
    premium?: boolean
  }): Promise<TemplatesData> {
    try {
      let url = '/api/app/templates'
      const queryParams: string[] = []

      if (params?.characterId) {
        queryParams.push(`characterId=${encodeURIComponent(params.characterId)}`)
      }
      if (params?.gender) {
        queryParams.push(`gender=${encodeURIComponent(params.gender)}`)
      }
      if (params?.category) {
        queryParams.push(`category=${encodeURIComponent(params.category)}`)
      }
      if (params?.premium !== undefined) {
        queryParams.push(`premium=${params.premium}`)
      }

      if (queryParams.length > 0) {
        url += `?${queryParams.join('&')}`
      }

      const response = await apiClient.get<TemplatesResponse>(url)
      return response.data
    } catch (error) {
      console.error('获取模板列表失败:', error)
      throw error
    }
  },

  /**
   * 获取模板详情
   */
  async getTemplate(templateId: string): Promise<PhotoTemplate> {
    try {
      const response = await apiClient.get<PhotoTemplate>(`/api/app/templates/${templateId}`)
      return response
    } catch (error) {
      console.error('获取模板详情失败:', error)
      throw error
    }
  },

  /**
   * 启动写真生成
   */
  async startGeneration(params: GenerationRequest): Promise<GenerationTaskResponse> {
    try {
      const response = await apiClient.post<GenerationTaskResponse>(
        '/api/app/photo-album-generation/generate',
        params
      )
      return response
    } catch (error) {
      console.error('启动生成失败:', error)
      throw error
    }
  },

  /**
   * 查询生成状态
   */
  async getGenerationStatus(taskId: string): Promise<GenerationTaskResponse> {
    try {
      console.log('🔍 [DEBUG] getGenerationStatus 调用, taskId:', taskId)
      console.log('🔍 [DEBUG] 构建的 URL:', `/api/app/photo-album-generation/status/${taskId}`)

      const response = await apiClient.get<GenerationTaskResponse>(
        `/api/app/photo-album-generation/status/${taskId}`
      )

      console.log('🔍 [DEBUG] getGenerationStatus 响应:', response)
      return response
    } catch (error) {
      console.error('查询状态失败:', error)
      throw error
    }
  },

  /**
   * 获取历史记录
   */
  async getHistory(characterId: string): Promise<PhotoHistory[]> {
    try {
      const url = `/api/app/photo-album-generation/history?characterId=${encodeURIComponent(
        characterId
      )}`
      const response = await apiClient.get<{
        success: boolean
        data: PhotoHistory[]
        pagination?: any
      }>(url)

      console.log('📋 [API] 历史记录响应:', response)

      // 处理响应格式
      if (response && typeof response === 'object' && 'data' in response) {
        return Array.isArray(response.data) ? response.data : []
      }

      // 如果响应直接是数组（向后兼容）
      if (Array.isArray(response)) {
        return response
      }

      console.warn('📋 [API] 历史记录响应格式异常:', typeof response, response)
      return []
    } catch (error) {
      console.error('获取历史记录失败:', error)
      throw error
    }
  },

  /**
   * 删除历史记录
   */
  async deleteHistory(historyId: string): Promise<void> {
    try {
      await apiClient.delete(`/api/app/photo-album-generation/history/${historyId}`)
    } catch (error) {
      console.error('删除历史记录失败:', error)
      throw error
    }
  },

  /**
   * 取消生成任务
   */
  async cancelGeneration(taskId: string): Promise<void> {
    try {
      await apiClient.post(`/api/app/photo-album-generation/cancel/${taskId}`)
    } catch (error) {
      console.error('取消任务失败:', error)
      throw error
    }
  },

  /**
   * 重新生成
   */
  async regenerate(historyId: string): Promise<GenerationTaskResponse> {
    try {
      const response = await apiClient.post<GenerationTaskResponse>(
        `/api/app/photo-album-generation/regenerate/${historyId}`
      )
      return response
    } catch (error) {
      console.error('重新生成失败:', error)
      throw error
    }
  },

  /**
   * 获取用户积分信息
   */
  async getUserPoints(): Promise<{ points: number; membership: UserMembership }> {
    try {
      const response = await apiClient.get<{ points: number; membership: UserMembership }>(
        '/api/app/user/points'
      )
      return response
    } catch (error) {
      console.error('获取积分信息失败:', error)
      throw error
    }
  }
}

export default photoAlbumService
