/**
 * 支付相关API服务
 */

import { apiClient } from '../client'
import type { ApiResponse } from '../client'

// 支付订单接口
export interface PaymentOrder {
  orderId: string
  paymentId: string
  redirectUrl: string
  amount: number
  originalAmount?: number
  planName: string
  description: string
  isUpgrade?: boolean
  savings?: number
}

// 支付状态接口
export interface PaymentStatus {
  success: boolean
  orderId: string
  paymentId: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  amount: number
  paidAt?: string
}

// 创建支付订单请求
export interface CreateOrderRequest {
  planId: string
  description?: string
  metadata?: Record<string, any>
}

// 模拟支付请求
export interface SimulatePaymentRequest {
  paymentId: string
  orderId: string
  action: 'success' | 'failed'
}

export const paymentService = {
  /**
   * 创建支付订单
   */
  async createOrder(data: CreateOrderRequest): Promise<ApiResponse<PaymentOrder>> {
    return apiClient.post('/api/app/payment/create-order', data)
  },

  /**
   * 查询支付状态
   */
  async getPaymentStatus(paymentId: string): Promise<ApiResponse<PaymentStatus>> {
    return apiClient.get(`/api/app/payment/status/${paymentId}`)
  },

  /**
   * 模拟支付操作（仅开发/测试环境）
   */
  async simulatePayment(data: SimulatePaymentRequest): Promise<ApiResponse<PaymentStatus>> {
    return apiClient.post('/api/app/payment/simulate', data)
  },

  /**
   * 获取调试信息（仅开发环境）
   */
  async getDebugInfo(): Promise<ApiResponse<any>> {
    return apiClient.get('/api/app/payment/debug')
  }
}