import { apiClient } from '../client'

// 本地定义类型，避免后端类型依赖
export interface MultimodalVideoGenerationRequest {
  prompt: string
  characterAvatar?: string
  chatId?: string
  messageId?: string
  characterId?: string // 添加 characterId 参数
  metadata?: {
    width?: number
    height?: number
    duration?: number // 视频时长(秒)
    fps?: number // 帧率
  }
}

export interface MultimodalVideoGenerationResponse {
  success: boolean
  data: {
    taskId: string
    messageId: string
    status: 'queued'
    pointsUsed: number
    remainingPoints: number
    estimatedTime?: number // 预计完成时间(秒)
  }
  message: string
}

interface MultimodalVideoGenerationErrorResponse {
  success: false
  error: string
  data?: {
    required?: number
    available?: number
  }
}

/**
 * 多模态视频生成状态
 */
export interface MultimodalVideoGenerationState {
  taskId: string
  currentStage: 'image_generation' | 'video_generation'
  overallProgress: number // 0-100 (整体进度)
  stageProgress: number // 当前阶段进度 0-100
  status: 'starting' | 'processing' | 'completed' | 'failed'
  intermediateImageUrl?: string // 第一阶段生成的图片
  finalVideoUrl?: string // 最终视频
  error?: string
  message?: string
}

/**
 * 多模态视频生成服务
 */
export class MultimodalVideoService {
  /**
   * 生成多模态视频 - 先生图，再生视频
   */
  async generateVideo(
    request: MultimodalVideoGenerationRequest
  ): Promise<MultimodalVideoGenerationResponse> {
    try {
      console.log('🎬 [START] 开始多模态视频生成:', {
        messageId: request.messageId,
        chatId: request.chatId,
        promptLength: request.prompt.length,
        hasCharacterAvatar: !!request.characterAvatar
      })

      const response = await apiClient.post<MultimodalVideoGenerationResponse>(
        '/api/app/multimodal-video-generation',
        request
      )

      if (response.success) {
        console.log('✅ [SUCCESS] 多模态视频生成任务已创建:', {
          taskId: response.data.taskId,
          pointsUsed: response.data.pointsUsed,
          remainingPoints: response.data.remainingPoints,
          estimatedTime: response.data.estimatedTime
        })
        return response
      } else {
        throw new Error('多模态视频生成失败')
      }
    } catch (error) {
      console.error('❌ [ERROR] 多模态视频生成API调用失败:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('多模态视频生成失败')
    }
  }

  /**
   * 取消多模态视频生成
   * 注意：由于是两阶段处理，取消逻辑比较复杂
   */
  async cancelGeneration(_taskId: string): Promise<boolean> {
    try {
      // 这里可以添加取消逻辑
      // 目前暂时返回 false，表示不支持取消
      console.warn('多模态视频生成暂不支持取消功能')
      return false
    } catch (error) {
      console.error('取消多模态视频生成失败:', error)
      return false
    }
  }

  /**
   * 验证视频URL是否有效
   */
  async validateVideoUrl(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, { method: 'HEAD' })
      return response.ok
    } catch (error) {
      console.error('验证视频URL失败:', error)
      return false
    }
  }

  /**
   * 获取视频元数据
   */
  async getVideoMetadata(_url: string): Promise<{
    duration?: number
    width?: number
    height?: number
    fileSize?: number
  } | null> {
    try {
      // 这里可以添加获取视频元数据的逻辑
      // 目前返回 null
      return null
    } catch (error) {
      console.error('获取视频元数据失败:', error)
      return null
    }
  }

  /**
   * 获取用户的多模态视频生成历史
   */
  async getGenerationHistory(options?: {
    limit?: number
    offset?: number
    characterId?: string
  }): Promise<{
    videos: Array<{
      id: string
      prompt: string
      status: string
      intermediateImageUrl?: string
      finalVideoUrl?: string
      createdAt: string
      metadata?: Record<string, any>
    }>
    total: number
    hasMore: boolean
  }> {
    try {
      const searchParams = new URLSearchParams()
      if (options?.limit) searchParams.set('limit', options.limit.toString())
      if (options?.offset) searchParams.set('offset', options.offset.toString())
      if (options?.characterId) searchParams.set('characterId', options.characterId)

      const url = `/api/app/multimodal-video-generation/history${
        searchParams.toString() ? `?${searchParams.toString()}` : ''
      }`

      return await apiClient.get<{
        videos: Array<{
          id: string
          prompt: string
          status: string
          intermediateImageUrl?: string
          finalVideoUrl?: string
          createdAt: string
          metadata?: Record<string, any>
        }>
        total: number
        hasMore: boolean
      }>(url)
    } catch (error) {
      console.error('获取多模态视频生成历史失败:', error)
      return {
        videos: [],
        total: 0,
        hasMore: false
      }
    }
  }

  /**
   * 解析消息附件中的多模态视频生成状态
   * 用于从 Supabase Realtime 消息更新中提取状态信息
   */
  parseGenerationStatusFromMessage(attachments: any[]): MultimodalVideoGenerationState | null {
    try {
      // 首先检查是否有完成的视频附件
      const completedVideoAttachment = attachments?.find(
        (att: any) => att.contentType === 'video/mp4' || att.contentType === 'video/webm'
      )

      if (completedVideoAttachment) {
        console.log('🎉 [SUCCESS] 检测到完成的视频附件:', completedVideoAttachment.url)
        return {
          taskId: completedVideoAttachment.metadata?.taskId || '',
          currentStage: 'video_generation',
          overallProgress: 100,
          stageProgress: 100,
          status: 'completed',
          finalVideoUrl: completedVideoAttachment.url,
          message: '视频生成完成'
        }
      }

      // 查找多模态视频生成状态附件
      const statusAttachment = attachments?.find(
        (att: any) =>
          att.contentType === 'video/generating' ||
          att.contentType === 'video/multimodal-generating'
      )

      if (!statusAttachment?.metadata) {
        return null
      }

      const metadata = statusAttachment.metadata

      return {
        taskId: metadata.taskId || '',
        currentStage: metadata.stage || 'image_generation',
        overallProgress: metadata.progress || 0,
        stageProgress: metadata.stageProgress || 0,
        status: metadata.status || 'starting',
        intermediateImageUrl: metadata.intermediateImageUrl,
        finalVideoUrl: metadata.finalVideoUrl,
        error: metadata.error,
        message: metadata.message
      }
    } catch (error) {
      console.error('解析多模态视频生成状态失败:', error)
      return null
    }
  }

  /**
   * 查询多模态视频生成状态
   */
  async getGenerationStatus(messageId: string): Promise<{
    success: boolean
    data?: {
      status: 'idle' | 'processing' | 'completed' | 'failed'
      progress: number
      stage: 'image_generation' | 'video_generation'
      message: string
      intermediateImageUrl?: string
      finalVideoUrl?: string
      error?: string
    }
    error?: string
  }> {
    try {
      console.log('🔍 [API] 查询多模态视频生成状态:', messageId)

      const response = await apiClient.get<{
        success: boolean
        data?: {
          status: 'idle' | 'processing' | 'completed' | 'failed'
          progress: number
          stage: 'image_generation' | 'video_generation'
          message: string
          intermediateImageUrl?: string
          finalVideoUrl?: string
          error?: string
        }
        error?: string
      }>(`/api/app/multimodal-video-generation/status/${messageId}`)

      console.log('📊 [API] 状态查询结果:', response)
      return response
    } catch (error) {
      console.error('❌ [API] 查询状态失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '查询状态失败'
      }
    }
  }
}

// 导出单例实例
export const multimodalVideoService = new MultimodalVideoService()
