import { apiClient } from '../client'

// 声音模型类型定义
export interface VoiceModel {
  id: string
  modelId: string
  name: string
  displayName: string
  description?: string
  gender: 'male' | 'female' | 'neutral'
  language: string
  supportedLanguages?: string[]
  category?: string
  tags?: string[]
  isActive: boolean
  isPremium: boolean
  sortOrder: number
  createdAt: string
  updatedAt: string
}

export interface VoiceSample {
  id: string
  voiceModelId: string
  language: string
  sampleText: string
  audioUrl?: string
  duration?: number
  fileSize?: number
  isDefault: boolean
  createdAt: string
}

export interface VoiceModelWithSample extends VoiceModel {
  defaultSample?: VoiceSample | null
}

// 声音选项（用于前端显示）
export interface VoiceOption {
  id?: string // 数据库ID
  value: string // modelId
  label: string // displayName
  description?: string
  gender: 'male' | 'female' | 'neutral'
  isPremium: boolean
  tags?: string[]
  emoji?: string
  defaultSample?: VoiceSample
}

// 声音数据缓存
class VoiceCache {
  private static instance: VoiceCache
  private cache: Map<string, any> = new Map()
  private cacheTimestamps: Map<string, number> = new Map()
  private readonly CACHE_DURATION = 30 * 60 * 1000 // 30分钟缓存

  static getInstance(): VoiceCache {
    if (!VoiceCache.instance) {
      VoiceCache.instance = new VoiceCache()
    }
    return VoiceCache.instance
  }

  set(key: string, data: any): void {
    this.cache.set(key, data)
    this.cacheTimestamps.set(key, Date.now())
  }

  get(key: string): any | null {
    const timestamp = this.cacheTimestamps.get(key)
    if (!timestamp || Date.now() - timestamp > this.CACHE_DURATION) {
      this.cache.delete(key)
      this.cacheTimestamps.delete(key)
      return null
    }
    return this.cache.get(key) || null
  }

  clear(): void {
    this.cache.clear()
    this.cacheTimestamps.clear()
  }

  // 用户退出时清除缓存
  clearOnLogout(): void {
    this.clear()
  }
}

const voiceCache = VoiceCache.getInstance()

// 声音模型映射（用于兼容旧的字符串值）
const VOICE_EMOJI_MAP: Record<string, string> = {
  alloy: '🕊️', // 柔和的
  echo: '🔊', // 低沉的
  fable: '🍬', // 甜美的
  onyx: '🥃', // 沙哑的
  nova: '⚡', // 有活力的
  shimmer: '💋' // 诱惑的
}

export const voicesService = {
  /**
   * 获取所有可用的声音模型
   */
  async getVoiceModels(withSamples = true): Promise<VoiceModelWithSample[]> {
    const cacheKey = `voice_models_${withSamples}`

    // 先检查缓存
    const cached = voiceCache.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const response = await apiClient.get<{
        success: boolean
        data: VoiceModelWithSample[]
      }>(`/api/app/voices?withSamples=${withSamples}`)

      if (response.success) {
        // 缓存数据
        voiceCache.set(cacheKey, response.data)
        return response.data
      }

      throw new Error('获取声音模型失败')
    } catch (error) {
      console.error('获取声音模型失败:', error)
      throw error
    }
  },

  /**
   * 根据性别获取声音模型（优化版：从全量数据中筛选）
   */
  async getVoiceModelsByGender(
    gender: 'male' | 'female' | 'neutral'
  ): Promise<VoiceModelWithSample[]> {
    const cacheKey = `voice_models_gender_${gender}`

    // 先检查缓存
    const cached = voiceCache.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      // 先获取所有带示例的声音模型
      const allModels = await this.getVoiceModels(true)

      // 按性别筛选
      const filteredModels = allModels.filter(model => model.gender === gender)

      // 缓存筛选结果
      voiceCache.set(cacheKey, filteredModels)

      return filteredModels
    } catch (error) {
      console.error('获取声音模型失败:', error)
      throw error
    }
  },

  /**
   * 获取声音模型详情
   */
  async getVoiceModelById(id: string, withSamples = true): Promise<VoiceModelWithSample | null> {
    const cacheKey = `voice_model_${id}_${withSamples}`

    // 先检查缓存
    const cached = voiceCache.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const response = await apiClient.get<{
        success: boolean
        data: VoiceModelWithSample
      }>(`/api/app/voices/${id}?withSamples=${withSamples}`)

      if (response.success) {
        // 缓存数据
        voiceCache.set(cacheKey, response.data)
        return response.data
      }

      return null
    } catch (error) {
      console.error('获取声音模型详情失败:', error)
      return null
    }
  },

  /**
   * 获取声音示例
   */
  async getVoiceSamples(voiceModelId: string, language?: string): Promise<VoiceSample[]> {
    const cacheKey = `voice_samples_${voiceModelId}_${language || 'all'}`

    // 先检查缓存
    const cached = voiceCache.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const url = language
        ? `/api/app/voices/${voiceModelId}/samples?language=${language}`
        : `/api/app/voices/${voiceModelId}/samples`

      const response = await apiClient.get<{
        success: boolean
        data: VoiceSample[]
      }>(url)

      if (response.success) {
        // 缓存数据
        voiceCache.set(cacheKey, response.data)
        return response.data
      }

      return []
    } catch (error) {
      console.error('获取声音示例失败:', error)
      return []
    }
  },

  /**
   * 将声音模型转换为前端选项格式
   */
  convertToVoiceOptions(models: VoiceModelWithSample[]): VoiceOption[] {
    return models.map(model => ({
      id: model.id, // 数据库ID
      value: model.modelId,
      label: model.displayName,
      description: model.description,
      gender: model.gender,
      isPremium: model.isPremium,
      tags: model.tags,
      emoji: VOICE_EMOJI_MAP[model.modelId] || '🎵',
      defaultSample: model.defaultSample || undefined
    }))
  },

  /**
   * 清除缓存（用户退出时调用）
   */
  clearCache(): void {
    voiceCache.clearOnLogout()
  },

  /**
   * 预加载声音数据
   */
  async preloadVoiceData(): Promise<void> {
    try {
      // 预加载所有声音模型
      await this.getVoiceModels(true)

      // 预加载男性和女性声音模型
      await Promise.all([
        this.getVoiceModelsByGender('male'),
        this.getVoiceModelsByGender('female')
      ])
    } catch (error) {
      console.error('预加载声音数据失败:', error)
    }
  }
}

// 导出缓存实例供其他地方使用
export { voiceCache }
