import { apiClientV2, type ApiResponse } from '../clientv2'

// 会员计划接口
export interface MembershipPlan {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  currency: string
  durationDays: number
  pointsIncluded: number
  features:
    | {
        maxCharacters?: number
        canCreatePublicCharacters?: boolean
        canUseCustomVoices?: boolean
        canAccessPremiumTemplates?: boolean
      }
    | string[] // 兼容旧数据格式
  isActive: boolean
  sortOrder: number
  createdAt: string
  updatedAt: string
}

// 用户订阅接口
export interface UserSubscription {
  id: string
  userId: string
  planId: string
  startDate: string
  endDate: string
  status: 'active' | 'expired' | 'cancelled'
  paymentId?: string
  createdAt: string
  updatedAt: string
}

// 用户积分接口
export interface UserPoints {
  userId: string
  totalPoints: number
  usedPoints: number
  availablePoints: number
  lastUpdated: string
}

// 积分交易记录接口
export interface PointsTransaction {
  id: string
  userId: string
  transactionType: 'earn' | 'spend'
  amount: number
  source: 'subscription' | 'purchase' | 'generation' | 'refund' | 'bonus' | 'admin'
  sourceId?: string
  description?: string
  balanceAfter: number
  createdAt: string
}

// 积分套餐接口
export interface PointsPackage {
  id: string
  name: string
  description: string
  points: number
  price: number
  originalPrice?: number
  currency: string
  bonusPoints: number
  isActive: boolean
  sortOrder: number
  createdAt: string
  updatedAt: string
}

// 会员状态接口
export interface MembershipStatus {
  membership: {
    isMember: boolean
    subscription: UserSubscription | null
    plan: MembershipPlan | null
  }
  points: UserPoints
  currentSubscription: {
    id: string
    planId: string
    startDate: string
    endDate: string
    status: string
    daysRemaining: number
  } | null
  permissions: {
    canAccessPremiumTemplates: boolean
    canUseAdvancedFeatures: boolean
    hasUnlimitedGenerations: boolean
  }
}

// 创建订阅请求接口
export interface CreateSubscriptionRequest {
  planId: string
  paymentId?: string
  autoRenew?: boolean
}

// 消费积分请求接口
export interface ConsumePointsRequest {
  feature: string
  sourceId?: string
  customAmount?: number
  description?: string
}

// 权限检查请求接口
export interface CheckPermissionRequest {
  permissionType:
    | 'text_chat'
    | 'character_create'
    | 'image_generation'
    | 'voice_generation'
    | 'script_purchase'
    | 'video_generation'
    | 'gallery_generation'
    | 'member_only'
  pointsCost?: number
  requireMembership?: boolean
}

// 会员服务类
class MembershipService {
  // ==================== 会员计划相关 ====================

  /**
   * 获取所有会员计划
   */
  async getPlans(): Promise<ApiResponse<MembershipPlan[]>> {
    return apiClientV2.get('/api/app/membership/plans')
  }

  /**
   * 获取指定会员计划详情
   */
  async getPlanById(id: string): Promise<ApiResponse<MembershipPlan>> {
    return apiClientV2.get(`/api/app/membership/plans/${id}`)
  }

  // ==================== 用户订阅相关 ====================

  /**
   * 获取用户当前订阅状态
   */
  async getSubscription(): Promise<ApiResponse<UserSubscription | null>> {
    return apiClientV2.get('/api/app/membership/subscription')
  }

  /**
   * 创建新的订阅
   */
  async createSubscription(
    data: CreateSubscriptionRequest
  ): Promise<ApiResponse<UserSubscription>> {
    return apiClientV2.post('/api/app/membership/subscription', data)
  }

  /**
   * 获取用户订阅历史
   */
  async getSubscriptionHistory(): Promise<ApiResponse<UserSubscription[]>> {
    return apiClientV2.get('/api/app/membership/subscription/history')
  }

  // ==================== 积分系统相关 ====================

  /**
   * 获取用户积分余额
   */
  async getPoints(): Promise<ApiResponse<UserPoints>> {
    return apiClientV2.get('/api/app/membership/points')
  }

  /**
   * 获取用户积分交易记录
   */
  async getPointsTransactions(limit = 50): Promise<ApiResponse<PointsTransaction[]>> {
    return apiClientV2.get(`/api/app/membership/points/transactions?limit=${limit}`)
  }

  /**
   * 消费积分
   */
  async spendPoints(data: {
    amount: number
    source: string
    sourceId?: string
    description?: string
  }): Promise<ApiResponse<UserPoints>> {
    return apiClientV2.post('/api/app/membership/points/spend', data)
  }

  /**
   * 消费积分（用于各种功能）
   */
  async consumePoints(
    data: ConsumePointsRequest
  ): Promise<ApiResponse<{ consumed: number; remainingPoints: number; transactionId: string }>> {
    return apiClientV2.post('/api/app/membership/consume-points', data)
  }

  // ==================== 积分套餐相关 ====================

  /**
   * 获取所有积分套餐
   */
  async getPointsPackages(): Promise<ApiResponse<PointsPackage[]>> {
    return apiClientV2.get('/api/app/membership/points/packages')
  }

  /**
   * 获取指定积分套餐详情
   */
  async getPointsPackageById(id: string): Promise<ApiResponse<PointsPackage>> {
    return apiClientV2.get(`/api/app/membership/points/packages/${id}`)
  }

  // ==================== 会员状态相关 ====================

  /**
   * 获取用户完整的会员状态信息
   */
  async getMembershipStatus(): Promise<ApiResponse<MembershipStatus>> {
    return apiClientV2.get('/api/app/membership/status')
  }

  // ==================== 权限检查相关 ====================

  /**
   * 检查用户是否有权限执行特定操作
   */
  async checkPermission(data: CheckPermissionRequest): Promise<
    ApiResponse<{
      hasPermission: boolean
      reason?: string
      userType: string
      pointsAvailable: number
      membershipInfo: any
    }>
  > {
    return apiClientV2.post('/api/app/membership/check-permission', data)
  }

  /**
   * 验证用户对特定功能的访问权限
   */
  async verifyAccess(feature: string): Promise<
    ApiResponse<{
      canAccess: boolean
      reason?: string
      pointsRequired: number
      userType: string
      pointsAvailable: number
      isMember: boolean
      // 角色创建特有字段
      currentCount?: number
      maxAllowed?: number
      characterLimitInfo?: {
        current: number
        max: number
        canCreate: boolean
      }
    }>
  > {
    return apiClientV2.get(`/api/app/membership/verify-access/${feature}`, {
      silentError: true
    })
  }

  /**
   * 获取功能积分消费配置
   */
  async getFeaturesConfig(): Promise<
    ApiResponse<{
      pointsCosts: Record<string, number>
      features: Record<
        string,
        {
          name: string
          description: string
          pointsCost: number
          requireMembership: boolean
        }
      >
    }>
  > {
    return apiClientV2.get('/api/app/membership/features/config')
  }
}

// 导出会员服务实例
export const membershipService = new MembershipService()
