import { apiClient } from '../client'

// ==================== 类型定义 ====================

// 邀请码信息
export interface InviteCode {
  id: string
  code: string
  userId: string
  maxUses: number | null
  usedCount: number
  expiresAt: string | null
  isActive: boolean
  createdAt: string
  updatedAt: string
  inviterEmail?: string
}

// 邀请统计
export interface InviteStats {
  totalInvites: number
  successfulInvites: number
  pendingInvites: number
  totalCommissionEarned: string
}

// 佣金账户
export interface CommissionAccount {
  totalEarned: string
  availableBalance: string
  frozenBalance: string
  totalWithdrawn: string
}

// 佣金记录
export interface CommissionRecord {
  id: string
  userId: string
  referredUserId: string
  orderId: string
  orderType: 'membership' | 'points'
  orderAmount: string
  commissionRate: string
  commissionAmount: string
  status: 'pending' | 'settled' | 'cancelled'
  settledAt: string | null
  createdAt: string
  referredUserEmail?: string
}

// 邀请用户信息
export interface InvitedUser {
  id: string
  email: string
  registeredAt: string
  totalSpent: string
  commissionEarned: string
}

// 提现申请
export interface WithdrawRequest {
  id: string
  userId: string
  amount: string
  feeAmount: string
  actualAmount: string
  status: 'pending' | 'approved' | 'rejected' | 'completed'
  bankInfo: {
    bankName: string
    accountName: string
    accountNumber: string
    phone?: string
  }
  createdAt: string
  processedAt: string | null
  remarks?: string
}

// 提现配置
export interface WithdrawConfig {
  minWithdrawAmount: string
  withdrawFeeRate: string
  feeDescription: string
}

// API响应类型
export interface ReferralApiResponse<T> {
  success: boolean
  data: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  list: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// ==================== API 服务 ====================

export const referralService = {
  // ==================== 邀请码管理 ====================
  
  /**
   * 生成邀请码
   */
  async generateInviteCode(): Promise<ReferralApiResponse<InviteCode>> {
    try {
      return await apiClient.post<ReferralApiResponse<InviteCode>>('/api/app/referral/invite-code/generate')
    } catch (error) {
      return {
        success: false,
        data: {} as InviteCode,
        error: error instanceof Error ? error.message : '生成邀请码失败'
      }
    }
  },

  /**
   * 获取我的邀请码信息
   */
  async getMyInviteCode(): Promise<ReferralApiResponse<{
    inviteCode: InviteCode | null
    stats: InviteStats
    commissionAccount: CommissionAccount | null
  }>> {
    try {
      return await apiClient.get<ReferralApiResponse<{
        inviteCode: InviteCode | null
        stats: InviteStats
        commissionAccount: CommissionAccount | null
      }>>('/api/app/referral/invite-code/my')
    } catch (error) {
      return {
        success: false,
        data: {
          inviteCode: null,
          stats: {
            totalInvites: 0,
            successfulInvites: 0,
            pendingInvites: 0,
            totalCommissionEarned: '0.00'
          },
          commissionAccount: null
        },
        error: error instanceof Error ? error.message : '获取邀请码信息失败'
      }
    }
  },

  /**
   * 验证邀请码
   */
  async validateInviteCode(code: string): Promise<ReferralApiResponse<{
    id: string
    code: string
    inviterEmail: string
  }>> {
    try {
      return await apiClient.post<ReferralApiResponse<{
        id: string
        code: string
        inviterEmail: string
      }>>('/api/app/referral/invite-code/validate', { code })
    } catch (error) {
      return {
        success: false,
        data: {} as any,
        error: error instanceof Error ? error.message : '验证邀请码失败'
      }
    }
  },

  /**
   * 获取我邀请的用户列表
   */
  async getInvitedUsers(page: number = 1, limit: number = 10): Promise<ReferralApiResponse<PaginatedResponse<InvitedUser>>> {
    try {
      return await apiClient.get<ReferralApiResponse<PaginatedResponse<InvitedUser>>>(
        `/api/app/referral/invites?page=${page}&limit=${limit}`
      )
    } catch (error) {
      return {
        success: false,
        data: {
          list: [],
          pagination: { page, limit, total: 0, totalPages: 0 }
        },
        error: error instanceof Error ? error.message : '获取邀请列表失败'
      }
    }
  },

  // ==================== 佣金管理 ====================

  /**
   * 获取佣金账户信息
   */
  async getCommissionAccount(): Promise<ReferralApiResponse<{
    account: CommissionAccount
    stats: any
  }>> {
    try {
      return await apiClient.get<ReferralApiResponse<{
        account: CommissionAccount
        stats: any
      }>>('/api/app/referral/commission/account')
    } catch (error) {
      return {
        success: false,
        data: {
          account: {
            totalEarned: '0.00',
            availableBalance: '0.00',
            frozenBalance: '0.00',
            totalWithdrawn: '0.00'
          },
          stats: {}
        },
        error: error instanceof Error ? error.message : '获取佣金账户失败'
      }
    }
  },

  /**
   * 获取佣金记录
   */
  async getCommissionRecords(page: number = 1, limit: number = 10): Promise<ReferralApiResponse<PaginatedResponse<CommissionRecord>>> {
    try {
      return await apiClient.get<ReferralApiResponse<PaginatedResponse<CommissionRecord>>>(
        `/api/app/referral/commission/records?page=${page}&limit=${limit}`
      )
    } catch (error) {
      return {
        success: false,
        data: {
          list: [],
          pagination: { page, limit, total: 0, totalPages: 0 }
        },
        error: error instanceof Error ? error.message : '获取佣金记录失败'
      }
    }
  },

  /**
   * 获取统计数据
   */
  async getStatistics(): Promise<ReferralApiResponse<InviteStats & { commissionStats: any }>> {
    try {
      return await apiClient.get<ReferralApiResponse<InviteStats & { commissionStats: any }>>(
        '/api/app/referral/statistics'
      )
    } catch (error) {
      return {
        success: false,
        data: {
          totalInvites: 0,
          successfulInvites: 0,
          pendingInvites: 0,
          totalCommissionEarned: '0.00',
          commissionStats: {}
        },
        error: error instanceof Error ? error.message : '获取统计数据失败'
      }
    }
  },

  // ==================== 提现管理 ====================

  /**
   * 获取提现配置
   */
  async getWithdrawConfig(): Promise<ReferralApiResponse<WithdrawConfig>> {
    try {
      return await apiClient.get<ReferralApiResponse<WithdrawConfig>>('/api/app/referral/withdraw/config')
    } catch (error) {
      return {
        success: false,
        data: {
          minWithdrawAmount: '0',
          withdrawFeeRate: '0',
          feeDescription: ''
        },
        error: error instanceof Error ? error.message : '获取提现配置失败'
      }
    }
  },

  /**
   * 申请提现
   */
  async applyWithdraw(data: {
    amount: number
    bankInfo: {
      bankName: string
      accountName: string
      accountNumber: string
      phone?: string
    }
  }): Promise<ReferralApiResponse<{
    amount: string
    feeAmount: string
    actualAmount: string
    status: string
  }>> {
    try {
      return await apiClient.post<ReferralApiResponse<{
        amount: string
        feeAmount: string
        actualAmount: string
        status: string
      }>>('/api/app/referral/withdraw/apply', data)
    } catch (error) {
      return {
        success: false,
        data: {} as any,
        error: error instanceof Error ? error.message : '提现申请失败'
      }
    }
  },

  /**
   * 获取提现记录
   */
  async getWithdrawRecords(page: number = 1, limit: number = 10): Promise<ReferralApiResponse<PaginatedResponse<WithdrawRequest>>> {
    try {
      return await apiClient.get<ReferralApiResponse<PaginatedResponse<WithdrawRequest>>>(
        `/api/app/referral/withdraw/records?page=${page}&limit=${limit}`
      )
    } catch (error) {
      return {
        success: false,
        data: {
          list: [],
          pagination: { page, limit, total: 0, totalPages: 0 }
        },
        error: error instanceof Error ? error.message : '获取提现记录失败'
      }
    }
  }
}

export default referralService
