import { apiClient, type ApiResponse } from '../client'

// 媒体生成记录类型
export interface MediaGeneration {
  id: string
  characterId?: string
  chatId?: string
  messageId?: string
  mediaType: 'image' | 'video' | 'audio'
  generationType: 'multimodal_chat' | 'standalone' | 'template_based'
  prompt?: string
  negativePrompt?: string
  inputImageUrl?: string
  outputUrls: string[]
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  errorMessage?: string
  pointsUsed: number
  generationTime?: number
  completedAt?: string
  createdAt: string
  updatedAt: string
  metadata?: any
}

// 图片记录类型
export interface ImageRecord {
  id: string
  url: string
  prompt?: string
  createdAt: string
  generationType: string
  metadata?: any
}

// 视频记录类型
export interface VideoRecord {
  id: string
  url: string
  prompt?: string
  createdAt: string
  generationType: string
  metadata?: any
}

export interface MediaGenerations {
  mediaGenerations: MediaGeneration[]
  total: number
  hasMore: boolean
}

// API 响应类型
export interface MediaGenerationsResponse {
  success: boolean
  data: MediaGenerations
}

export interface Images {
  images: ImageRecord[]
  total: number
  hasMore: boolean
}

export interface ImagesResponse {
  success: boolean
  data: Images
}

export interface Videos {
  videos: VideoRecord[]
  total: number
  hasMore: boolean
}

export interface VideosResponse {
  success: boolean
  data: Videos
}

// 角色媒体 API 服务
export const characterMediaService = {
  /**
   * 获取角色的所有媒体生成记录
   */
  async getCharacterMedia(
    characterId: string,
    options?: {
      mediaType?: 'image' | 'video' | 'audio'
      generationType?: 'multimodal_chat' | 'standalone' | 'template_based'
      status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
      limit?: number
      offset?: number
    }
  ): Promise<MediaGenerations> {
    try {
      const queryParams = new URLSearchParams()

      if (options?.mediaType) {
        queryParams.append('mediaType', options.mediaType)
      }
      if (options?.generationType) {
        queryParams.append('generationType', options.generationType)
      }
      if (options?.status) {
        queryParams.append('status', options.status)
      }
      if (options?.limit) {
        queryParams.append('limit', options.limit.toString())
      }
      if (options?.offset) {
        queryParams.append('offset', options.offset.toString())
      }

      const endpoint = `/api/app/character-media/${characterId}${
        queryParams.toString() ? `?${queryParams.toString()}` : ''
      }`

      const response = await apiClient.get<MediaGenerationsResponse>(endpoint)

      return response.data
    } catch (error) {
      console.error('获取角色的媒体记录失败:', error)
      throw error
    }
  },

  /**
   * 获取角色的图片生成记录
   */
  async getCharacterImages(
    characterId: string,
    options?: {
      limit?: number
      offset?: number
    }
  ): Promise<Images> {
    try {
      const queryParams = new URLSearchParams()

      if (options?.limit) {
        queryParams.append('limit', options.limit.toString())
      }
      if (options?.offset) {
        queryParams.append('offset', options.offset.toString())
      }

      const endpoint = `/api/app/character-media/${characterId}/images${
        queryParams.toString() ? `?${queryParams.toString()}` : ''
      }`

      const response = await apiClient.get<ImagesResponse>(endpoint)

      return response.data
    } catch (error) {
      console.error('获取角色的图片记录失败:', error)
      throw error
    }
  },

  /**
   * 获取角色的视频生成记录
   */
  async getCharacterVideos(
    characterId: string,
    options?: {
      limit?: number
      offset?: number
    }
  ): Promise<Videos> {
    try {
      const queryParams = new URLSearchParams()

      if (options?.limit) {
        queryParams.append('limit', options.limit.toString())
      }
      if (options?.offset) {
        queryParams.append('offset', options.offset.toString())
      }

      const endpoint = `/api/app/character-media/${characterId}/videos${
        queryParams.toString() ? `?${queryParams.toString()}` : ''
      }`

      const response = await apiClient.get<VideosResponse>(endpoint)

      return response.data
    } catch (error) {
      console.error('获取角色的视频记录失败:', error)
      throw error
    }
  }
}
