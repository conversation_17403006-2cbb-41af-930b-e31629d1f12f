import { imageService } from './image'

/**
 * 图片生成服务 - RunPod 版本
 * 直接使用 RunPod API 进行图片生成
 */
export const imageGenerationService = {
  /**
   * 生成图片（RunPod）
   */
  async generateImage(params: {
    messageId: string
    chatId: string
    prompt: string
    characterAvatar?: string
    characterId?: string
    metadata?: {
      width?: number
      height?: number
    }
  }) {
    const { messageId, chatId, prompt, characterAvatar, characterId, metadata } = params

    console.log('🎨 [RunPod] 开始图片生成:', {
      messageId,
      chatId,
      promptLength: prompt.length,
      hasCharacterAvatar: !!characterAvatar
    })

    try {
      console.log('🚀 [RunPod] 调用 RunPod API...')

      const runpodResponse = await imageService.generateImageRunpod({
        messageId,
        chatId,
        prompt,
        characterAvatar,
        characterId,
        metadata: metadata || {
          width: 1024,
          height: 1440
        }
      })

      if (runpodResponse.success) {
        console.log('✅ [RunPod] RunPod API 调用成功:', {
          taskId: runpodResponse.taskId,
          message: runpodResponse.message
        })
        return {
          success: true,
          source: 'runpod' as const,
          data: runpodResponse
        }
      } else {
        throw new Error(runpodResponse.message || '图片生成失败')
      }
    } catch (error) {
      console.error('❌ [RunPod] RunPod 图片生成失败:', error)
      throw new Error('图片生成服务暂时不可用，请稍后重试')
    }
  },

  /**
   * 查询 RunPod 图片生成状态（通过 messageId）
   */
  async getGenerationStatus(messageId: string): Promise<{
    success: boolean
    data?: {
      status: 'idle' | 'processing' | 'completed' | 'failed'
      progress: number
      stage: 'image_generation'
      message: string
      finalImageUrl?: string
      error?: string
    }
    error?: string
  }> {
    try {
      console.log('🔍 [RunPod] 查询图片生成状态:', messageId)

      const { apiClient } = await import('../client')
      const response = await apiClient.get<{
        success: boolean
        data?: {
          status: 'idle' | 'processing' | 'completed' | 'failed'
          progress: number
          stage: 'image_generation'
          message: string
          finalImageUrl?: string
          error?: string
        }
        error?: string
      }>(`/api/app/image-generation/status/${messageId}`)

      console.log('📊 [RunPod] 状态查询结果:', response)
      return response
    } catch (error) {
      console.error('❌ [RunPod] 查询状态失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '查询状态失败'
      }
    }
  }
}
