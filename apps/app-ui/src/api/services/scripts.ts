import { apiClientV2, type ApiResponse } from '../clientv2'

// 剧本数据类型
export interface Script {
  id: string
  title: string
  description: string
  coverImage: string
  duration: string
  tags: string[]
  category?: string
  content?: any
  isPublic: boolean
  isActive: boolean
  isPremium: boolean
  pointsCost: number
  usageCount: number
  rating?: string
  ratingCount: number
  createdBy?: string
  createdAt: string
  updatedAt: string
}

// 剧本分类类型
export interface ScriptCategory {
  category: string
  count: number
}

// 剧本使用记录类型
export interface ScriptUsage {
  id: string
  userId: string
  scriptId: string
  chatId?: string
  duration?: number
  rating?: number
  feedback?: string
  createdAt: string
}

// 分页信息类型
export interface PaginationInfo {
  limit: number
  offset: number
  total: number
}

// 获取剧本列表的查询参数
export interface GetScriptsParams {
  limit?: number
  offset?: number
  category?: string
  search?: string
  sortBy?: 'latest' | 'popular' | 'rating'
}

// 创建剧本的数据
export interface CreateScriptData {
  title: string
  description: string
  coverImage: string
  duration: string
  tags?: string[]
  category?: string
  content?: any
  isPublic?: boolean
  isPremium?: boolean
}

// 更新剧本的数据
export interface UpdateScriptData {
  title?: string
  description?: string
  coverImage?: string
  duration?: string
  tags?: string[]
  category?: string
  content?: any
  isPublic?: boolean
  isPremium?: boolean
  isActive?: boolean
}

// 创建剧本使用记录的数据
export interface CreateScriptUsageData {
  scriptId: string
  chatId?: string
  duration?: number
  rating?: number
  feedback?: string
}

// 剧本API服务类
export class ScriptService {
  /**
   * 获取公开剧本列表
   */
  static async getPublicScripts(params: GetScriptsParams = {}): Promise<ApiResponse<Script[]>> {
    const queryParams = new URLSearchParams()

    if (params.limit) queryParams.append('limit', params.limit.toString())
    if (params.offset) queryParams.append('offset', params.offset.toString())
    if (params.category) queryParams.append('category', params.category)
    if (params.search) queryParams.append('search', params.search)
    if (params.sortBy) queryParams.append('sortBy', params.sortBy)

    const queryString = queryParams.toString()
    const endpoint = `/api/app/scripts${queryString ? `?${queryString}` : ''}`

    return apiClientV2.get<Script[]>(endpoint)
  }

  /**
   * 获取剧本分类列表
   */
  static async getScriptCategories(): Promise<ApiResponse<ScriptCategory[]>> {
    return apiClientV2.get<ScriptCategory[]>('/api/app/scripts/categories')
  }

  /**
   * 根据ID获取剧本详情
   */
  static async getScriptById(id: string): Promise<ApiResponse<Script>> {
    return apiClientV2.get<Script>(`/api/app/scripts/${id}`)
  }

  /**
   * 获取剧本详细内容（包含阶段、对话等）
   */
  static async getScriptContent(id: string): Promise<
    ApiResponse<{
      id: string
      title: string
      content: any
      audioUrl: string
      totalDuration: number
      stageCount: number
    }>
  > {
    return apiClientV2.get<{
      id: string
      title: string
      content: any
      audioUrl: string
      totalDuration: number
      stageCount: number
    }>(`/api/app/scripts/${id}/content`)
  }

  /**
   * 创建新剧本
   */
  static async createScript(data: CreateScriptData): Promise<ApiResponse<Script>> {
    return apiClientV2.post<Script>('/api/app/scripts', data)
  }

  /**
   * 更新剧本
   */
  static async updateScript(id: string, data: UpdateScriptData): Promise<ApiResponse<Script>> {
    return apiClientV2.put<Script>(`/api/app/scripts/${id}`, data)
  }

  /**
   * 删除剧本（软删除）
   */
  static async deleteScript(id: string): Promise<ApiResponse<void>> {
    return apiClientV2.delete<void>(`/api/app/scripts/${id}`)
  }

  /**
   * 使用剧本（增加使用次数）
   */
  static async useScript(id: string): Promise<ApiResponse<void>> {
    return apiClientV2.post<void>(`/api/app/scripts/${id}/use`)
  }

  /**
   * 创建剧本使用记录
   */
  static async createScriptUsage(data: CreateScriptUsageData): Promise<ApiResponse<ScriptUsage>> {
    return apiClientV2.post<ScriptUsage>('/api/app/scripts/usage', data)
  }

  /**
   * 获取用户的剧本使用历史
   */
  static async getUserScriptUsageHistory(
    params: {
      limit?: number
      offset?: number
    } = {}
  ): Promise<ApiResponse<Array<{ usage: ScriptUsage; script: Script }>>> {
    const queryParams = new URLSearchParams()

    if (params.limit) queryParams.append('limit', params.limit.toString())
    if (params.offset) queryParams.append('offset', params.offset.toString())

    const queryString = queryParams.toString()
    const endpoint = `/api/app/scripts/usage/history${queryString ? `?${queryString}` : ''}`

    return apiClientV2.get<Array<{ usage: ScriptUsage; script: Script }>>(endpoint)
  }
}

// 导出默认实例
export const scriptService = ScriptService
