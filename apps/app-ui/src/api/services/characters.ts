import { apiClient } from '../client'

// 角色数据类型
export interface CharacterData {
  id: string
  name: string
  description?: string
  relationship?: string
  ethnicity?: string
  gender?: string
  age?: number
  eyeColor?: string
  hairStyle?: string
  hairColor?: string
  faceShape?: string // 添加脸型字段
  bodyType?: string
  breastSize?: string
  buttSize?: string
  personality?: string
  clothing?: string
  voice?: string
  voiceModelId?: string // 声音模型ID
  imageUrl?: string
  userId: string
  createdAt: string
  updatedAt: string
}

// 角色类型（兼容性）
export interface Role {
  role: string
  character: string
  initialScene: string
  description: string
  age: number
  avatar?: string
}

// 角色 API 服务
export const charactersService = {
  // 获取系统角色（公开角色）
  async getSystemRole(roleId: string) {
    // 先获取公开角色列表，然后找到指定ID的角色
    const response = await apiClient.get<{ success: boolean; data: CharacterData[] }>(
      '/api/app/characters/public'
    )
    if (response.success) {
      const character = response.data.find(char => char.id === roleId)
      if (character) {
        // 转换为Role格式以保持兼容性
        return {
          role: character.name,
          character: character.name,
          initialScene: character.personality || '',
          description: character.description || character.personality || '',
          age: character.age || 25,
          avatar: character.imageUrl
        } as Role
      }
    }
    throw new Error('角色不存在')
  },

  // 获取公开角色列表
  async getPublicCharacters() {
    const response = await apiClient.get<{ success: boolean; data: CharacterData[] }>(
      '/api/app/characters/public'
    )
    if (response.success) {
      return { characters: response.data }
    }
    return { characters: [] }
  },

  // 获取系统角色列表
  async getSystemCharacters() {
    const response = await apiClient.get<{ success: boolean; data: CharacterData[] }>(
      '/api/app/characters/system'
    )
    if (response.success) {
      return { characters: response.data }
    }
    return { characters: [] }
  },

  // 获取自定义角色
  async getUserCharacters() {
    const response = await apiClient.get<{ success: boolean; data: CharacterData[] }>(
      '/api/app/characters'
    )
    if (response.success) {
      return { characters: response.data }
    }
    return { characters: [] }
  },

  // 获取单个角色详情
  async getCharacter(characterId: string) {
    const response = await apiClient.get<{ success: boolean; data: CharacterData }>(
      `/api/app/characters/${characterId}`
    )
    if (response.success) {
      return { character: response.data }
    }
    throw new Error('角色不存在')
  },

  // 创建自定义角色
  async createCharacter(data: {
    characterData: {
      name: string
      gender: string
      relationship: string
      ethnicity: string
      age: string
      eyeColor: string
      hairStyle: string
      hairColor: string
      bodyType: string
      breastSize?: string
      buttSize?: string
      personality: string
      clothing: string
      voice: string
      voiceModelId?: string // 声音模型ID
    }
    keywords: string
    prompt: string
    imageUrl: string
  }) {
    const requestData = {
      ...data.characterData,
      keywords: data.keywords,
      prompt: data.prompt,
      imageUrl: data.imageUrl,
      age: data.characterData.age // 保持字符串格式
    }

    const response = await apiClient.post<{ success: boolean; data: CharacterData }>(
      '/api/app/characters',
      requestData
    )
    if (response.success) {
      return { character: response.data }
    }
    throw new Error('创建角色失败')
  },

  // 更新角色
  async updateCharacter(characterId: string, data: Partial<CharacterData>) {
    const response = await apiClient.put<{ success: boolean; data: CharacterData }>(
      `/api/app/characters/${characterId}`,
      data
    )
    if (response.success) {
      return { character: response.data }
    }
    throw new Error('更新角色失败')
  },

  // 删除角色
  async deleteCharacter(characterId: string) {
    const response = await apiClient.delete<{ success: boolean }>(`/api/app/characters/${characterId}`)
    if (response.success) {
      return { success: true }
    }
    throw new Error('删除角色失败')
  }
}
