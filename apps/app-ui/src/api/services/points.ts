/**
 * 积分商城API服务
 */

import { apiClient } from '../client'
import type { ApiResponse } from '../client'

// 积分包接口
export interface PointsPackage {
  id: string
  name: string
  description?: string
  points: number
  price: string
  bonusPoints: number
  isActive: boolean
  sortOrder: number
  createdAt: string
  updatedAt: string
}

// 积分包购买订单接口
export interface PointsOrderResult {
  orderId: string
  paymentId: string
  redirectUrl: string
  amount: number
  originalAmount: number
  planName: string
  description: string
  paymentMethod: string
  isUpgrade: boolean
  savings: number
  expiresAt: string
  packageInfo: {
    id: string
    name: string
    points: number
    bonusPoints: number
    totalPoints: number
  }
}

// 创建积分包购买请求
export interface CreatePointsOrderRequest {
  description?: string
  paymentMethod?: 'alipay' | 'wechat'
  metadata?: Record<string, any>
}

export const pointsService = {
  /**
   * 获取积分包列表
   */
  async getPackages(): Promise<ApiResponse<PointsPackage[]>> {
    return apiClient.get('/api/app/points/packages')
  },

  /**
   * 获取单个积分包详情
   */
  async getPackageById(packageId: string): Promise<ApiResponse<PointsPackage>> {
    return apiClient.get(`/api/app/points/packages/${packageId}`)
  },

  /**
   * 创建积分包购买订单
   */
  async purchasePackage(
    packageId: string, 
    data: CreatePointsOrderRequest
  ): Promise<ApiResponse<PointsOrderResult>> {
    return apiClient.post(`/api/app/points/packages/${packageId}/purchase`, data)
  }
}