import { isCapacitorEnvironment } from '@/lib/utils'
import { addToast } from '@heroui/react'
import i18n from '@/i18n'
import { getCurrentLanguage, formatAcceptLanguage } from '@/config/i18n'
import { getAppApiUrl } from '@/utils/check-version'

// API基础URL配置 - 与原client保持一致
const API_URL = isCapacitorEnvironment() ? getAppApiUrl() : import.meta.env.VITE_WEB_API_URL

// 统一的后端响应格式
export interface ApiSuccessResponse<T = any> {
  success: true
  data: T
  message?: string
  timestamp?: string
  requestId?: string
}

export interface ApiErrorResponse {
  success: false
  error: string
  code?: string
  data?: any
  timestamp?: string
  requestId?: string
}

export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse

// API请求选项接口 - 与原client保持一致
interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  body?: unknown
  credentials?: RequestCredentials
  requireAuth?: boolean // 是否需要认证
  silentError?: boolean // 是否静默处理错误（不显示 toast）
}

// API错误类
export class ApiError extends Error {
  constructor(message: string, public status: number, public code?: string, public data?: any) {
    super(message)
    this.name = 'ApiError'
  }
}

// 获取访问令牌 - 与原client保持一致
const getAccessToken = (): string | null => {
  return localStorage.getItem('access_token')
}

// API客户端类
class ApiClientV2 {
  // 获取翻译文本的辅助方法 - 与原client保持一致
  private getTranslation(key: string, options?: any): string {
    try {
      const result = i18n.t(key, options)
      return typeof result === 'string' ? result : key
    } catch (error) {
      console.warn(`Translation key not found: ${key}`)
      return key
    }
  }

  // 显示错误提示的统一方法 - 与原client保持一致，优先使用后端消息
  private showErrorToast(errorMessage: string, status: number, silentError?: boolean) {
    // 如果设置了静默错误，则不显示 toast
    if (silentError) {
      return
    }

    // 根据状态码显示不同的错误提示，优先使用后端返回的errorMessage
    if (status === 401) {
      addToast({
        title: this.getTranslation('toast:api.unauthorized'),
        description: errorMessage || this.getTranslation('toast:api.unauthorized_description'),
        color: 'warning'
      })
    } else if (status === 403) {
      addToast({
        title: this.getTranslation('toast:api.forbidden'),
        description: errorMessage || this.getTranslation('toast:api.forbidden_description'),
        color: 'warning'
      })
    } else if (status === 404) {
      addToast({
        title: this.getTranslation('toast:api.not_found'),
        description: errorMessage || this.getTranslation('toast:api.not_found_description'),
        color: 'warning'
      })
    } else if (status >= 500) {
      addToast({
        title: this.getTranslation('toast:api.server_error'),
        description: errorMessage || this.getTranslation('toast:api.server_error_description'),
        color: 'danger'
      })
    } else {
      // 对于其他错误（包括400），直接显示后端的错误消息
      addToast({
        title: this.getTranslation('toast:api.request_failed'),
        description: errorMessage,
        color: 'danger'
      })
    }
  }

  // 通用请求方法
  async request<T>(endpoint: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    // 默认请求配置
    const defaultOptions: RequestOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include' // 自动携带cookies，与原client保持一致
    }

    // 合并选项
    const mergedOptions: RequestOptions = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    }

    // 添加认证头
    const token = getAccessToken()
    if (token) {
      mergedOptions.headers!['Authorization'] = `Bearer ${token}`
    }

    // 添加语言头 - 与原client保持一致
    const currentLanguage = getCurrentLanguage()
    mergedOptions.headers!['Accept-Language'] = formatAcceptLanguage(currentLanguage)

    // 如果有body，序列化为JSON
    if (mergedOptions.body && typeof mergedOptions.body === 'object') {
      mergedOptions.body = JSON.stringify(mergedOptions.body)
    }

    try {
      console.log(`[APIv2请求] ${mergedOptions.method} ${API_URL}${endpoint}`)
      console.log('[APIv2请求配置]', mergedOptions)

      // 发送请求
      const response = await fetch(`${API_URL}${endpoint}`, {
        ...mergedOptions,
        credentials: mergedOptions.credentials || 'include'
      } as RequestInit)

      // 检查响应状态
      if (!response.ok) {
        let errorMessage = `API Error: ${response.status} ${response.statusText}`
        let errorCode: string | undefined
        let errorData: any

        // 尝试解析错误响应
        try {
          const errorResponse = await response.json()

          // 优先使用后端的错误消息
          if (errorResponse.error) {
            errorMessage = errorResponse.error
          } else if (errorResponse.message) {
            errorMessage = errorResponse.message
          }

          errorCode = errorResponse.code || errorResponse.errorCode
          errorData = errorResponse.data
          console.error('[APIv2错误详情]', JSON.stringify(errorResponse))
        } catch {
          // 解析错误响应失败，保持默认错误信息
          console.error('[APIv2错误] 无法解析错误响应')
        }

        // 显示错误提示
        this.showErrorToast(errorMessage, response.status, mergedOptions.silentError)
        throw new ApiError(errorMessage, response.status, errorCode, errorData)
      }

      // 检查响应内容类型
      const contentType = response.headers.get('content-type')
      if (contentType?.includes('application/json')) {
        const data = await response.json()
        console.log(`[APIv2响应] ${endpoint}:`, data)

        // 直接返回后端响应，不做格式转换
        return data as ApiResponse<T>
      }

      const textResponse = await response.text()
      console.log(`[APIv2响应文本] ${endpoint}:`, textResponse)
      return textResponse as unknown as ApiResponse<T>
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }

      console.error(`[APIv2请求失败] ${endpoint}:`, error)

      // 显示网络错误提示 - 与原client保持一致
      this.showErrorToast(
        error instanceof Error ? error.message : this.getTranslation('toast:api.unknown_error'),
        0,
        mergedOptions.silentError
      )
      // 处理其他错误（网络错误等）
      throw new ApiError(error instanceof Error ? error.message : 'Unknown API error', 0)
    }
  }

  // GET 请求
  async get<T>(
    endpoint: string,
    options: Omit<RequestOptions, 'method' | 'body'> = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' })
  }

  // POST 请求 - 与原client保持一致的参数命名
  async post<T>(
    endpoint: string,
    data?: unknown,
    options: Omit<RequestOptions, 'method' | 'body'> = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'POST', body: data })
  }

  // PUT 请求 - 与原client保持一致的参数命名
  async put<T>(
    endpoint: string,
    data?: unknown,
    options: Omit<RequestOptions, 'method' | 'body'> = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'PUT', body: data })
  }

  // DELETE 请求
  async delete<T>(
    endpoint: string,
    options: Omit<RequestOptions, 'method' | 'body'> = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' })
  }

  // PATCH 请求 - 与原client保持一致的参数命名
  async patch<T>(
    endpoint: string,
    data?: unknown,
    options: Omit<RequestOptions, 'method' | 'body'> = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'PATCH', body: data })
  }

  // 流式 POST 请求 - 返回 Response 对象用于处理流
  async streamPost(
    endpoint: string,
    data?: unknown,
    options: Omit<RequestOptions, 'method' | 'body'> & { signal?: AbortSignal } = {}
  ): Promise<Response> {
    // 默认请求配置
    const defaultOptions: RequestOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include'
    }

    // 合并选项
    const mergedOptions: RequestOptions = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    }

    // 添加认证头
    const token = getAccessToken()
    if (token) {
      mergedOptions.headers!['Authorization'] = `Bearer ${token}`
    }

    // 添加语言头
    const currentLanguage = getCurrentLanguage()
    mergedOptions.headers!['Accept-Language'] = formatAcceptLanguage(currentLanguage)

    // 🔧 修复：如果有data，序列化为JSON
    if (data && typeof data === 'object') {
      mergedOptions.body = JSON.stringify(data)
    }

    try {
      console.log(`[APIv2流式请求] ${mergedOptions.method} ${API_URL}${endpoint}`)
      console.log('[APIv2流式请求配置]', mergedOptions)

      // 发送请求并返回 Response 对象（不解析响应体）
      const fetchOptions: RequestInit = {
        method: mergedOptions.method,
        headers: mergedOptions.headers as HeadersInit,
        body: mergedOptions.body as BodyInit,
        credentials: mergedOptions.credentials,
        signal: options.signal
      }
      const response = await fetch(`${API_URL}${endpoint}`, fetchOptions)

      // 检查响应状态
      if (!response.ok) {
        let errorMessage = `API Error: ${response.status} ${response.statusText}`

        // 尝试解析错误响应
        try {
          const errorData = await response.clone().json()
          errorMessage = errorData.error || errorData.message || errorMessage
          console.error('[APIv2流式错误详情]', errorData)
        } catch {
          // 解析错误响应失败，保持默认错误信息
          console.error('[APIv2流式错误] 无法解析错误响应')
        }

        throw new ApiError(errorMessage, response.status)
      }

      console.log(`[APIv2流式响应] ${endpoint}: 流式响应已建立`)
      return response
    } catch (error) {
      console.error(`[APIv2流式错误] ${endpoint}:`, error)

      if (error instanceof ApiError) {
        throw error
      }

      // 处理其他错误（网络错误等）
      throw new ApiError(error instanceof Error ? error.message : 'Unknown API error', 0)
    }
  }
}

// 导出单例实例
export const apiClientV2 = new ApiClientV2()
