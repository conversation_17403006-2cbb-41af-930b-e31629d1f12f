import { apiClientV2, type ApiResponse } from './clientv2'

// 用户类型
export interface User {
  id: string
  email: string
  emailConfirmed: boolean
  dbUserId?: number
  createdAt?: string
  lastSignIn?: string
}

// 会话类型
export interface Session {
  user: User
  expires: string
}

// 登录请求类型
export interface LoginRequest {
  email: string
  password: string
}

// 注册请求类型
export interface RegisterRequest {
  email: string
  password: string
  name?: string
  inviteCode?: string
}

// 发送验证码请求类型
export interface SendCodeRequest {
  email: string
}

// 验证验证码请求类型
export interface VerifyCodeRequest {
  email: string
  code: string
  name?: string
  password?: string
  inviteCode?: string
}

// 登录验证码请求类型
export interface LoginCodeRequest {
  email: string
  code: string
}

// 会话数据类型（后端data中的内容）
export interface SessionData {
  access_token: string
  refresh_token: string
  expires_at: number
  user: User
}

// 后端响应中的数据类型定义
export interface LoginData {
  message: string
  session: SessionData
}

export interface RegisterData {
  message: string
  user?: User
}

export interface SendCodeData {
  message: string
}

export interface VerifyCodeData {
  message: string
  session: SessionData
}

export interface LoginCodeData {
  message: string
  session: SessionData
  requireEmailConfirm?: boolean
}

// 使用新的响应格式类型（后端统一格式）
export type LoginResponse = ApiResponse<LoginData>
export type RegisterResponse = ApiResponse<RegisterData>
export type SendCodeResponse = ApiResponse<SendCodeData>
export type VerifyCodeResponse = ApiResponse<VerifyCodeData>
export type LoginCodeResponse = ApiResponse<LoginCodeData>

// 认证状态
export type AuthStatus =
  | 'authenticated' // 已认证
  | 'unauthenticated' // 未认证
  | 'loading' // 加载中

// 认证API服务
export const authApi = {
  // 登录
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    return await apiClientV2.post<LoginData>('/api/app/auth/login', credentials, {
      silentError: true // 静默处理错误，由页面自己处理错误提示
    })
  },

  // 刷新令牌
  async refreshToken(refreshToken: string): Promise<LoginResponse> {
    return await apiClientV2.post<LoginData>('/api/app/auth/refresh', {
      refresh_token: refreshToken
    })
  },

  // 验证码登录
  async loginWithCode(data: LoginCodeRequest): Promise<LoginCodeResponse> {
    return await apiClientV2.post<LoginCodeData>('/api/app/auth/login-code', data, {
      silentError: true // 静默处理错误，由页面自己处理错误提示
    })
  },

  // 注册
  async register(data: RegisterRequest): Promise<RegisterResponse> {
    return await apiClientV2.post<RegisterData>('/api/app/auth/register', data)
  },

  // 发送验证码
  async sendCode(data: SendCodeRequest): Promise<SendCodeResponse> {
    return await apiClientV2.post<SendCodeData>('/api/app/auth/send-code', data)
  },

  // 发送登录验证码
  async sendLoginCode(data: SendCodeRequest): Promise<SendCodeResponse> {
    return await apiClientV2.post<SendCodeData>('/api/app/auth/send-login-code', data, {
      silentError: true // 静默处理错误，由页面自己处理错误提示
    })
  },

  // 验证验证码
  async verifyCode(data: VerifyCodeRequest): Promise<VerifyCodeResponse> {
    return await apiClientV2.post<VerifyCodeData>('/api/app/auth/verify-code', data)
  },

  // 注销
  async logout(): Promise<{ success: boolean }> {
    try {
      await apiClientV2.post('/api/app/auth/logout', {})
      return { success: true }
    } catch {
      // API调用失败，缓存清除由AuthProvider处理
      return { success: false }
    }
  },

  // 获取当前会话
  async getSession(): Promise<Session | null> {
    try {
      const response = await apiClientV2.get<{
        user?: User
        expires?: string
        tokenRefreshAt?: string
        message?: string
        session?: null
      }>('/api/app/auth/session')

      if (response.success && response.data) {
        // 如果session为null，表示用户未认证
        if (response.data.session === null) {
          return null
        }

        // 如果有用户信息，构造Session对象
        if (response.data.user && response.data.expires) {
          return {
            user: response.data.user,
            expires: response.data.expires
          }
        }
      }
      return null
    } catch {
      return null
    }
  }
}
