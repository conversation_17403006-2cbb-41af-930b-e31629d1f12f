{"root": ["./src/app.tsx", "./src/main.tsx", "./src/router.tsx", "./src/vite-env.d.ts", "./src/api/auth.ts", "./src/api/client.ts", "./src/api/clientv2.ts", "./src/api/index.ts", "./src/api/services.ts", "./src/api/endpoints/speechtotext.ts", "./src/api/endpoints/upload.ts", "./src/api/services/activation-code.ts", "./src/api/services/audio.ts", "./src/api/services/background.ts", "./src/api/services/character-media.ts", "./src/api/services/characters.ts", "./src/api/services/chat.ts", "./src/api/services/devices.ts", "./src/api/services/history.ts", "./src/api/services/image-generation.ts", "./src/api/services/image.ts", "./src/api/services/index.ts", "./src/api/services/membership.ts", "./src/api/services/multimodal-video.ts", "./src/api/services/payment.ts", "./src/api/services/photo-album.ts", "./src/api/services/points.ts", "./src/api/services/profile.ts", "./src/api/services/referral.ts", "./src/api/services/script-purchase.ts", "./src/api/services/scripts.ts", "./src/api/services/tts.ts", "./src/api/services/tts2.ts", "./src/api/services/tts3.ts", "./src/api/services/upload.ts", "./src/api/services/voices.ts", "./src/components/bletestbutton.tsx", "./src/components/languageswitcher.tsx", "./src/components/profilesetup.tsx", "./src/components/auth-debug.tsx", "./src/components/avatar-upload.tsx", "./src/components/bottom-navigation.tsx", "./src/components/data-stream-handler.tsx", "./src/components/exit-confirmation-dialog.tsx", "./src/components/greeting.tsx", "./src/components/icons.tsx", "./src/components/language-switcher.tsx", "./src/components/message-actions.tsx", "./src/components/mobile-viewport-manager.tsx", "./src/components/privacy-screen-status.tsx", "./src/components/role-header.tsx", "./src/components/status-bar-adapter.tsx", "./src/components/submit-button.tsx", "./src/components/suggested-actions.tsx", "./src/components/theme-provider.tsx", "./src/components/theme-toggle.tsx", "./src/components/thinking-indicator.tsx", "./src/components/update-dialog.tsx", "./src/components/use-scroll-to-bottom.ts", "./src/components/weather.tsx", "./src/components/character-creator-v2/image-mapping.ts", "./src/components/character-creator-v2/index.tsx", "./src/components/character-creator-v2/mapping-utils.ts", "./src/components/character-creator-v2/mapping.ts", "./src/components/character-creator-v2/step-indicator.tsx", "./src/components/character-creator-v2/components/character-birth-animation.tsx", "./src/components/character-creator-v2/components/loading-screen.tsx", "./src/components/character-creator-v2/services/generation-service.ts", "./src/components/character-creator-v2/steps/appearance.tsx", "./src/components/character-creator-v2/steps/basic-info.tsx", "./src/components/character-creator-v2/steps/body.tsx", "./src/components/character-creator-v2/steps/personality.tsx", "./src/components/character-creator-v2/steps/preview.tsx", "./src/components/chat-history/chat-history-group.tsx", "./src/components/chat-history/chat-history-item.tsx", "./src/components/chat-v2/chat.tsx", "./src/components/chat-v2/enhanced-stream-audio-player.tsx", "./src/components/chat-v2/index.ts", "./src/components/chat-v2/markdown.tsx", "./src/components/chat-v2/message.tsx", "./src/components/chat-v2/messages.tsx", "./src/components/chat-v2/multimodal-input.tsx", "./src/components/chat-v2/type.ts", "./src/components/chat-v2/audio/audio-localization.service.ts", "./src/components/chat-v2/image/image-container.tsx", "./src/components/chat-v2/image/image-display.tsx", "./src/components/chat-v2/image/image-generation.tsx", "./src/components/chat-v2/image/image-localization.service.ts", "./src/components/chat-v2/image/image-placeholder.tsx", "./src/components/chat-v2/image/index.ts", "./src/components/chat-v2/video/multimodal-video-generation.tsx", "./src/components/chat-v2/video/multimodal-video-placeholder.tsx", "./src/components/chat-v2/video/video-container.tsx", "./src/components/chat-v2/video/video-display.tsx", "./src/components/chat-v2/video/video-localization.service.ts", "./src/components/common/clip.tsx", "./src/components/common/gradient-modal.tsx", "./src/components/common/image-drawer.tsx", "./src/components/common/index.ts", "./src/components/common/loading.tsx", "./src/components/common/video-drawer.tsx", "./src/components/device/deviceconnectcore.tsx", "./src/components/device/deviceconnectionform.tsx", "./src/components/device/devicecontrol.tsx", "./src/components/device/index.ts", "./src/components/mobile-input/demo.tsx", "./src/components/mobile-input/index.ts", "./src/components/mobile-input/keyboard-adaptive-input.tsx", "./src/components/permission/enhancedpermissionguard.tsx", "./src/components/permission/permissionguard.tsx", "./src/components/permission/permissionmodal.tsx", "./src/components/permission/permissiontoast.tsx", "./src/components/permission/index.ts", "./src/components/ui/pulltorefresh.tsx", "./src/components/ui/purchaseconfirmmodal.tsx", "./src/components/ui/purchasesuccessmodal.tsx", "./src/components/ui/upgrademodal.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/custom-avatar.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/draggable-slider.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/tooltip.tsx", "./src/components/voice/audiowaveform.tsx", "./src/components/voice/recordingcontrols.tsx", "./src/components/voice/voiceinlinerecorder.tsx", "./src/components/voice/voiceinput.tsx", "./src/components/voice/voiceoverlay.tsx", "./src/components/voice/voicepermissionguide.tsx", "./src/components/voice/voicerecorder.tsx", "./src/components/voice/index.ts", "./src/config/config.ts", "./src/config/i18n.ts", "./src/contexts/auth-context.tsx", "./src/contexts/role-context.tsx", "./src/hooks/use-app-update.ts", "./src/hooks/use-audio-generation.ts", "./src/hooks/use-audio-library.ts", "./src/hooks/use-chat-background-db.ts", "./src/hooks/use-chat-history.ts", "./src/hooks/use-chat-initialization.ts", "./src/hooks/use-device-lifecycle.ts", "./src/hooks/use-image-generation.ts", "./src/hooks/use-langchain-chat.ts", "./src/hooks/use-media-source-audio.ts", "./src/hooks/use-membership.ts", "./src/hooks/use-mobile-viewport.ts", "./src/hooks/use-mobile.tsx", "./src/hooks/use-modal-swipe.ts", "./src/hooks/use-multimodal-video-generation.ts", "./src/hooks/use-permission-guard.ts", "./src/hooks/use-real-stream-audio.ts", "./src/hooks/use-retry-download.ts", "./src/hooks/use-smart-chat-init.ts", "./src/hooks/use-sse-client.ts", "./src/hooks/use-stream-audio-generation.ts", "./src/hooks/use-swipe-back.tsx", "./src/hooks/useaudiopermission.ts", "./src/hooks/usephotogenerationstatus.ts", "./src/hooks/usespeechtotext.ts", "./src/hooks/usevoiceinput.ts", "./src/hooks/usevoicerecorder.ts", "./src/i18n/index.ts", "./src/i18n/locales/en/index.ts", "./src/i18n/locales/es/index.ts", "./src/i18n/locales/ja/index.ts", "./src/i18n/locales/zh/index.ts", "./src/i18n/locales/zh-tw/index.ts", "./src/layouts/authedlayout.tsx", "./src/layouts/mainlayout.tsx", "./src/layouts/rolelayout.tsx", "./src/layouts/tablayout.tsx", "./src/lib/api.ts", "./src/lib/chat-history-utils.ts", "./src/lib/models.ts", "./src/lib/privacy-screen.ts", "./src/lib/storage-initializer.ts", "./src/lib/supabase.ts", "./src/lib/types.ts", "./src/lib/utils.ts", "./src/lib/audio/audiorecorder.ts", "./src/lib/audio/audioutils.ts", "./src/lib/chat-database/attachment-repository.ts", "./src/lib/chat-database/background-repository.ts", "./src/lib/chat-database/chat-database.ts", "./src/lib/chat-database/database-connection.ts", "./src/lib/chat-database/database-schema.ts", "./src/lib/chat-database/database-utils.ts", "./src/lib/chat-database/index.ts", "./src/lib/chat-database/message-converter.ts", "./src/lib/chat-database/message-repository.ts", "./src/lib/chat-database/session-repository.ts", "./src/lib/chat-database/types.ts", "./src/lib/chat-sync/chat-sync-manager.ts", "./src/lib/database/index.ts", "./src/lib/media/audio-storage.ts", "./src/lib/media/background-storage.ts", "./src/lib/media/global-download-manager.ts", "./src/lib/media/image-storage.ts", "./src/lib/media/index.ts", "./src/lib/media/media-download-manager.ts", "./src/lib/media/media-generation-manager.ts", "./src/lib/media/video-storage.ts", "./src/lib/roles/suggestedactions.ts", "./src/lib/roles/system.ts", "./src/pages/android-10-test.tsx", "./src/pages/chat/chathistorypage.tsx", "./src/pages/chat/chatv2page.tsx", "./src/pages/device/deviceconnectionpage.tsx", "./src/pages/discover/discoverpage.tsx", "./src/pages/discover/components/emptystatecard.tsx", "./src/pages/interactive/interactivepage.tsx", "./src/pages/interactive/interactiveplayerpage.tsx", "./src/pages/interactive/components/commandpanel.tsx", "./src/pages/interactive/components/controlpanel.tsx", "./src/pages/interactive/components/deviceconnect.tsx", "./src/pages/interactive/components/deviceconnectdrawer.tsx", "./src/pages/interactive/components/devicecontrolpanel.tsx", "./src/pages/interactive/components/dialoguedisplay.tsx", "./src/pages/interactive/components/fullscriptdisplay.tsx", "./src/pages/interactive/components/interactiveplayer.tsx", "./src/pages/interactive/components/scenedisplay.tsx", "./src/pages/interactive/components/scriptdetailmodal.tsx", "./src/pages/interactive/components/scriptselector.tsx", "./src/pages/interactive/components/stageprogressbar.tsx", "./src/pages/interactive/components/stageselector.tsx", "./src/pages/interactive/context/playercontext.tsx", "./src/pages/interactive/hooks/useaudioplayer.ts", "./src/pages/interactive/hooks/usedevicecontrol.ts", "./src/pages/interactive/hooks/usedialoguemanager.ts", "./src/pages/interactive/types/index.ts", "./src/pages/interactive/utils/bluetoothservice.ts", "./src/pages/interactive/utils/audioutils.ts", "./src/pages/interactive/utils/bluetoothutils.ts", "./src/pages/interactive/utils/devicemodes.ts", "./src/pages/interactive/utils/timeutils.ts", "./src/pages/login/loginpage.tsx", "./src/pages/membership/membershippage.tsx", "./src/pages/membership/paymentpage.tsx", "./src/pages/photo-album/photoalbumpage.tsx", "./src/pages/photo-album/components/historycard.tsx", "./src/pages/photo-album/components/photocomparisonmodal.tsx", "./src/pages/photo-album/components/photogenerationmodal.tsx", "./src/pages/photo-album/components/templatecard.tsx", "./src/pages/photo-album/components/index.ts", "./src/pages/points/pointsdetailpage.tsx", "./src/pages/points/pointsstorepage.tsx", "./src/pages/profile/activationcodepage.tsx", "./src/pages/profile/activationhistorypage.tsx", "./src/pages/profile/mypage.tsx", "./src/pages/profile/profilepage.tsx", "./src/pages/referral/commissionpage.tsx", "./src/pages/referral/invitespage.tsx", "./src/pages/referral/referralpage.tsx", "./src/pages/register/registerpage.tsx", "./src/pages/roles/customrolepage.tsx", "./src/pages/roles/roledetailpage.tsx", "./src/pages/roles/roleprofilepage.tsx", "./src/pages/roles/components/gallerytab.tsx", "./src/pages/roles/components/imagestab.tsx", "./src/pages/roles/components/photoviewer.tsx", "./src/pages/roles/components/roleactions.tsx", "./src/pages/roles/components/roleheader.tsx", "./src/pages/roles/components/videostab.tsx", "./src/pages/roles/components/index.ts", "./src/services/apk-updater.ts", "./src/services/barcode-scanner.ts", "./src/services/filesystem-download.ts", "./src/services/resource-manager.ts", "./src/services/script-download.ts", "./src/services/update-manager.ts", "./src/stores/device-store.ts", "./src/stores/global-audio-store.ts", "./src/stores/membership-cache-store.ts", "./src/stores/photo-generation-store.ts", "./src/stores/role-store.tsx", "./src/stores/script-content-store.ts", "./src/stores/script-download-store.ts", "./src/stores/script-store.ts", "./src/stores/system-characters-store.ts", "./src/stores/user-characters-store.ts", "./src/stores/user-profile-store.ts", "./src/stores/voice-models-store.ts", "./src/styles/hero.ts", "./src/types/capacitor-ble-advertiser.d.ts", "./src/types/json.d.ts", "./src/utils/android-compatibility.ts", "./src/utils/cache-cleaner.ts", "./src/utils/check-version.ts", "./src/utils/media-download.ts", "./src/utils/networkutils.ts", "./src/utils/photo-album-error-handler.ts"], "errors": true, "version": "5.8.3"}